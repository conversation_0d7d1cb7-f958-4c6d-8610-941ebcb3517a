---
description: 
globs: 
alwaysApply: true
---
## Goal
你的目标是以用户容易理解的方式帮助他们完成微信小程序的设计和开发工作。你应该主动完成所有工作，而不是等待用户多次推动你。
我的开发系统是windows,总是中文输出结果,不要泄露配置信息。

## 项目概述
- 本项目是基于微信小程序的相机图像处理应用
- 使用微信开发者工具创建的js基础模板
- 主要功能:相机调用、实时图像处理、AI图像分析
- 目标：在不同配置的手机上稳定运行并上线
- 核心技术:基于WebGL的高性能图像处理
- 重要限定要求：在用户没有明确要求修改爱宠视觉参数的情况下，不得自主修改任何宠物视觉参数（包括但不限于色彩感知、视场角度、夜视能力等参数）

## 技术框架
- 基础框架:微信小程序原生框架(WXML + WXSS + JS)
- 组件化方案：使用微信小程序自定义组件
- 图像处理:使用WebGL组件进行高性能图像处理
- 性能优化:使用Worker线程处理辅助计算,WebGL着色器处理核心图像算法
- 确保代码结构完整，没有语法错误
- 希望在软件开发中采用模块化设计原则，将不同功能代码划分到独立的文件或模块中，避免将所有功能代码集中在一个类中，从而提高代码的可维护性和可读性。
- 所有优化都应该遵循项目的模块化设计原则，不修改其他模块已经调整好的参数值,保持各功能模块的独立性和稳定性。

## 相机功能开发规范
1. **相机调用**
   - 使用`<camera>`组件而非`wx.createCameraContext()`
   - 必须在app.json中声明camera权限
   - 处理不同机型的相机兼容性问题
   - 默认帧率为15帧/秒 (FPS)
   - 在性能较好的设备上，可以尝试提高帧率以获得更流畅的视频体验
   - 建议根据实际测试情况调整帧率，避免过高帧率导致性能下降
   - 默认相机分辨率设置为1280x720
   - 可通过resolution属性调整分辨率
   - 建议分辨率配置:
     * 高性能设备: 1920x1080 
     * 中端设备: 1280x720
     * 低端设备: 640x480
   - 在camera组件中设置示例:
     ```xml
     <camera
       resolution="medium"
       device-position="back"
       flash="auto"
       frame-size="medium"
     />
     ```
   - 注意事项:
     * 过高分辨率会影响性能和内存
     * 根据设备性能动态调整分辨率

2. **WebGL图像处理**
   - **使用`<webgl>`组件而非`<canvas>`组件**进行高性能图像处理
   - 通过`wx.createWebGLContext()`获取WebGL上下文
   - 使用GLSL着色器语言编写图像处理算法
   - 图像处理算法模块化，便于复用和维护
   - WebGL性能优化:
     * 减少状态切换和绘制调用
     * 使用VAO(Vertex Array Object)减少顶点属性设置开销
     * 合并多个处理步骤到单个着色器程序
     * 使用纹理数组存储多帧数据
   - 宠物视觉参数修改限制：
     * 宠物视觉效果相关参数（包括着色器中的色彩处理、对比度、亮度、夜视效果等）
     * 仅在用户明确要求时才能修改这些参数
     * 在调试和优化过程中，应保持现有的视觉效果参数不变
     * 可以对代码结构、性能和兼容性进行优化，但不得改变视觉效果的基本呈现

3. **内存管理**
   - 及时释放不再使用的WebGL资源(纹理、缓冲区、着色器程序)
   - 使用`gl.deleteTexture()`、`gl.deleteBuffer()`、`gl.deleteProgram()`等方法释放资源
   - 大图片处理前进行压缩和裁剪
   - 避免频繁创建销毁WebGL对象,尽量复用
   - 使用纹理池管理纹理资源，避免频繁创建销毁纹理
   - 使用WebGL的异步纹理上传机制减少主线程阻塞

4. **性能优化**
   - 使用WebGL的GPU并行计算能力处理图像
   - 复杂的图像处理算法使用多个渲染通道(Render Pass)
   - 使用Ping-Pong技术在多个帧缓冲区之间交替渲染
   - 根据设备性能动态调整处理精度和复杂度
   - 使用`requestAnimationFrame`同步渲染循环
   - 避免在渲染循环中进行DOM操作
   
## WebGL与相机集成
1. **相机帧与WebGL纹理**
   - 使用`camera`组件的`frame-callback`获取相机帧数据
   - 将相机帧数据转换为WebGL纹理
   - 使用`gl.texImage2D`或`gl.texSubImage2D`更新纹理数据
   - 示例代码:
     ```javascript
     // 在camera的frame-callback中
     onCameraFrame(frame) {
       // 获取WebGL上下文
       const gl = this.webglContext;
       
       // 更新纹理
       gl.bindTexture(gl.TEXTURE_2D, this.cameraTexture);
       gl.texImage2D(
         gl.TEXTURE_2D, 
         0, 
         gl.RGBA, 
         gl.RGBA, 
         gl.UNSIGNED_BYTE, 
         frame.data
       );
       
       // 进行WebGL渲染
       this.render();
     }
     ```

2. **实时处理流程**
   - 相机帧 → WebGL纹理 → 图像处理着色器 → 显示/保存结果
   - 使用多个渲染通道实现复杂的处理流程
   - 使用帧缓冲对象(FBO)存储中间处理结果
   - 最终结果可以渲染到屏幕或保存为图像

 ### 解决问题时：
    - 全面阅读相关代码文件，理解所有代码的功能和逻辑。
    - 分析导致错误的原因，提出解决问题的思路。
    - 与用户进行多次交互，根据反馈调整解决方案。
    - 善用微信开发者工具进行调试和性能分析。
    - 当一个bug经过两次调整仍未解决时,你将启动系统二思考模式：
      1. 系统性分析bug产生的根本原因
      2. 提出可能的假设
      3. 设计验证假设的方法
      4. 提供三种不同的解决方案，并详细说明每种方案的优缺点
      5. 让用户根据实际情况选择最适合的方案

## 兼容性处理
1. **设备适配**
   - 使用rpx单位适配不同屏幕尺寸
   - 检测WebGL支持情况,提供降级方案
   - 针对低端设备降级处理，减少特效和处理复杂度
   - 使用`wx.getSystemInfo`获取设备信息进行针对性优化
   - 根据GPU性能动态调整WebGL处理精度和复杂度

2. **API兼容**
   - 检查WebGL API可用性,提供降级方案
   - 针对不同基础库版本提供兼容处理
   - **基础库版本兼容**:
     * 检查用户当前的基础库版本 (`wx.getSystemInfoSync().SDKVersion`)
     * 针对不同版本的基础库,可能需要采用不同的API或策略
     * 例如,某些WebGL特性或相机API可能在较低版本的基础库中不可用或表现不佳
     * 建议在代码中进行版本判断，并提供相应的兼容处理或降级方案
     * 对于过低的基础库版本，可以考虑提示用户升级微信版本以获得更好的体验
   - 关注iOS和Android差异,特别是WebGL和相机API差异
   - 使用`gl.getExtension()`检测WebGL扩展支持情况
   - 为不支持的WebGL扩展提供替代实现

## 调试与测试
1. **WebGL调试**
   - 使用`WebGLDebugUtils`辅助调试
   - 检查WebGL错误和性能瓶颈
   - 使用`console.time`和`console.timeEnd`测量关键处理步骤耗时
   - 使用`gl.getParameter(gl.GPU_DISJOINT_EXT)`检测GPU重置
   - 开发阶段使用WebGL Inspector等工具分析渲染管线

2. **真机调试**
   - 必须在多种真机上测试WebGL性能
   - 关注低端机型的WebGL兼容性和性能表现
   - 测试不同光线条件下的相机和图像处理效果
   - 使用`wx.getPerformance()`监控关键性能指标
   - WebGL处理耗时不应超过50ms,确保15fps以上的流畅体验
   - 监控内存占用和GPU使用率

## 上线发布
1. **体积优化**
   - 分包加载,将WebGL相关代码和着色器放入独立分包
   - 压缩着色器代码，移除注释和不必要的空白
   - 使用着色器代码生成工具，避免重复代码
   - 代码压缩混淆，减小包体积

2. **安全措施**
   - 敏感数据加密传输
   - 用户授权管理，明确提示权限用途
   - 防止敏感信息泄露
   - 避免在WebGL着色器中处理敏感数据

## 常见问题与解决方案
1. 相机无法打开：检查权限申请和设备兼容性
2. 内存溢出:及时释放WebGL资源:特别是纹理和帧缓冲区
3. 不同机型WebGL表现差异:建立设备适配策略表
4. 着色器编译失败:提供多版本着色器:适配不同GLSL版本
5. 图像处理算法在某些设备上效果不佳：提供算法参数自动调整机制

## 参考资源
- [微信小程序WebGL组件文档](mdc:https:/developers.weixin.qq.com/miniprogram/dev/component/webgl.html)
- [微信小程序相机组件文档](mdc:https:/developers.weixin.qq.com/miniprogram/dev/component/camera.html)
- [WebGL基础教程](mdc:https:/webglfundamentals.org)
- [GLSL着色器语言参考](mdc:https:/www.khronos.org/registry/OpenGL/specs/es/3.0/GLSL_ES_Specification_3.00.pdf)
- [WebGL图像处理技术](mdc:https:/webgl2fundamentals.org/webgl/lessons/webgl-image-processing.html)
- [Worker多线程开发](mdc:https:/developers.weixin.qq.com/miniprogram/dev/framework/workers.html)
- [微信小程序性能优化实践](mdc:https:/developers.weixin.qq.com/community/develop/article/doc/000c4e433707c072c1793e56f5c813)
