App({
  onLaunch: function() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)
    // 初始化主题
    const savedTheme = wx.getStorageSync('currentTheme') || 'theme1';
    this.globalData.currentTheme = savedTheme;
    
    // 初始化主题配置
    this.updateThemeConfig(savedTheme);
    
    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloud1-7g8ba3gkeaf4c69f', // 云环境ID
        traceUser: true, // 是否记录用户访问云环境的身份信息
      })
      
      // 云环境初始化后立即检查登录状态
      this.checkLoginStatus(true);
    }
    
    // 初始化主题回调数组
    this.themeChangeCallbacks = [];
    
    // 为了防止回调中出现无效函数，定期清理
    this._setupCallbackCleanupTimer();
  },
  
  // 设置回调清理定时器
  _setupCallbackCleanupTimer: function() {
    // 每30秒清理一次无效回调
    this._callbackCleanupTimer = setInterval(() => {
      this._cleanupInvalidCallbacks();
    }, 30000);
  },
  
  // 清理无效回调
  _cleanupInvalidCallbacks: function() {
    if (!this.themeChangeCallbacks || this.themeChangeCallbacks.length === 0) return;
    
    // 过滤掉非函数回调
    const validCallbacks = this.themeChangeCallbacks.filter(callback => 
      typeof callback === 'function'
    );
    
    // 如果有无效回调被移除，更新数组并记录日志
    if (validCallbacks.length !== this.themeChangeCallbacks.length) {
      const removedCount = this.themeChangeCallbacks.length - validCallbacks.length;
      console.log(`清理了 ${removedCount} 个无效的主题回调函数`);
      this.themeChangeCallbacks = validCallbacks;
    }
  },
  
  // 检查登录状态
  checkLoginStatus: function(isAutoLogin = false) {
    // 首先获取本地存储的用户信息
    const userInfo = wx.getStorageSync('userInfo');
    const userId = wx.getStorageSync('userId');
    
    if (userInfo && userId) {
      // 先更新全局数据
      this.globalData.userInfo = userInfo;
      this.globalData.userId = userId;
      this.globalData.isLoggedIn = true;
      
      // 然后调用云函数验证登录状态
      wx.cloud.callFunction({
        name: 'checkLogin',
        success: res => {
          console.log('应用启动时检查登录状态返回', res.result);
          
          if (res.result && res.result.isLoggedIn) {
            // 登录有效，更新最新用户信息
            const latestUserInfo = res.result.userInfo || {};
            
            this.globalData.userInfo = latestUserInfo;
            this.globalData.userId = res.result.userId;
            this.globalData.isLoggedIn = true;
            
            // 更新本地存储
            wx.setStorageSync('userInfo', latestUserInfo);
            wx.setStorageSync('userId', res.result.userId);
            
            console.log('用户登录状态有效，已自动登录');
          } else {
            // 登录已过期，需要自动重新登录
            console.log('登录已过期，尝试自动重新登录');
            if (isAutoLogin) {
              this.autoLogin();
            } else {
              // 清除过期数据
              this.globalData.userInfo = null;
              this.globalData.userId = '';
              this.globalData.isLoggedIn = false;
              
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('userId');
            }
          }
        },
        fail: err => {
          console.error('检查登录状态失败', err);
        }
      });
    } else if (isAutoLogin) {
      // 本地没有登录信息但需要自动登录
      this.autoLogin();
    }
  },
  
  // 自动登录功能
  autoLogin: function() {
    // 静默调用云函数登录，获取用户openid并自动获取已有用户信息
    wx.cloud.callFunction({
      name: 'login',
      data: {
        fetchUserInfo: true
      },
      success: res => {
        console.log('自动登录结果：', res.result);
        
        if (res.result && res.result.code === 0) {
          // 登录成功
          if (res.result.hasUserInfo && res.result.userInfo) {
            // 有用户信息
            const userInfo = res.result.userInfo;
            userInfo.hasUserDetail = true;
            
            // 更新全局数据
            this.globalData.userInfo = userInfo;
            this.globalData.userId = res.result.userId;
            this.globalData.isLoggedIn = true;
            
            // 更新本地存储
            wx.setStorageSync('userInfo', userInfo);
            wx.setStorageSync('userId', res.result.userId);
            
            console.log('自动登录成功并获取用户信息');
          } else {
            // 登录成功但没有完整用户信息
            console.log('自动登录成功，但需要用户完善资料');
            
            // 更新用户ID
            this.globalData.userId = res.result.userId;
            wx.setStorageSync('userId', res.result.userId);
          }
        } else {
          console.log('自动登录失败，用户可能需要手动登录');
        }
      },
      fail: err => {
        console.error('自动登录失败', err);
      }
    });
  },
  
  // 更新主题配置
  updateThemeConfig: function(theme) {
    // 主题配置
    const themes = {
      theme1: {
        darkBg: '#4C5D99',
        cardBg: 'rgba(85, 105, 155, 0.85)',
        gradient: 'linear-gradient(145deg, #4C5D99, #6275B3)',
        textColor: '#ffffff'
      },
      theme2: {
        darkBg: '#4A7A8C',
        cardBg: 'rgba(80, 135, 145, 0.85)',
        gradient: 'linear-gradient(145deg, #4A7A8C, #5E9CAF)',
        textColor: '#ffffff'
      },
      theme4: {
        darkBg: '#3D6B96',
        cardBg: 'rgba(70, 115, 160, 0.85)',
        gradient: 'linear-gradient(145deg, #3D6B96, #5284B4)',
        textColor: '#ffffff'
      }
    };
    
    this.globalData.themeConfig = themes[theme] || themes.theme1;
  },
  
  // 切换主题
  switchTheme: function(theme) {
    console.log('切换主题为:', theme);
    
    // 更新全局主题
    this.globalData.currentTheme = theme;
    
    // 更新主题配置
    this.updateThemeConfig(theme);
    
    // 保存到本地存储
    wx.setStorageSync('currentTheme', theme);
    
    // 先确保回调数组干净
    this._cleanupInvalidCallbacks();
    
    // 打印当前注册的回调数量
    const callbackCount = this.themeChangeCallbacks ? this.themeChangeCallbacks.length : 0;
    console.log(`准备通知 ${callbackCount} 个页面更新主题`);
    
    // 强制更新当前页面的导航栏颜色
    const themeConfig = this.globalData.themeConfig;
    let bgColor = '';
    switch(theme) {
      case 'theme1':
        bgColor = '#4C5D99'; // 天空蓝
        break;
      case 'theme2':
        bgColor = '#4A7A8C'; // 薄荷青
        break;
      case 'theme4':
        bgColor = '#3D6B96'; // 海洋蓝
        break;
      default:
        bgColor = '#4C5D99'; // 默认天空蓝
    }
    
    // 设置导航栏颜色
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: bgColor,
      fail: function(err) {
        console.error('设置导航栏颜色失败:', err);
      },
      success: function() {
        console.log('全局设置导航栏颜色成功:', bgColor);
      }
    });
    
    // 触发主题变化回调
    this.notifyThemeChange();
  },
  
  // 通知主题变化
  notifyThemeChange: function() {
    if (this.themeChangeCallbacks && this.themeChangeCallbacks.length > 0) {
      // 创建回调数组的副本进行遍历，避免回调中修改数组导致问题
      const callbacks = [...this.themeChangeCallbacks];
      
      for (let i = 0; i < callbacks.length; i++) {
        const callback = callbacks[i];
        if (typeof callback === 'function') {
          try {
            callback();
            console.log(`成功执行主题回调 ${i+1}/${callbacks.length}`);
          } catch (e) {
            console.error('执行主题变化回调出错:', e);
          }
        }
      }
    } else {
      console.log('没有注册的主题回调，无需通知');
    }
  },
  
  // 应用退出时清理资源
  onHide: function() {
    // 清除定时器
    if (this._callbackCleanupTimer) {
      clearInterval(this._callbackCleanupTimer);
      this._callbackCleanupTimer = null;
    }
  },
  
  globalData: {
    userInfo: null,
    userId: '',
    isLoggedIn: false,
    breedType: '',
    selectedBreed: null,
    currentTheme: 'theme1', // 默认主题（天空蓝）
    themeConfig: null
  }
})
