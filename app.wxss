/**app.wxss**/
page {
  /* 当前默认主题 */
  --primary-color: #FF8A5B;
  --secondary-color: #78C2AD;
  --tertiary-color: #64B5F6;
  --dark-bg: #3A4980;
  --card-bg: rgba(66, 82, 128, 0.85);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.85);
  --accent-color: #FFD166;
  --gradient-primary: linear-gradient(145deg, #3A4980, #4E5D8F);
  --glass-effect: rgba(255, 255, 255, 0.15);
  
  /* 主题1: 天空蓝 */
  --theme1-dark-bg: #4C5D99;
  --theme1-card-bg: rgba(85, 105, 155, 0.85);
  --theme1-gradient: linear-gradient(145deg, #4C5D99, #6275B3);
  --theme1-text-primary: #ffffff;
  --theme1-text-secondary: rgba(255, 255, 255, 0.85);
  
  /* 主题2: 薄荷青 */
  --theme2-dark-bg: #4A7A8C;
  --theme2-card-bg: rgba(80, 135, 145, 0.85);
  --theme2-gradient: linear-gradient(145deg, #4A7A8C, #5E9CAF);
  --theme2-text-primary: #ffffff;
  --theme2-text-secondary: rgba(255, 255, 255, 0.85);
  
  /* 主题4: 海洋蓝 */
  --theme4-dark-bg: #3D6B96;
  --theme4-card-bg: rgba(70, 115, 160, 0.85);
  --theme4-gradient: linear-gradient(145deg, #3D6B96, #5284B4);
  --theme4-text-primary: #ffffff;
  --theme4-text-secondary: rgba(255, 255, 255, 0.85);
  
  
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
  background-color: var(--dark-bg);
  color: var(--text-primary);
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: var(--dark-bg);
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(255, 138, 91, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 90% 80%, rgba(120, 194, 173, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 50% 50%, rgba(100, 181, 246, 0.08) 0%, transparent 50%);
}

.navbar {
  height: 100rpx;
  background: var(--card-bg);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  position: relative;
  border-bottom: 1px solid rgba(255, 138, 91, 0.15);
  border-radius: 16rpx 16rpx 0 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.navbar-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
  color: var(--primary-color);
  display: flex;
  align-items: center;
}

.navbar-title::before {
  content: "🐾";
  margin-right: 8rpx;
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0px); }
}

.back-btn {
  font-size: 40rpx;
  color: var(--primary-color);
  background: rgba(255, 138, 91, 0.1);
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.back-btn:active {
  transform: scale(0.95);
  background: rgba(255, 138, 91, 0.2);
}

.data-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  background: rgba(255, 138, 91, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  border: 1px solid rgba(255, 138, 91, 0.3);
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.data-indicator-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  margin-right: 10rpx;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.breed-card {
  background: var(--card-bg);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  border: 1px solid rgba(255, 138, 91, 0.15);
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.breed-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.breed-img {
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 138, 91, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 80rpx;
}

.breed-img::after {
  content: '';
  position: absolute;
  width: 140%;
  height: 140%;
  background: radial-gradient(circle, rgba(255, 138, 91, 0.15) 0%, transparent 70%);
  z-index: 0;
}

.breed-info {
  padding: 30rpx;
  flex: 1;
}

.breed-name {
  font-weight: bold;
  margin-bottom: 14rpx;
  color: var(--primary-color);
  font-size: 32rpx;
}

.breed-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.btn {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 138, 91, 0.3);
}

.btn:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 6rpx rgba(255, 138, 91, 0.2);
}

.btn-secondary {
  background: var(--secondary-color);
  box-shadow: 0 4rpx 12rpx rgba(120, 194, 173, 0.3);
}

.btn-tertiary {
  background: var(--tertiary-color);
  box-shadow: 0 4rpx 12rpx rgba(100, 181, 246, 0.3);
}

.icon-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
  transition: all 0.2s;
}

.icon-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.15);
} 