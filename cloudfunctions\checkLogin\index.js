// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-7g8ba3gkeaf4c69f'
})

const db = cloud.database()
const usersCollection = db.collection('users')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      isLoggedIn: false,
      message: '获取用户openid失败'
    }
  }
  
  try {
    // 查询用户是否存在
    const userRecord = await usersCollection.where({
      openid: openid
    }).get()
    
    if (userRecord.data.length === 0) {
      // 用户不存在
      return {
        isLoggedIn: false,
        message: '用户未登录'
      }
    } else {
      // 用户存在，返回用户信息
      const user = userRecord.data[0]
      
      // 更新最后访问时间
      await usersCollection.doc(user._id).update({
        data: {
          lastVisitTime: db.serverDate()
        }
      })
      
      // 检查用户是否已完善资料
      const hasUserDetail = user.hasUserDetail || !!(user.nickName && user.avatarUrl);
      
      // 如果数据库中hasUserDetail状态与实际不符，更新数据库
      if (user.hasUserDetail !== hasUserDetail && (user.nickName && user.avatarUrl)) {
        await usersCollection.doc(user._id).update({
          data: {
            hasUserDetail: true
          }
        });
      }
      
      return {
        isLoggedIn: true,
        userId: user._id,
        userInfo: {
          nickName: user.nickName || '',
          avatarUrl: user.avatarUrl || '',
          hasUserDetail: hasUserDetail,
          gender: user.gender,
          country: user.country,
          province: user.province,
          city: user.city
        }
      }
    }
  } catch (error) {
    console.error('检查登录状态失败', error)
    return {
      isLoggedIn: false,
      message: '检查登录状态失败: ' + error.message
    }
  }
}
