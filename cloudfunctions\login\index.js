// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-7g8ba3gkeaf4c69f'
})

const db = cloud.database()
const usersCollection = db.collection('users')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      code: 1,
      message: '获取用户openid失败'
    }
  }
  
  try {
    // 查询用户是否已存在
    const userRecord = await usersCollection.where({
      openid: openid
    }).get()
    
    let userId = ''
    let hasUserInfo = false
    let userInfo = null
    
    if (userRecord.data.length === 0) {
      // 用户不存在，创建新用户，不包含头像和昵称
      const result = await usersCollection.add({
        data: {
          openid: openid,
          nickName: '',
          avatarUrl: '',
          hasUserDetail: false,
          createTime: db.serverDate(),
          updateTime: db.serverDate(),
          lastLoginTime: db.serverDate()
        }
      })
      
      userId = result._id
    } else {
      // 用户已存在，更新登录时间
      const user = userRecord.data[0]
      userId = user._id
      
      // 检查用户是否已有完整信息
      if (user.nickName && user.avatarUrl) {
        hasUserInfo = true
        userInfo = {
          nickName: user.nickName,
          avatarUrl: user.avatarUrl,
          gender: user.gender,
          country: user.country,
          province: user.province,
          city: user.city
        }
      }
      
      // 更新登录时间
      await usersCollection.doc(userId).update({
        data: {
          lastLoginTime: db.serverDate()
        }
      })
    }
    
    // 返回结果，包含用户信息（如果有）
    const result = {
      code: 0,
      message: '登录成功',
      userId: userId,
      openid: openid,
      hasUserInfo: hasUserInfo
    }
    
    // 如果有用户信息且客户端请求获取用户信息，则返回
    if (hasUserInfo && userInfo && event.fetchUserInfo) {
      result.userInfo = userInfo
    }
    
    return result
  } catch (error) {
    console.error('登录失败', error)
    return {
      code: 2,
      message: '登录失败: ' + error.message
    }
  }
}
