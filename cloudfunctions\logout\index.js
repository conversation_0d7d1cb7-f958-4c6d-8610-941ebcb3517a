// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-7g8ba3gkeaf4c69f'
})

const db = cloud.database()
const usersCollection = db.collection('users')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      code: 1,
      message: '获取用户openid失败'
    }
  }
  
  try {
    // 查询用户是否存在
    const userRecord = await usersCollection.where({
      openid: openid
    }).get()
    
    if (userRecord.data.length === 0) {
      // 用户不存在
      return {
        code: 0,
        message: '用户未登录'
      }
    } else {
      // 用户存在，记录退出登录时间
      const user = userRecord.data[0]
      
      await usersCollection.doc(user._id).update({
        data: {
          lastLogoutTime: db.serverDate()
        }
      })
      
      return {
        code: 0,
        message: '退出登录成功'
      }
    }
  } catch (error) {
    console.error('退出登录失败', error)
    return {
      code: 2,
      message: '退出登录失败: ' + error.message
    }
  }
}
