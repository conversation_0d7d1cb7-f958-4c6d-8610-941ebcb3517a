// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-7g8ba3gkeaf4c69f'
})

const db = cloud.database()
const feedbackCollection = db.collection('feedback')
const usersCollection = db.collection('users')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  // 检查反馈内容
  if (!event.content || event.content.trim() === '') {
    return {
      code: 1,
      message: '反馈内容不能为空'
    }
  }
  
  try {
    // 查询用户信息
    let userInfo = null
    const userRecord = await usersCollection.where({
      openid: openid
    }).get()
    
    if (userRecord.data.length > 0) {
      userInfo = userRecord.data[0]
    }
    
    // 保存反馈
    const result = await feedbackCollection.add({
      data: {
        openid: openid,
        userId: userInfo ? userInfo._id : '',
        content: event.content,
        contactInfo: event.contactInfo || '',
        userInfo: userInfo ? {
          nickName: userInfo.nickName || '',
          avatarUrl: userInfo.avatarUrl || ''
        } : null,
        status: 'unread', // 未读状态
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    })
    
    return {
      code: 0,
      message: '提交反馈成功',
      feedbackId: result._id
    }
  } catch (error) {
    console.error('提交反馈失败', error)
    return {
      code: 2,
      message: '提交反馈失败: ' + error.message
    }
  }
} 