// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-7g8ba3gkeaf4c69f'
})

const db = cloud.database()
const usersCollection = db.collection('users')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      code: 1,
      message: '获取用户openid失败'
    }
  }
  
  // 检查必要参数
  if (!event.avatarUrl || !event.nickName) {
    return {
      code: 2,
      message: '头像或昵称不能为空'
    }
  }
  
  try {
    // 查询用户是否存在
    const userRecord = await usersCollection.where({
      openid: openid
    }).get()
    
    if (userRecord.data.length === 0) {
      return {
        code: 3,
        message: '用户不存在，请先登录'
      }
    }
    
    // 用户存在，更新用户信息
    const user = userRecord.data[0]
    const userId = user._id
    
    await usersCollection.doc(userId).update({
      data: {
        avatarUrl: event.avatarUrl,
        nickName: event.nickName,
        hasUserDetail: true,
        updateTime: db.serverDate()
      }
    })
    
    return {
      code: 0,
      message: '更新成功',
      userId: userId
    }
  } catch (error) {
    console.error('更新用户信息失败', error)
    return {
      code: 4,
      message: '更新用户信息失败: ' + error.message
    }
  }
}
