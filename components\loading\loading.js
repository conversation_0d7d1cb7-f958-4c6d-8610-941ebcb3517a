Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    message: {
      type: String,
      value: '加载中...'
    },
    maskClosable: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    animationData: {}
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      this.initAnimation();
    }
  },
  
  /**
   * 组件的方法列表
   */
  methods: {
    initAnimation: function() {
      const animation = wx.createAnimation({
        duration: 1000,
        timingFunction: 'linear',
        delay: 0
      });
      
      animation.rotate(360).step();
      
      this.setData({
        animationData: animation.export()
      });
      
      // 周期性执行动画
      setInterval(() => {
        animation.rotate(360).step();
        this.setData({
          animationData: animation.export()
        });
      }, 1000);
    },
    
    onMaskTap: function() {
      if (this.data.maskClosable) {
        this.setData({
          show: false
        });
        this.triggerEvent('close');
      }
    }
  }
}); 