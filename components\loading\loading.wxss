.loading-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.loading-content {
  position: relative;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-circle {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
}

.loading-text {
  font-size: 28rpx;
  color: #333333;
  margin-top: 20rpx;
} 