// 修复运动视觉参数初始化问题
// 此脚本将在toggleMotion函数中添加代码，确保运动视觉参数正确初始化

// 确保运动视觉参数在切换时正确初始化
function fixMotionVisionParameters() {
  const fs = require('fs');
  const path = require('path');
  
  // 读取视觉模拟页面的JS文件
  const filePath = path.join(__dirname, 'pages', 'vision-simulation', 'vision-simulation.js');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复犬科动物原始参数值设置错误
  content = content.replace(
    /'originalDogVisionParams\.motionSensitivity': visionConfig\.MOTION_VISION_CONFIG\.CAT\.MOTION_SENSITIVITY,/g,
    "'originalDogVisionParams.motionSensitivity': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY,"
  );
  
  content = content.replace(
    /'originalDogVisionParams\.motionThreshold': visionConfig\.MOTION_VISION_CONFIG\.CAT\.MOTION_THRESHOLD,/g,
    "'originalDogVisionParams.motionThreshold': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD,"
  );
  
  content = content.replace(
    /'originalDogVisionParams\.motionSizeThreshold': visionConfig\.MOTION_VISION_CONFIG\.CAT\.MOTION_SIZE_THRESHOLD/g,
    "'originalDogVisionParams.motionSizeThreshold': visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD"
  );
  
  // 写入修复后的文件
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('运动视觉参数初始化问题已修复');
}

fixMotionVisionParameters();
