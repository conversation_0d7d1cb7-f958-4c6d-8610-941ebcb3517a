# CAM硬件相机集成功能说明

## 🎯 功能概述

本版本在"爱宠看世界"小程序的视觉模拟页面中集成了外部硬件相机（CAM）连接功能，允许用户通过网络连接到远程硬件相机，实现远程图像流查看。

## 📱 使用方法

### 1. 启用CAM模式
- 在视觉模拟页面的iOS风格控制栏中，点击"📡 CAM"按钮
- 系统会自动尝试连接到硬件相机：`https://s1.v100.vip:33501/petvision/1`
- 连接成功后，按钮会显示为绿色并标注"CAM已连接"

### 2. 切换模式
- **手机相机模式**：使用设备自带摄像头，支持WebGL视觉处理
- **CAM硬件模式**：使用外部硬件相机的图片流，640x480分辨率

### 3. 状态指示
- **📡 连接中...**：正在尝试连接硬件相机
- **📡 CAM已连接**：硬件相机连接成功，图片流正常
- **❌ 连接失败**：连接出现问题，可重试或切回手机相机

## ⚙️ 技术特性

### 图片流显示
- **双缓冲机制**：确保图片切换平滑，无闪烁
- **实时刷新**：10fps更新频率（100ms间隔）
- **自动重试**：连接失败时自动重试，最多3次

### 网络优化
- **超时控制**：5秒超时保护
- **缓存避免**：每次请求添加时间戳参数
- **错误恢复**：连接中断自动重新尝试

### 用户体验
- **无缝切换**：手机相机和CAM相机之间平滑切换
- **状态提示**：清晰的连接状态和错误信息
- **触觉反馈**：操作时提供震动反馈

## 🚀 功能优势

### 1. 模块化设计
- CAM功能独立封装在`cam-manager.js`中
- 不影响原有手机相机功能
- 代码解耦，便于维护

### 2. 智能切换
- 自动检测连接状态
- 连接失败时自动回退到手机相机
- 状态同步，避免冲突

### 3. 性能优化
- CAM模式下跳过WebGL视觉处理，节省性能
- 双缓冲机制减少渲染压力
- 智能错误处理和重试机制 