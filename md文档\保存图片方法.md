// 添加一个调试提示
    console.log('开始保存爱宠视角图片', 
                '当前视图:', that.data.currentView,
                'WebGL已初始化:', !!that.gl,
                'Canvas尺寸:', that.canvas.width, 'x', that.canvas.height);

    // 1. 检查相册写入权限
    wx.getSetting({
      success(res) {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已经授权，直接保存
          that.saveCanvasToAlbum();
        } else {
          // 未授权或曾拒绝授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              // 用户同意授权
              that.saveCanvasToAlbum();
            },
            fail() {
              // 用户拒绝授权
              wx.showModal({
                title: '授权提示',
                content: '需要您授权保存图片到相册才能使用该功能，是否前往设置页面授权？',
                success(modalRes) {
                  if (modalRes.confirm) {
                    // 用户确认，打开设置页面
                    wx.openSetting({
                      // 如果授权成功，可以再次尝试保存，但这里简化处理，让用户下次点击按钮时再保存
                      // success(settingRes) {
                      //   if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      //     that.saveCanvasToAlbum();
                      //   } else {
                      //     wx.showToast({ title: '授权失败', icon: 'none' });
                      //   }
                      // }
                    });
                  } else {
                    // 用户取消
                    wx.showToast({
                      title: '授权失败，无法保存',
                      icon: 'none'
                    });
                  }
                }
              });
            }
          });
        }


// 内部方法：将 Canvas 保存到相册
  saveCanvasToAlbum: function() {
    const that = this;
    wx.showLoading({ title: '正在保存...', mask: true }); // 显示加载提示
    
    try {
      // 确保WebGL上下文和Canvas可用
      if (!that.gl || !that.canvas) {
        wx.hideLoading();
        wx.showToast({
          title: 'WebGL上下文不可用',
          icon: 'none'
        });
        return;
      }
      
      const gl = that.gl;
      const width = that.canvas.width;
      const height = that.canvas.height;
      
      // 获取当前帧
      let frameData = null;
      if (that.rawFrame) {
        // 如果有缓存的原始帧，使用它重新渲染一次确保内容是最新的
        console.log('使用已缓存的原始帧重新渲染一次');
        // 尝试使用renderFrame方法，如果不存在则使用processFrameWebGL
        if (typeof that.renderFrame === 'function') {
          console.log('调用renderFrame重新渲染');
          that.renderFrame(that.rawFrame);
        } else {
          console.log('调用processFrameWebGL重新渲染');
          that.processFrameWebGL(that.rawFrame);
        }
        
        // 强制执行一次渲染刷新
        gl.finish();
      } else {
        console.warn('没有找到缓存的原始帧');
      }
      
      // 临时禁用深度测试和模板测试，确保能读取所有像素
      const depthTest = gl.getParameter(gl.DEPTH_TEST);
      if (depthTest) {
        gl.disable(gl.DEPTH_TEST);
      }
      
      // 创建一个临时的2D画布用于保存图片
      const tmpCanvas = wx.createOffscreenCanvas({
        type: '2d',
        width: width,
        height: height
      });
      const ctx = tmpCanvas.getContext('2d');
      
      // 从WebGL读取像素数据
      const pixels = new Uint8Array(width * height * 4);
      
      // 确保使用RGBA格式读取所有像素
      gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);
      
      // 输出调试信息，检查像素数据是否有效
      let nonZeroPixels = 0;
      for (let i = 0; i < pixels.length; i += 4) {
        if (pixels[i] > 0 || pixels[i+1] > 0 || pixels[i+2] > 0) {
          nonZeroPixels++;
        }
      }
      console.log(`读取的像素数据: 总像素: ${width * height}, 非黑色像素: ${nonZeroPixels}`);
      
      // 如果没有非黑色像素，尝试强制渲染
      if (nonZeroPixels === 0 && that.texture) {
        console.log('尝试强制渲染并再次读取像素');
        
        // 强制渲染一个全屏四边形
        gl.viewport(0, 0, width, height);
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        
        // 使用着色器程序
        gl.useProgram(that.program);
        
        // 设置顶点
        gl.bindBuffer(gl.ARRAY_BUFFER, that.buffers.position);
        gl.vertexAttribPointer(
          that.programInfo.attribLocations.vertexPosition,
          2, gl.FLOAT, false, 0, 0
        );
        gl.enableVertexAttribArray(that.programInfo.attribLocations.vertexPosition);
        
        // 设置纹理坐标
        gl.bindBuffer(gl.ARRAY_BUFFER, that.buffers.textureCoord);
        gl.vertexAttribPointer(
          that.programInfo.attribLocations.textureCoord,
          2, gl.FLOAT, false, 0, 0
        );
        gl.enableVertexAttribArray(that.programInfo.attribLocations.textureCoord);
        
        // 绑定纹理
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, that.texture);
        gl.uniform1i(that.programInfo.uniformLocations.sampler, 0);
        
        // 绘制
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
        gl.finish();
        
        // 再次读取像素
        gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);
        
        // 再次检查像素数据
        nonZeroPixels = 0;
        for (let i = 0; i < pixels.length; i += 4) {
          if (pixels[i] > 0 || pixels[i+1] > 0 || pixels[i+2] > 0) {
            nonZeroPixels++;
          }
        }
        console.log(`第二次读取的像素数据: 总像素: ${width * height}, 非黑色像素: ${nonZeroPixels}`);
        
        // 如果依然全黑，需要创建一个测试渐变图像
        if (nonZeroPixels === 0) {
          console.warn('WebGL渲染后依然无法获取有效像素，创建测试图像');
          
          // 创建一个颜色渐变图，确认保存流程正常
          for (let i = 0; i < pixels.length; i += 4) {
            // 创建渐变色
            const row = Math.floor((i/4) / width);
            const col = (i/4) % width;
            
            // 渐变色方案
            pixels[i] = Math.floor(255 * (col / width));         // R从左到右渐变
            pixels[i+1] = Math.floor(255 * (row / height));      // G从上到下渐变
            pixels[i+2] = Math.floor(255 * (1 - col / width));   // B从右到左渐变
            pixels[i+3] = 255;                                   // A固定不透明
          }
          
          // 添加白色文字指示这是测试图像
          const centerX = Math.floor(width / 2);
          const centerY = Math.floor(height / 2);
          const textWidth = Math.floor(width * 0.7);
          const textHeight = Math.floor(height * 0.1);
          
          // 在中心绘制白色矩形
          for (let y = centerY - textHeight; y < centerY + textHeight; y++) {
            for (let x = centerX - textWidth/2; x < centerX + textWidth/2; x++) {
              if (x >= 0 && x < width && y >= 0 && y < height) {
                const i = (y * width + x) * 4;
                pixels[i] = 255;    // R
                pixels[i+1] = 255;  // G
                pixels[i+2] = 255;  // B
                pixels[i+3] = 255;  // A
              }
            }
          }
          
          console.log('已创建测试图像');
        }
      }
      
      // 创建ImageData并上下翻转(WebGL坐标系Y轴是从下到上)
      const imageData = ctx.createImageData(width, height);
      const data = imageData.data;
      
      for (let row = 0; row < height; row++) {
        for (let col = 0; col < width; col++) {
          // 翻转Y坐标，因为WebGL和Canvas 2D的坐标系不同
          const sourceIndex = (col + (height - row - 1) * width) * 4;
          const targetIndex = (col + row * width) * 4;
          
          data[targetIndex] = pixels[sourceIndex];         // R
          data[targetIndex + 1] = pixels[sourceIndex + 1]; // G
          data[targetIndex + 2] = pixels[sourceIndex + 2]; // B
          data[targetIndex + 3] = 255; // 设置为完全不透明
        }
      }
      
      // 将ImageData绘制到临时画布
      ctx.putImageData(imageData, 0, 0);
      
      // 恢复WebGL状态
      if (depthTest) {
        gl.enable(gl.DEPTH_TEST);
      }
      
      // 将临时画布转换为临时文件
      const tempFilePath = tmpCanvas.toDataURL('image/png');
      
      // 将Data URL转换为临时文件
      const fs = wx.getFileSystemManager();
      const tempFileName = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.png`;
      
      // 写入文件系统
      fs.writeFile({
        filePath: tempFileName,
        data: tempFilePath.replace(/^data:image\/\w+;base64,/, ''),
        encoding: 'base64',
        success() {
          console.log('临时文件创建成功:', tempFileName);
          
          // 保存到相册
          wx.saveImageToPhotosAlbum({
            filePath: tempFileName,
            success() {
              // 删除临时文件
              fs.unlink({
                filePath: tempFileName,
                complete() {
                  wx.hideLoading();
                  wx.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                }
              });
            },
            fail(err) {
              // 删除临时文件
              fs.unlink({
                filePath: tempFileName,
                complete() {
                  wx.hideLoading();
                  console.error('保存图片到相册失败:', err);
                  wx.showToast({
                    title: '保存失败，请重试',
                    icon: 'none'
                  });
                }
              });
            }
          });
        },
        fail(err) {
          wx.hideLoading();
          console.error('创建临时文件失败:', err);
          wx.showToast({
            title: '创建临时文件失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('保存图片过程中出错:', error);
      wx.showToast({
        title: '保存图片出错',
        icon: 'none'
      });
    }
  },