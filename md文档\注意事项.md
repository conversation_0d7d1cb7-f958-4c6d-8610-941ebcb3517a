1.开发日志包含以下内容：
任务记录：完整记录了运动视觉模块删除的所有步骤、修改文件、遇到的问题及解决方案、测试验证和后续计划。
任务模板：在文档末尾提供了一个标准模板，你可以在后续任务中直接复制使用。
这种结构化的开发日志将帮助你：

清晰追踪每个开发任务的完成情况
记录决策过程和解决方案
方便团队成员了解项目进展
为后续开发提供参考
在后续开发中，你可以按照以下方式使用这个日志：

开始新任务时，复制模板并填写任务描述
开发过程中，随时记录关键步骤和决策
任务完成后，补充测试结果和后续计划
如果你需要对开发日志格式进行任何调整，或者想要添加其他类型的信息，随时告诉我，我可以帮你更新文档模板


2.保存当前上下文，我可以为您创建一个记忆，这样在未来的对话中可以更容易地恢复上下文。
我已经为您创建了一个包含当前项目上下文的记忆。这个记忆包含了：

项目概述（微信小程序相机图像处理应用）
技术框架（微信小程序原生框架、WebGL图像处理）
当前工作重点（vision-simulation.js中的detectMotionFast函数）
关键限制（不得自主修改宠物视觉参数）
当您在未来创建新的对话时，可以通过以下方式恢复上下文：

告诉我："我们正在开发微信小程序相机图像处理应用，请继续帮助我"
提供当前文件路径："我们正在处理vision-simulation.js文件"
指明具体功能："我们在处理detectMotionFast函数"
这样我就能够快速恢复上下文，继续为您提供帮助。如果您需要查看当前正在处理的代码，也可以请我查看相关文件内容。

3.少 Cascade 错误的方法
3.1. 优化工具调用方式
减少连续多次工具调用：尽量在一次对话中减少连续多次调用同一工具
合并查询请求：如果需要查看多个相关文件或代码段，尽量一次性请求更多内容
使用更精确的查询：使用更精确的搜索词，避免返回过多不相关结果
3.2. 调整交互策略
分步骤进行复杂任务：将复杂任务分解为多个简单步骤，每步完成后再进行下一步
提供更清晰的指令：给我提供更明确、具体的指令，减少我需要进行的推断
避免过长的对话：定期开始新的对话，避免单个对话变得过长
3.3 使用更高效的工作流
先规划后执行：在开始编码前，先讨论和规划解决方案
使用代码片段：对于复杂的代码修改，可以先讨论关键部分而不是整个文件
利用记忆功能：我可以创建记忆来保存重要信息，减少重复查询
3.4. 文件处理建议
处理较小的文件：如果可能，将大文件拆分为较小的模块
使用更有组织的代码结构：清晰的代码结构有助于减少查询次数
提前共享关键信息：提前告诉我项目的关键部分，减少我需要探索的内容
这些方法应该能帮助减少 Cascade 系统中出现的 "Unknown: No credits consumed by this error" 错误。如果您有其他关于使用 Cascade 的问题，我很乐意提供更多帮助。

4.不要所有代码都累计到一个页面类来做,之前vision-simulation.js就是一个惨痛的代价；
在更新函数时遇到的问题是生成的内容超出了最大令牌限制。这是因为我尝试替换整个文件内容，而不是只针对特定函数进行更新

5.代码分模块进行解耦，依赖度相互解耦。

6.明确的模式层级关系，请再仔细确认
二色视觉模式：只启用二色视觉，禁用视力辨析、夜视和运动视觉
视力辨析度模式：启用二色视觉和视力辨析度，禁用夜视和运动视觉
夜视能力模式：启用二色视觉、视力辨析度和夜视能力，禁用运动视觉
运动视觉模式：启用所有视觉效果

7.不要修改其它模块的功能代码和业务流程，保证语法正确;
  所需改的需求，界面和交互能够适应各种不同尺寸的手机屏幕;
  vision-simulation.js文件代码行太多了，不好维护，建议分到其它文件完成需求;
  暂时不考虑界面横屏的需求

