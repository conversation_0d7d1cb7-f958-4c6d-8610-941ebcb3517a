# 相机选择器功能测试清单

## 基础功能测试

### 1. 界面显示测试
- [ ] 页面加载后，相机按钮显示正确的默认图标（📷）和文字（后置相机）
- [ ] 点击相机按钮能正确打开选择器面板
- [ ] 选择器面板显示三个选项：后置相机、前置相机、硬件相机
- [ ] 当前选择的选项有正确的高亮显示（✓标记）
- [ ] 点击面板外部或关闭按钮能正确关闭选择器

### 2. 手机相机切换测试
- [ ] 选择"后置相机"能正确切换到手机后置摄像头
- [ ] 选择"前置相机"能正确切换到手机前置摄像头
- [ ] 切换后按钮图标和文字正确更新
- [ ] 手机相机模式下视觉处理功能正常（二色视觉、夜视等）
- [ ] 切换过程中没有资源泄漏或错误

### 3. 硬件相机切换测试
- [ ] 选择"硬件相机"能正确启动CAM连接流程
- [ ] CAM连接成功时，按钮显示正确图标（📡）和文字（硬件相机）
- [ ] CAM模式下显示"硬件相机模式"提示信息
- [ ] CAM模式下不进行视觉处理，显示原始图像
- [ ] CAM连接失败时自动回退到手机相机，并显示错误提示

## 高级功能测试

### 4. 状态同步测试
- [ ] 页面重新加载后，相机类型状态正确恢复
- [ ] 在CAM模式下切换视角，硬件相机模式提示正确显示/隐藏
- [ ] 各种相机状态变化时，`currentCameraType`数据正确更新

### 5. 错误处理测试
- [ ] CAM设备异常时，错误提示正确显示
- [ ] 网络连接问题时，自动回退机制正常工作
- [ ] 权限拒绝时，错误处理流程正确执行
- [ ] 资源冲突时，系统能正确恢复

### 6. 用户体验测试
- [ ] 触觉反馈正常工作（设备支持时）
- [ ] 动画效果流畅，无卡顿
- [ ] 选择器面板在不同屏幕尺寸下显示正常
- [ ] 浅色主题下样式正确显示
- [ ] 操作反馈及时，用户了解当前状态

## 兼容性测试

### 7. 向后兼容测试
- [ ] 原有的`toggleCamMode()`调用能正确打开选择器
- [ ] 所有CAM管理功能保持不变
- [ ] 手机相机的所有视觉处理功能未受影响
- [ ] 原有的快捷键或外部调用仍然有效

### 8. 边界情况测试
- [ ] 快速连续点击相机按钮不会导致状态混乱
- [ ] 在相机切换过程中离开页面，资源正确释放
- [ ] 系统内存不足时，相机切换能优雅处理
- [ ] 同时有多个相机操作请求时，处理逻辑正确

## 性能测试

### 9. 资源管理测试
- [ ] 相机切换时旧资源被正确释放
- [ ] 长时间使用后内存占用稳定
- [ ] CPU使用率在合理范围内
- [ ] 电池消耗无异常增加

### 10. 稳定性测试
- [ ] 连续多次切换相机，系统稳定
- [ ] 长时间运行CAM模式，无内存泄漏
- [ ] 网络不稳定环境下，功能降级正常
- [ ] 异常情况下的恢复机制有效

## 测试结果记录

### 通过的测试项
- [ ] 基础界面显示
- [ ] 手机相机切换
- [ ] 硬件相机切换
- [ ] 状态同步
- [ ] 错误处理
- [ ] 用户体验
- [ ] 向后兼容
- [ ] 边界情况
- [ ] 资源管理
- [ ] 稳定性测试

### 发现的问题
1. 
2. 
3. 

### 待优化项目
1. 
2. 
3. 

## 测试环境

- **设备型号**：
- **系统版本**：
- **微信版本**：
- **小程序基础库版本**：
- **测试时间**：
- **测试人员**： 