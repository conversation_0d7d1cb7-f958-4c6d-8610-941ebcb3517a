1、解决ios控制栏和知识库重叠的问题
1）主要问题：在华为Mate30手机上，IO控制栏和展开的知识库会重叠。
修复方案：
将知识库展开时的IO控制栏位置从底部(bottom: 0)调整为底部加上知识库高度(bottom: 70vh)，确保在知识库展开时控制栏会上移到知识库上方。
针对不同屏幕尺寸的手机添加了媒体查询规则：
低于700px高度的小屏幕手机
701px-799px之间的中等屏幕手机(如华为Mate30)
大于800px的大屏幕手机(如iPhone 14 Plus)
对横屏模式进行了特殊处理，在知识库展开时将控制栏移到左侧(right: 35vw)，避免与右侧的知识库重叠。
这些修改符合提供的注意事项，没有修改业务流程，只是调整了UI的适配性，使界面和交互能够适应不同尺寸的手机屏幕。现在在华为Mate30和iPhone 14 Plus等各种尺寸的手机上，IO控制栏与知识库展开不会再重叠了。

2）这些修改应该能解决华为Mate30手机上IO控制栏和知识库重叠的问题。我做了以下几个关键修改：
将控制栏从position: absolute改为position: fixed，确保它始终固定在视口中的正确位置。
默认情况下，将控制栏位置设置为距离底部130rpx，而不是直接贴在底部，避免与知识库重叠。
知识库展开时，控制栏的位置设置为bottom: 72vh，确保它始终位于展开后的知识库上方。
对UI组件的层级进行了系统性调整：
控制栏(ios-control-bar)的z-index从1001提高到2000
知识库(analysis-container)的z-index保持为100
视觉参数面板的z-index提高到2100
分辨率选择器和视觉模式选择器的z-index提高到2200
对不同尺寸的屏幕进行了适配，包括小屏幕、中屏幕(如华为Mate30)和大屏幕，以及横屏模式。
这些修改应该确保参数按钮和控制栏在华为Mate30手机上不会被展开的知识库遮挡。

2.华为Mate 30的屏幕参数如下：
屏幕尺寸：6.62英寸
分辨率：1080 x 2340像素（px）
设备像素比（DPR）：3.0
像素密度：409 PPI
屏幕比例：19.5:9
逻辑尺寸（dp）：360 x 780

3.强光适应提示（class="motion-vision-tip" 和 "motion-tip-content"）- 显示强光模式相关信息
  夜视模式提示（class="motion-vision-tip" 和 "motion-tip-content night-tip"）- 显示夜视能力相关信息

4.第一个提示"切换到人类视角"是通过 wx.showToast 实现的，这是微信小程序的一个内置组件，名称是 "Toast提示"（吐司提示）。在代码中没有特定的自定义名称。
第二个提示"犬科水平视角约240°，比人类的180°更广"是在 wxml 中定义的 visual-angle-tip（视角提示），它的确切类名是 "visual-angle-tip"，内部文本内容放在 "visual-angle-info" 中。
所以这两个提示分别叫：
Toast提示（微信小程序内置组件）
视角提示（visual-angle-tip）

5. 在 WXML 文件中，我们移除了所有的提示框元素：
强光适应提示框（motion-vision-tip 类，显示"汪星人的瞳孔正在缩小以适应强光"等文本）
夜视模式提示框（motion-vision-tip 类，显示"汪星人的夜视能力开启，可以看清暗处的物体"等文本）
运动视觉提示框（motion-vision-tip 类，显示"汪星人的运动视觉已开启，善于捕捉移动目标"等文本）
右上角的状态指示条 (light-status 类)

"汪星人的瞳孔正在缩小以适应强光"
"汪星人的夜视能力开启，可以看清暗处的物体"
猫咪的夜视能力极强，可以看清暗处的物体！
喵星人的瞳孔迅速缩小以保护视网膜，强光环境下视觉敏感度降低
"喵星人的夜视能力开启，可以清晰看到昏暗环境中的物体",

    const defaultTips = {
      dog: {
        nightVision: "汪星人的光感模式开启，可以相对看清暗处的物体",
        brightLight: "汪星人的瞳孔正在缩小以适应强光"
      },
      cat: {
        nightVision: "喵星人的光感模式开启，可以清晰看到昏暗环境中的物体",
        brightLight: "喵星人的瞳孔迅速缩小以保护视网膜，强光环境下视觉敏感度降低"
      }
