# 相机切换资源管理说明

## 📋 概述

当在手机相机和硬件相机之间切换时，系统会进行完整的资源管理，确保内存和性能的最优化。

## 🔄 切换到硬件相机时的资源管理

### 1. **手机相机资源释放**
- ✅ 停止相机上下文和帧监听器
- ✅ 清除全局相机引用（`this.cameraCtx`, `cameraCtx`）
- ✅ 停止FPS计数器
- ✅ 清除所有相机相关定时器
- ✅ 重置相机状态标记

### 2. **WebGL资源临时清理**
- ✅ 解绑当前纹理，清空画布
- ✅ 清除帧数据引用
- ✅ 重置第一帧标记
- ✅ 保留WebGL上下文供恢复使用

### 3. **内存清理**
- ✅ 清理大对象引用
- ✅ 撤销blob URL释放图片内存
- ✅ 强制垃圾回收（调试环境）
- ✅ 清除图片缓存数据

### 4. **状态重置**
- ✅ 重置手机相机UI状态
- ✅ 清除错误信息和加载状态
- ✅ 重置FPS显示

## 🔄 切换回手机相机时的资源管理

### 1. **CAM资源释放**
- ✅ 断开CAM连接，停止图片流
- ✅ 清除CAM定时器
- ✅ 撤销CAM图片URL
- ✅ 重置CAM统计数据

### 2. **WebGL资源恢复**
- ✅ 重新绑定纹理
- ✅ 初始化空白纹理
- ✅ 重置渲染状态
- ✅ 恢复WebGL上下文

### 3. **手机相机恢复**
- ✅ 重新启动FPS计数器
- ✅ 延迟重新初始化相机
- ✅ 确保状态一致性检查

## 🛡️ 内存保护机制

### **自动内存监控**
```javascript
// 实时监控内存警告级别
_monitorMemoryUsage() {
  // 检测内存警告并自动清理
  if (memoryWarningLevel > 0) {
    this._forceMemoryCleanup();
  }
}
```

### **强制内存清理**
- 清理临时图片数据
- 撤销所有blob URL
- 清除大对象引用
- 触发垃圾回收

### **页面生命周期管理**
- ✅ **onLoad**: 初始化内存监控
- ✅ **onHide**: 执行内存清理
- ✅ **onUnload**: 完整资源释放

## 🎯 性能优化特性

### **智能延迟机制**
- CAM连接延迟：600ms（确保资源完全释放）
- 手机相机恢复延迟：1000ms（确保CAM资源完全清理）

### **错误容忍机制**
- 资源释放失败不影响切换流程
- 自动重试和故障恢复
- 详细的错误日志记录

### **内存泄漏防护**
- 自动URL撤销
- 定时器完全清理
- 引用置空处理
- 垃圾回收触发

## 📊 监控和调试

### **内存状态检查**
```javascript
// 查看当前内存状态
this._monitorMemoryUsage();
```

### **资源状态日志**
- 详细的资源释放日志
- 内存清理过程追踪
- 性能指标记录

## ✅ 验证清单

切换相机时系统会自动完成以下检查：

- [ ] 手机相机完全停止
- [ ] 所有定时器清除
- [ ] WebGL资源正确管理
- [ ] 内存引用清理
- [ ] 状态一致性确认
- [ ] 错误状态重置
- [ ] 性能计数器管理

## 🚀 使用建议

1. **正常使用**：直接点击CAM按钮切换，系统自动处理所有资源管理
2. **内存不足时**：系统会自动检测并执行额外的内存清理
3. **调试模式**：查看控制台日志了解详细的资源管理过程

这套资源管理系统确保了相机切换的稳定性和性能，避免了内存泄漏和资源冲突问题。 