1.保存下来的图片是342*391，而不是相机的分辨率，这跟webgl组件有关系。

2.视力辨析度在0.75以上变化不大，1的时候好像不是最清楚的；

3.正常白天情况下，犬猫眼中的画面亮度都比人类要暗，因为没那么多锥细胞，需要调整。

4.添加了一个非线性映射函数，将用户设置的辨析度值(0.2-1.0)转换为更广泛的模糊范围：
0.9-1.0：接近人类视力，几乎不模糊（映射到0.95）
0.7-0.9：高辨析度犬种（如边境牧羊犬），映射到0.7-0.9区间
0.5-0.7：中等辨析度犬种（如拉布拉多），映射到0.4-0.7区间
0.3-0.5：低辨析度犬种（如金毛寻回犬），映射到0.2-0.4区间
0.2-0.3：非常低辨析度（如某些短鼻犬种），映射到0.0-0.2区间
这样，金毛寻回犬的视力辨析度（约为人类的1/3）设置为0.3-0.4时，会映射到0.2-0.3的范围，产生更明显的模糊效果。

5.不同的分辨率的选择可以代码去掉，先记住保留。

