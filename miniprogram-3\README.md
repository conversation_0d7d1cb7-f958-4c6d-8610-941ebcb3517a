# 🐾 爱宠看世界 - 宠物实时画面小程序

## 项目简介
这是一个基于微信小程序的宠物实时画面系统，支持观看宠物的实时画面。

## 主要功能
- 🎥 **实时监控**: 通过摄像头实时观看宠物视角的图像
- 📷 **图片查看**: 支持静态图片和图片流查看  
- 🔧 **视频测试**: 多种视频格式播放测试
- 🌐 **智能网络**: 内网/外网自动切换

## 项目结构

### 根目录文件
- `app.js` - 小程序主入口，处理全局逻辑和用户登录
- `app.json` - 小程序配置，定义页面路由和窗口样式
- `app.wxss` - 全局样式文件
- `project.config.json` - 项目配置文件
- `sitemap.json` - 搜索索引配置
- `video-js-player.html` - Video.js播放器页面

### 页面目录 (pages/)

#### index/ (首页)
项目入口页面，提供简洁的导航界面
- `index.js` - 页面逻辑，处理用户信息和页面跳转
- `index.wxml` - 页面模板，显示主标题和功能入口按钮
- `index.wxss` - 页面样式
- `index.json` - 页面配置

#### cam-viewer/ (摄像头查看器) 
核心监控页面，实现宠物实时监控
- `cam-viewer.js` - 核心逻辑，包含双缓冲加载、智能重试、网络切换
- `cam-viewer.wxml` - 监控界面模板
- `cam-viewer.wxss` - 监控界面样式
- `cam-viewer.json` - 页面配置

**技术特点:**
- 双缓冲图片加载机制，减少闪烁
- 自适应刷新频率（2-10秒）
- 内网/外网智能切换
- 连续失败检测和恢复

#### image-viewer/ (图片查看器)
静态图片和图片流查看工具
- `image-viewer.js` - 图片查看逻辑，支持自动刷新和视频流模拟
- `image-viewer.wxml` - 图片查看界面
- `image-viewer.wxss` - 界面样式
- `image-viewer.json` - 页面配置

**功能特色:**
- 多种刷新间隔（1-10秒）
- 视频流模拟模式
- 图片保存到相册
- 网络连通性测试

#### logs/ (日志页面)
系统日志显示页面
- `logs.js` - 日志显示逻辑
- `logs.wxml` - 日志列表模板
- `logs.wxss` - 日志样式
- `logs.json` - 页面配置

### 工具目录 (utils/)
- `util.js` - 通用工具函数，包含时间格式化等功能

## 硬件配置

### 支持的摄像头
- ESP32-CAM模块
- 其他支持HTTP图片流的摄像头

### 默认地址配置
- **内网**: `http://*************/petvision/1`
- **外网**: `https://s1.v100.vip:33501/petvision/1`

## 接口说明
- `/petvision/1` - 视频流接口
- `/jpg` - 静态图片接口  


## 技术特色

### 智能网络处理
- 自动检测网络状态
- 内网/外网切换
- 连接失败自动重试
- 网络质量自适应

### 用户体验优化
- 双缓冲加载减少闪烁
- 静默重试减少干扰
- 详细错误诊断
- 一键保存功能

## 开发环境
- 微信小程序原生框架
- glass-easel组件框架
- ES6语法支持
- 代码压缩优化

## 使用方法
1. 使用微信开发者工具打开项目
2. 配置正确的AppID
3. 设置硬件摄像头IP地址
4. 在小程序后台配置业务域名
5. 编译运行

## 注意事项
- 需配置小程序业务域名
- 外网访问需HTTPS协议
- 建议先测试内网连接
- 确保摄像头支持跨域访问

## 版本信息

- **当前版本**: 1.0.0
- **小程序库版本**: trial
- **兼容性**: 支持微信小程序最新版本

---

 