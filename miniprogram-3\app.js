// app.js
App({
  onLaunch() {
    console.log('小程序启动')
    
    // 初始化全局错误处理
    this.initGlobalErrorHandler()
    
    // 安全的本地存储操作
    this.initLocalStorage()
    
    // 安全的用户登录
    this.initUserLogin()
  },

  // 初始化全局错误处理器
  initGlobalErrorHandler() {
    // 监听小程序错误
    wx.onError((error) => {
      console.error('全局错误捕获:', error)
      
      // 错误上报（可以发送到后台）
      this.reportError('global_error', error)
      
      // 用户友好提示
      wx.showToast({
        title: '应用遇到问题，正在恢复',
        icon: 'none',
        duration: 2000
      })
    })
    
    // 监听未处理的Promise异常
    wx.onUnhandledRejection((res) => {
      console.error('未处理的Promise异常:', res)
      this.reportError('unhandled_promise', res.reason)
    })
  },

  // 安全的本地存储初始化
  initLocalStorage() {
    try {
      // 展示本地存储能力
      const logs = wx.getStorageSync('logs') || []
      logs.unshift(Date.now())
      
      // 限制日志数量，防止存储过大
      const maxLogs = 100
      if (logs.length > maxLogs) {
        logs.splice(maxLogs)
      }
      
      wx.setStorageSync('logs', logs)
      console.log('本地存储初始化成功')
    } catch (error) {
      console.error('本地存储初始化失败:', error)
      // 清理可能损坏的存储
      try {
        wx.removeStorageSync('logs')
        wx.setStorageSync('logs', [Date.now()])
      } catch (cleanError) {
        console.error('存储清理失败:', cleanError)
      }
    }
  },

  // 安全的用户登录
  initUserLogin() {
    wx.login({
      success: res => {
        console.log('登录成功，获取code:', res.code)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        // 这里可以添加实际的后台请求逻辑
      },
      fail: error => {
        console.error('登录失败:', error)
        this.reportError('login_failed', error)
        
        // 用户友好提示
        wx.showModal({
          title: '登录失败',
          content: '微信登录失败，部分功能可能受限。是否重试？',
          showCancel: true,
          confirmText: '重试',
          cancelText: '跳过',
          success: (res) => {
            if (res.confirm) {
              // 延迟重试
              setTimeout(() => {
                this.initUserLogin()
              }, 2000)
            }
          }
        })
      },
      complete: () => {
        console.log('登录请求完成')
      }
    })
  },

  // 错误上报
  reportError(type, error) {
    const errorInfo = {
      type: type,
      error: error,
      timestamp: new Date().toISOString(),
      userAgent: wx.getSystemInfoSync()
    }
    
    console.log('错误上报:', errorInfo)
    
    // 这里可以添加实际的错误上报逻辑
    // 例如发送到后台服务器或第三方错误监控服务
  },

  // 全局数据
  globalData: {
    userInfo: null,
    // 添加应用状态管理
    appStatus: {
      isOnline: true,
      networkType: 'unknown',
      lastActiveTime: Date.now()
    }
  },

  // 应用显示时
  onShow() {
    console.log('应用进入前台')
    this.globalData.appStatus.lastActiveTime = Date.now()
    
    // 检查网络状态
    this.checkNetworkStatus()
  },

  // 应用隐藏时
  onHide() {
    console.log('应用进入后台')
    this.globalData.appStatus.lastActiveTime = Date.now()
  },

  // 检查网络状态
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.globalData.appStatus.networkType = res.networkType
        this.globalData.appStatus.isOnline = res.networkType !== 'none'
        console.log('网络状态更新:', res.networkType)
      },
      fail: (error) => {
        console.error('网络状态检查失败:', error)
        this.globalData.appStatus.isOnline = false
        this.reportError('network_check_failed', error)
      }
    })
  }
})
