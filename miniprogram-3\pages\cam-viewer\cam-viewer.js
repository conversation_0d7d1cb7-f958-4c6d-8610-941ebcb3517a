// 爱宠视界页面
Page({
  data: {
    // 视频流地址配置
    internalUrl: 'http://*************/petvision/1',  // 内网地址
    externalUrl: 'https://s1.v100.vip:33501/petvision/1',  // 外网地址
    currentImageUrl: '',  // 当前使用的图像流地址
    isUsingExternal: true,  // 是否使用外网地址
    
    // 界面状态
    imageStatus: '正在连接...',  // 图像状态
    errorMsg: '',  // 错误信息
    statusClass: 'connecting',  // 状态样式类
    
    // 重试机制
    retryCount: 0,
    maxRetryCount: 3,
    
    // 图像流刷新
    imageRefreshInterval: null,
    imageRefreshRate: 2000, // 改为2秒刷新一次，减少服务器压力
    loadingShown: false, // 跟踪loading状态
    
    // 调试信息
    showDebugInfo: false,
    imageWidth: 0,
    imageHeight: 0,
    
    // 图像加载监控
    lastImageLoadTime: 0, // 上次成功加载时间
    imageLoadTimeout: null, // 加载超时定时器
    consecutiveFailCount: 0, // 连续失败次数
    maxConsecutiveFails: 3, // 最大连续失败次数
    adaptiveRefreshRate: 2000, // 自适应刷新频率
    
    // 双缓冲系统
    currentBuffer: 'buffer1', // 当前显示的缓冲区
    nextImageUrl: '', // 下一张图片的URL
    lastSuccessTime: 0, // 最后成功加载的时间戳
    loadingNextImage: false, // 是否正在加载下一张图片
    silentFailTimeout: null, // 静默失败检测定时器
    
    // 新增：页面状态管理
    pageActive: true, // 页面是否处于活跃状态
    isPageDestroyed: false, // 页面是否已销毁
    memoryWarning: false, // 内存警告标志
    
    // 新增：边界检查相关
    maxImageSize: 5 * 1024 * 1024, // 最大图片大小 5MB
    loadFailureThreshold: 10, // 加载失败阈值
    totalLoadAttempts: 0, // 总加载尝试次数
    successfulLoads: 0 // 成功加载次数
  },

  onLoad: function (options) {
    console.log('爱宠视界页面加载')
    
    // 初始化页面状态
    this.setData({ 
      pageActive: true,
      isPageDestroyed: false 
    })
    
    // 设置默认使用外网地址
    this.setData({
      currentImageUrl: this.data.externalUrl
    })
    
    // 检查网络状态
    this.checkNetworkStatus()
    
    // 监听内存警告
    this.setupMemoryWarning()
    
    // 直接启动图像流模式
    this.startImageMode()
  },

  onReady: function () {
    console.log('爱宠视界页面准备就绪')
  },
  
  // 新增：页面显示时的处理
  onShow: function () {
    console.log('爱宠视界页面显示')
    
    if (this.data.isPageDestroyed) {
      console.log('页面已销毁，忽略onShow')
      return
    }
    
    this.setData({ pageActive: true })
    
    // 恢复图像刷新
    if (!this.data.imageRefreshInterval) {
      console.log('恢复图像刷新')
      this.startImageRefresh()
    }
    
    // 重新检查网络状态
    this.checkNetworkStatus()
  },
  
  // 新增：页面隐藏时的处理
  onHide: function () {
    console.log('爱宠视界页面隐藏')
    
    this.setData({ pageActive: false })
    
    // 暂停图像刷新以节省资源
    if (this.data.imageRefreshInterval) {
      console.log('暂停图像刷新')
      clearInterval(this.data.imageRefreshInterval)
      this.setData({ imageRefreshInterval: null })
    }
    
    // 清理timeout类型的定时器
    this.clearTimeouts()
  },

  onUnload: function () {
    console.log('爱宠视界页面卸载')
    
    // 标记页面已销毁
    this.setData({ isPageDestroyed: true })
    
    // 页面卸载时清理资源
    this.cleanupResources()
  },

  // 新增：设置内存警告监听
  setupMemoryWarning: function() {
    try {
      wx.onMemoryWarning(() => {
        console.warn('收到内存警告，开始清理资源')
        this.setData({ memoryWarning: true })
        
        // 降低刷新频率
        if (this.data.adaptiveRefreshRate < 5000) {
          this.setData({ adaptiveRefreshRate: 5000 })
          this.startImageRefresh()
        }
        
        // 清理不必要的定时器
        this.clearTimeouts()
        
        // 用户提示
        wx.showToast({
          title: '内存不足，已优化性能',
          icon: 'none',
          duration: 2000
        })
      })
    } catch (error) {
      console.error('内存警告监听设置失败:', error)
    }
  },

  // 新增：清理超时定时器
  clearTimeouts: function() {
    if (this.data.imageLoadTimeout) {
      clearTimeout(this.data.imageLoadTimeout)
      this.setData({ imageLoadTimeout: null })
    }
    if (this.data.silentFailTimeout) {
      clearTimeout(this.data.silentFailTimeout)
      this.setData({ silentFailTimeout: null })
    }
  },

  // 强化：清理资源
  cleanupResources: function() {
    console.log('开始清理资源')
    
    // 清理所有定时器
    if (this.data.imageRefreshInterval) {
      clearInterval(this.data.imageRefreshInterval)
      this.setData({ imageRefreshInterval: null })
    }
    
    this.clearTimeouts()
    
    // 清理loading状态
    if (this.data.loadingShown) {
      try {
        wx.hideLoading()
      } catch (error) {
        console.error('隐藏loading失败:', error)
      }
      this.setData({ loadingShown: false })
    }
    
    // 重置状态
    this.setData({
      loadingNextImage: false,
      pageActive: false
    })
    
    console.log('资源清理完成')
  },

  // 强化：检查网络状态
  checkNetworkStatus: function() {
    // 边界检查：页面是否还活跃
    if (this.data.isPageDestroyed) {
      console.log('页面已销毁，跳过网络检查')
      return
    }
    
    wx.getNetworkType({
      success: (res) => {
        // 再次检查页面状态
        if (this.data.isPageDestroyed) {
          return
        }
        
        console.log('当前网络类型:', res.networkType)
        
        // 更新全局网络状态
        const app = getApp()
        if (app && app.globalData) {
          app.globalData.appStatus.networkType = res.networkType
          app.globalData.appStatus.isOnline = res.networkType !== 'none'
        }
        
        if (res.networkType === 'none') {
          this.setData({
            errorMsg: '网络连接异常，请检查网络设置',
            imageStatus: '网络异常',
            statusClass: 'error'
          })
          wx.showToast({
            title: '网络连接异常',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        console.error('网络状态检查失败:', error)
        
        // 上报错误
        const app = getApp()
        if (app && app.reportError) {
          app.reportError('network_check_failed', error)
        }
        
        // 用户友好提示
        this.setData({
          errorMsg: '网络检查失败，请稍后重试',
          imageStatus: '网络检查失败',
          statusClass: 'error'
        })
      },
      complete: () => {
        console.log('网络状态检查完成')
      }
    })
  },

  // 启动图像流模式
  startImageMode: function() {
    console.log('启动图像流模式')
    
    // 边界检查：页面是否已销毁
    if (this.data.isPageDestroyed) {
      console.log('页面已销毁，跳过启动图像流')
      return
    }
    
    this.setData({
      imageStatus: '正在连接...',
      statusClass: 'connecting',
      errorMsg: '',
      retryCount: 0,
      showDebugInfo: false,
      imageWidth: 0,
      imageHeight: 0,
      // 重置双缓冲系统
      currentBuffer: 'buffer1',
      nextImageUrl: '',
      lastSuccessTime: 0,
      loadingNextImage: false,
      consecutiveFailCount: 0,
      // 重置统计数据
      totalLoadAttempts: 0,
      successfulLoads: 0
    })

    // 清理所有定时器
    this.cleanupResources()

    // 初始化第一张图片到buffer1
    this.refreshImageUrl()
    
    // 启动图像自动刷新
    this.startImageRefresh()
  },

  // 启动图像自动刷新
  startImageRefresh: function() {
    // 边界检查
    if (this.data.isPageDestroyed || !this.data.pageActive) {
      console.log('页面不活跃，跳过启动刷新')
      return
    }
    
    if (this.data.imageRefreshInterval) {
      clearInterval(this.data.imageRefreshInterval)
    }
    
    // 重置计数器
    this.setData({
      consecutiveFailCount: 0,
      adaptiveRefreshRate: this.data.imageRefreshRate
    })
    
    const interval = setInterval(() => {
      // 定时器内也要检查页面状态
      if (this.data.isPageDestroyed || !this.data.pageActive) {
        console.log('页面状态异常，停止图像刷新')
        clearInterval(interval)
        return
      }
      
      this.refreshImageWithMonitoring()
    }, this.data.adaptiveRefreshRate)
    
    this.setData({ imageRefreshInterval: interval })
  },

  // 带监控的图像刷新
  refreshImageWithMonitoring: function() {
    // 边界检查：检查页面状态和内存情况
    if (this.data.isPageDestroyed || !this.data.pageActive) {
      console.log('页面不活跃，跳过图像刷新')
      return
    }
    
    // 检查连续失败次数
    if (this.data.consecutiveFailCount > this.data.loadFailureThreshold) {
      console.warn('连续失败次数过多，暂停刷新')
      wx.showToast({
        title: '连接不稳定，已暂停刷新',
        icon: 'none'
      })
      return
    }
    
    // 使用双缓冲系统进行刷新
    this.refreshImageUrl()
  },

  // 强化：刷新图像URL（双缓冲实现）
  refreshImageUrl: function() {
    // 边界检查
    if (this.data.isPageDestroyed) {
      console.log('页面已销毁，跳过图像URL刷新')
      return
    }
    
    const baseUrl = this.data.isUsingExternal ? this.data.externalUrl : this.data.internalUrl
    
    // 验证基础URL
    if (!baseUrl || typeof baseUrl !== 'string') {
      console.error('无效的基础URL:', baseUrl)
      this.setData({
        errorMsg: '无效的视频流地址',
        imageStatus: '配置错误',
        statusClass: 'error'
      })
      return
    }
    
    // 添加更强的防缓存参数
    const timestamp = new Date().getTime()
    const random = Math.random().toString(36).substring(7)
    const newUrl = `${baseUrl}?t=${timestamp}&r=${random}&nocache=1`
    
    console.log('刷新图像URL:', {
      baseUrl: baseUrl,
      newUrl: newUrl,
      timestamp: timestamp,
      currentBuffer: this.data.currentBuffer,
      loadingNextImage: this.data.loadingNextImage,
      totalAttempts: this.data.totalLoadAttempts + 1
    })
    
    // 如果当前正在加载，跳过本次刷新
    if (this.data.loadingNextImage) {
      console.log('跳过刷新：正在加载中')
      return
    }
    
    // 增加加载尝试计数
    this.setData({ 
      loadingNextImage: true,
      totalLoadAttempts: this.data.totalLoadAttempts + 1
    })
    
    // 根据当前显示的缓冲区，更新另一个缓冲区的URL
    if (this.data.currentBuffer === 'buffer1') {
      this.setData({ nextImageUrl: newUrl })
    } else {
      this.setData({ currentImageUrl: newUrl })
    }
    
    // 启动静默失败检测（8秒超时）
    this.startSilentFailDetection()
  },

  // 启动静默失败检测
  startSilentFailDetection: function() {
    // 清除之前的检测定时器
    if (this.data.silentFailTimeout) {
      clearTimeout(this.data.silentFailTimeout)
    }
    
    const timeout = setTimeout(() => {
      this.handleSilentLoadFailure()
    }, 8000) // 8秒超时，比之前的5秒更宽松
    
    this.setData({ silentFailTimeout: timeout })
  },

  // 处理静默加载失败
  handleSilentLoadFailure: function() {
    console.warn('检测到静默加载失败')
    
    // 重置加载状态
    this.setData({ loadingNextImage: false })
    
    const newFailCount = this.data.consecutiveFailCount + 1
    console.log(`静默失败次数: ${newFailCount}/${this.data.maxConsecutiveFails}`)
    
    // 只更新失败计数，不显示错误状态给用户
    this.setData({ 
      consecutiveFailCount: newFailCount
    })
    
    // 如果连续失败次数过多，调整刷新频率
    if (newFailCount >= this.data.maxConsecutiveFails) {
      console.log('达到最大静默失败次数，调整刷新频率')
      this.handleConsecutiveFailures()
    } else {
      // 短暂延迟后继续尝试
      console.log('静默失败，1秒后重试')
      setTimeout(() => {
        this.refreshImageUrl()
      }, 1000)
    }
  },

  // 处理连续失败
  handleConsecutiveFailures: function() {
    // 降低刷新频率
    const newRate = Math.min(this.data.adaptiveRefreshRate * 1.5, 10000) // 最多10秒
    
    console.log(`连续失败${this.data.consecutiveFailCount}次，调整刷新频率从${this.data.adaptiveRefreshRate}ms到${newRate}ms`)
    
    this.setData({
      adaptiveRefreshRate: newRate
      // 不再显示错误状态，保持播放状态
    })
    
    // 重启刷新器
    this.startImageRefresh()
    
    // 只在控制台记录，用户界面保持清洁
    console.log('连接不稳定，已自动调整刷新频率')
  },

  // 切换网络（内网/外网）
  switchNetwork: function() {
    const newIsExternal = !this.data.isUsingExternal
    const newUrl = newIsExternal ? this.data.externalUrl : this.data.internalUrl
    
    console.log(`切换到${newIsExternal ? '外网' : '内网'}地址: ${newUrl}`)
    
    this.setData({
      isUsingExternal: newIsExternal,
      errorMsg: '',
      retryCount: 0
    })
    
    this.setData({
      imageStatus: '正在切换...',
      statusClass: 'connecting'
    })
    
    // 立即刷新图像URL
    this.refreshImageUrl()
    
    wx.showToast({
      title: `已切换到${newIsExternal ? '外网' : '内网'}`,
      icon: 'success'
    })
  },

  // === 图像事件处理 ===
  onImageLoad: function(e) {
    console.log('图像加载成功', e)
    
    // 边界检查：页面状态
    if (this.data.isPageDestroyed) {
      console.log('页面已销毁，忽略图像加载成功事件')
      return
    }
    
    // 获取是哪个缓冲区的图片加载成功
    const bufferType = e.currentTarget.dataset.buffer
    
    // 验证图像尺寸
    const imageWidth = e.detail.width || 0
    const imageHeight = e.detail.height || 0
    
    // 边界检查：图像尺寸
    if (imageWidth === 0 || imageHeight === 0) {
      console.warn('图像尺寸异常:', { width: imageWidth, height: imageHeight })
      wx.showToast({
        title: '图像尺寸异常，请检查摄像头',
        icon: 'none'
      })
      this.setData({ showDebugInfo: true })
    }
    
    // 边界检查：图像尺寸过大
    const estimatedSize = imageWidth * imageHeight * 3 // 粗略估算RGB图像大小
    if (estimatedSize > this.data.maxImageSize) {
      console.warn('图像尺寸过大:', estimatedSize, 'bytes')
      wx.showToast({
        title: '图像过大，可能影响性能',
        icon: 'none'
      })
    }
    
    console.log('图像详细信息:', {
      width: imageWidth,
      height: imageHeight,
      src: e.detail.src,
      bufferType: bufferType,
      currentBuffer: this.data.currentBuffer,
      consecutiveFailCount: this.data.consecutiveFailCount,
      currentRefreshRate: this.data.adaptiveRefreshRate,
      estimatedSize: estimatedSize
    })
    
    // 清除静默失败检测定时器
    if (this.data.silentFailTimeout) {
      clearTimeout(this.data.silentFailTimeout)
      this.setData({ silentFailTimeout: null })
    }
    
    // 清除加载超时定时器
    if (this.data.imageLoadTimeout) {
      clearTimeout(this.data.imageLoadTimeout)
      this.setData({ imageLoadTimeout: null })
    }
    
    // 记录成功加载时间
    const currentTime = Date.now()
    
    // 如果加载成功的是下一个缓冲区的图片，则切换显示
    const shouldSwitch = (this.data.currentBuffer === 'buffer1' && bufferType === 'buffer2') ||
                        (this.data.currentBuffer === 'buffer2' && bufferType === 'buffer1')
    
    if (shouldSwitch) {
      // 切换到新加载的缓冲区
      this.setData({ currentBuffer: bufferType })
      console.log(`切换显示缓冲区到: ${bufferType}`)
    }
    
    // 更新状态信息
    this.setData({
      imageStatus: '正在播放',
      statusClass: 'connected',
      errorMsg: '',
      retryCount: 0,
      imageWidth: imageWidth,
      imageHeight: imageHeight,
      lastImageLoadTime: currentTime,
      lastSuccessTime: currentTime,
      loadingNextImage: false, // 重置加载状态
      successfulLoads: this.data.successfulLoads + 1 // 增加成功计数
    })
    
    // 如果之前有连续失败，现在成功了，尝试恢复正常刷新频率
    if (this.data.consecutiveFailCount > 0) {
      this.recoverRefreshRate()
    }
    
    // 重置连续失败计数
    this.setData({ consecutiveFailCount: 0 })
    
    // 计算成功率并给出提示
    const successRate = (this.data.successfulLoads / this.data.totalLoadAttempts * 100).toFixed(1)
    console.log(`加载成功率: ${successRate}% (${this.data.successfulLoads}/${this.data.totalLoadAttempts})`)
    
    // 如果成功率过低，给出提示
    if (this.data.totalLoadAttempts > 20 && successRate < 60) {
      wx.showToast({
        title: `连接不稳定 (成功率${successRate}%)`,
        icon: 'none',
        duration: 2000
      })
    }
  },

  onImageError: function(e) {
    console.log('图像加载错误', e)
    
    // 边界检查：页面状态
    if (this.data.isPageDestroyed) {
      console.log('页面已销毁，忽略图像加载错误事件')
      return
    }
    
    console.log('错误详情:', e.detail)
    
    // 获取是哪个缓冲区的图片加载失败
    const bufferType = e.currentTarget.dataset.buffer
    console.log(`缓冲区${bufferType}加载失败`)
    
    // 重置加载状态
    this.setData({ loadingNextImage: false })
    
    // 清除相关定时器
    if (this.data.silentFailTimeout) {
      clearTimeout(this.data.silentFailTimeout)
      this.setData({ silentFailTimeout: null })
    }
    
    // 增加失败计数
    const newFailCount = this.data.consecutiveFailCount + 1
    console.log(`图像加载失败，连续失败次数: ${newFailCount}`)
    this.setData({ consecutiveFailCount: newFailCount })
    
    // 解析错误信息
    const errorMessage = this.getErrorMessage(e.detail.errMsg)
    
    // 只有在没有任何成功加载过的情况下才显示错误状态
    // 如果之前有成功加载，保持播放状态，避免频繁的错误提示
    if (this.data.lastSuccessTime === 0) {
      this.setData({
        imageStatus: '连接失败',
        statusClass: 'error',
        errorMsg: `图像加载失败: ${errorMessage}`,
        imageWidth: 0,
        imageHeight: 0
      })
      
      // 如果是外网失败，建议切换内网
      if (this.data.isUsingExternal && this.data.retryCount === 0) {
        wx.showToast({
          title: '外网连接失败，建议切换内网',
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      // 有成功记录，只在后台记录错误，保持界面清洁
      console.log('保持播放状态，后台记录错误:', errorMessage)
    }
    
    // 计算成功率
    const successRate = this.data.totalLoadAttempts > 0 ? 
      (this.data.successfulLoads / this.data.totalLoadAttempts * 100).toFixed(1) : 0
    console.log(`当前成功率: ${successRate}%`)
    
    // 如果失败次数过多，增加重试间隔
    const retryDelay = Math.min(1500 + (newFailCount * 500), 5000) // 最多5秒延迟
    
    // 短暂延迟后重试
    setTimeout(() => {
      // 再次检查页面状态
      if (!this.data.isPageDestroyed && this.data.pageActive) {
        this.refreshImageUrl()
      }
    }, retryDelay)
  },

  // 恢复正常刷新频率
  recoverRefreshRate: function() {
    // 如果当前频率高于正常频率，逐步恢复
    if (this.data.adaptiveRefreshRate > this.data.imageRefreshRate) {
      const newRate = Math.max(this.data.adaptiveRefreshRate * 0.8, this.data.imageRefreshRate)
      
      console.log(`图像加载恢复，调整刷新频率从${this.data.adaptiveRefreshRate}ms到${newRate}ms`)
      
      this.setData({ adaptiveRefreshRate: newRate })
      
      // 重启刷新器以应用新频率
      this.startImageRefresh()
      
      if (newRate === this.data.imageRefreshRate) {
        wx.showToast({
          title: '连接已恢复正常',
          icon: 'success',
          duration: 1500
        })
      }
    }
  },

  // 强化：解析错误信息
  getErrorMessage: function(errMsg) {
    const errorMap = {
      'MEDIA_ERR_NETWORK': '网络连接错误',
      'MEDIA_ERR_DECODE': '图像解码错误', 
      'MEDIA_ERR_SRC_NOT_SUPPORTED': '图像格式不支持',
      'MEDIA_ERR_ABORTED': '加载被中断',
      'ERR_PROXY_CONNECTION_FAILED': '代理连接失败',
      'ERR_INTERNET_DISCONNECTED': '网络连接已断开',
      'ERR_CONNECTION_REFUSED': '服务器拒绝连接',
      'ERR_TIMEOUT': '连接超时',
      'ERR_SSL_PROTOCOL_ERROR': 'SSL协议错误'
    }
    
    if (!errMsg) return '未知错误'
    
    // 查找匹配的错误类型
    for (let key in errorMap) {
      if (errMsg.toLowerCase().includes(key.toLowerCase())) {
        return errorMap[key]
      }
    }
    
    // 如果没有匹配的错误类型，返回更通用的描述
    if (errMsg.toLowerCase().includes('network')) {
      return '网络连接问题'
    } else if (errMsg.toLowerCase().includes('timeout')) {
      return '连接超时'
    } else if (errMsg.toLowerCase().includes('denied') || errMsg.toLowerCase().includes('refuse')) {
      return '访问被拒绝'
    }
    
    return '连接超时或服务器无响应'
  },

  // === 调试功能 ===
  
  // 切换调试信息显示
  toggleDebugInfo: function() {
    this.setData({
      showDebugInfo: !this.data.showDebugInfo
    })
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '🐾 爱宠视界',
      path: '/pages/cam-viewer/cam-viewer'
    }
  }
}) 