<!--爱宠视界页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🐾 爱宠看世界</text>
  </view>

  <!-- 图像显示区域 -->
  <view class="image-section">
    <view class="image-container">
      <!-- 双缓冲图像显示 -->
      <image 
        src="{{currentImageUrl}}"
        mode="aspectFit"
        class="image-stream {{currentBuffer === 'buffer1' ? 'active' : 'hidden'}}"
        bindload="onImageLoad"
        binderror="onImageError"
        show-menu-by-longpress="{{true}}"
        lazy-load="{{false}}"
        data-buffer="buffer1"
      ></image>
      
      <image 
        src="{{nextImageUrl}}"
        mode="aspectFit"
        class="image-stream {{currentBuffer === 'buffer2' ? 'active' : 'hidden'}}"
        bindload="onImageLoad"
        binderror="onImageError"
        show-menu-by-longpress="{{true}}"
        lazy-load="{{false}}"
        data-buffer="buffer2"
      ></image>
      
      <view class="image-overlay" wx:if="{{imageStatus !== '正在播放'}}">
        <text class="overlay-text">{{imageStatus}}</text>
      </view>
      
      <!-- 调试信息显示 -->
      <view class="debug-info" wx:if="{{showDebugInfo}}">
        <text class="debug-text">URL: {{currentBuffer === 'buffer1' ? currentImageUrl : nextImageUrl}}</text>
        <text class="debug-text">尺寸: {{imageWidth}} x {{imageHeight}}</text>
        <text class="debug-text">状态: {{imageStatus}}</text>
        <text class="debug-text">刷新频率: {{adaptiveRefreshRate}}ms</text>
        <text class="debug-text">连续失败: {{consecutiveFailCount}}/{{maxConsecutiveFails}}</text>
        <text class="debug-text">当前缓冲: {{currentBuffer}}</text>
        <text class="debug-text">最后加载: {{lastSuccessTime ? '成功' : '等待中'}}</text>
      </view>
    </view>
    
    <!-- 简化的控制按钮 -->
    <view class="image-controls">
      <button class="control-btn" bindtap="switchNetwork">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">{{isUsingExternal ? '切换内网' : '切换外网'}}</text>
      </button>
      <button class="control-btn" bindtap="toggleDebugInfo">
        <text class="btn-icon">🔍</text>
        <text class="btn-text">{{showDebugInfo ? '隐藏' : '显示'}}调试</text>
      </button>
    </view>
  </view>

  <!-- 状态信息 -->
  <view class="status-section">
    <view class="status-item">
      <text class="status-label">连接状态：</text>
      <text class="status-value {{statusClass}}">{{imageStatus}}</text>
    </view>
    <view class="status-item">
      <text class="status-label">当前网络：</text>
      <text class="status-value">{{isUsingExternal ? '外网' : '内网'}}</text>
    </view>
    <view class="status-item" wx:if="{{errorMsg}}">
      <text class="status-label">错误信息：</text>
      <text class="status-value error">{{errorMsg}}</text>
    </view>
    
    <!-- 智能建议 -->
    <view class="smart-suggestion" wx:if="{{statusClass === 'error'}}">
      <text class="suggestion-title">🤖 智能建议</text>
      <view class="suggestion-content">
        <text wx:if="{{errorMsg.includes('网络') || errorMsg.includes('连接')}}">网络连接问题，尝试切换网络地址或检查网络状况</text>
        <text wx:elif="{{consecutiveFailCount > 0}}">图像加载不稳定，可以尝试调整刷新频率</text>
        <text wx:else>连接异常，请检查ESP32-CAM设备状态</text>
      </view>
    </view>
  </view>

  <!-- 网络地址信息 -->
  <view class="network-info">
    <view class="info-title">📡 网络配置</view>
    <view class="address-item">
      <text class="address-label">内网地址：</text>
      <text class="address-value">{{internalUrl}}</text>
    </view>
    <view class="address-item">
      <text class="address-label">外网地址：</text>
      <text class="address-value">{{externalUrl}}</text>
    </view>
  </view>
</view> 