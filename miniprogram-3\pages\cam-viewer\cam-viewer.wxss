/* 爱宠视界页面样式 */

.container {
  padding: 20rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 图像显示区域 */
.image-section {
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  width: 100%;
  min-height: 300rpx;
  aspect-ratio: 16/9;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2rpx solid #dee2e6;
}

.image-stream {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-width: 200rpx;
  min-height: 150rpx;
  object-fit: contain;
  background: #f0f0f0;
  border: 1rpx solid #ddd;
  display: block;
  transition: opacity 0.3s ease-in-out;
}

.image-stream.active {
  opacity: 1;
  z-index: 2;
}

.image-stream.hidden {
  opacity: 0;
  z-index: 1;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  z-index: 2;
}

.overlay-text {
  text-align: center;
  padding: 20rpx;
}

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  z-index: 3;
  max-width: 400rpx;
}

.debug-text {
  display: block;
  margin-bottom: 5rpx;
  word-break: break-all;
}

.debug-text:last-child {
  margin-bottom: 0;
}

/* 图像控制按钮 */
.image-controls {
  padding: 15rpx 20rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
  justify-content: center;
  background: rgba(0, 0, 0, 0.05);
}

.control-btn {
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: translateY(1rpx);
  background: rgba(255, 255, 255, 0.7);
}

.btn-icon {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

.btn-text {
  font-size: 20rpx;
  text-align: center;
  line-height: 1.2;
}

/* 状态信息 */
.status-section {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  color: #666;
  min-width: 160rpx;
}

.status-value {
  flex: 1;
  font-weight: bold;
}

.status-value.connected {
  color: #4CAF50;
}

.status-value.connecting {
  color: #FF9800;
}

.status-value.disconnected {
  color: #9E9E9E;
}

.status-value.paused {
  color: #2196F3;
}

.status-value.error {
  color: #F44336;
}

/* 智能建议 */
.smart-suggestion {
  margin-top: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 10rpx;
  border-left: 4rpx solid #ff9800;
}

.suggestion-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #e65100;
  display: block;
  margin-bottom: 10rpx;
}

.suggestion-content {
  font-size: 24rpx;
  color: #bf360c;
  line-height: 1.5;
}

/* 网络信息 */
.network-info {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.address-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #4ECDC4;
}

.address-item:last-child {
  margin-bottom: 0;
}

.address-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.address-value {
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
  word-break: break-all;
  background: white;
  padding: 10rpx;
  border-radius: 5rpx;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-value.connecting {
  animation: pulse 1.5s infinite;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .image-controls {
    grid-template-columns: 1fr 1fr;
    gap: 15rpx;
  }
  
  .control-btn {
    min-height: 80rpx;
    font-size: 18rpx;
  }
  
  .btn-icon {
    font-size: 24rpx;
  }
  
  .btn-text {
    font-size: 18rpx;
  }
}

@media (max-width: 600rpx) {
  .image-controls {
    grid-template-columns: 1fr 1fr;
    gap: 15rpx;
  }
  
  .control-btn {
    height: 70rpx;
    font-size: 16rpx;
  }
  
  .btn-icon {
    font-size: 22rpx;
    margin-bottom: 3rpx;
  }
  
  .btn-text {
    font-size: 16rpx;
  }
} 