// 实时图片查看器页面
Page({
  data: {
    // 图片相关
    imageUrl: 'https://s1.v100.vip:33501/jpg', // 修正：默认图片接口
    currentImageUrl: '', // 当前显示的图片URL（包含时间戳）
    imageStatus: 'waiting', // waiting, loading, success, error
    imageStatusText: '等待加载',
    lastUpdateTime: '',
    imageRefreshCount: 0,
    autoRefreshImage: false,
    refreshInterval: 5000, // 刷新间隔，默认5秒
    refreshTimer: null,
    
    // 视频流模拟相关
    videoStreamMode: false, // 视频流模式
    streamFps: 5, // 帧率，默认5FPS
    streamTimer: null, // 视频流定时器
    streamFrameCount: 0, // 已播放帧数
    streamStartTime: null, // 流开始时间
    streamDuration: '00:00', // 播放时长
    streamStatusText: '未开始', // 流状态文本
    showStreamSettings: false, // 是否显示流设置面板
    errorMsg: '',
    
    // 优化的单缓冲系统 - 避免内存问题
    nextImageUrl: '', // 下一帧URL
    isLoading: false, // 是否正在加载
    loadingTimeout: null, // 加载超时定时器
    maxLoadTime: 3000, // 最大加载时间3秒
    skipFrameCount: 0, // 跳帧计数
    lastLoadTime: 0, // 上次加载时间
    
    // 网络错误重试计数器
    retryCount_socket_hang_up: 0, // Socket hang up错误重试次数
    retryCount_network_error: 0, // 网络错误重试次数
    consecutiveErrorCount: 0, // 连续错误计数
    lastErrorType: '', // 最后一次错误类型
    networkQuality: 'unknown', // 网络质量评估
    
    // 前台图片加载完成标志
    frontImageLoaded: false,
    frontImageActive: true,
    frontImageUrl: '',
    
    // 后台图片加载完成标志
    backImageLoaded: false,
    backImageUrl: ''
  },

  onLoad: function (options) {
    console.log('图片查看器页面加载')
    
    // 检测网络状态
    this.checkNetworkStatus()
    
    // 显示欢迎提示
    wx.showToast({
      title: '欢迎使用图片查看器',
      icon: 'success'
    })
  },

  onReady: function () {
    console.log('图片查看器页面准备就绪')
  },

  onUnload: function () {
    // 页面卸载时清理定时器
    this.stopAutoRefresh()
    this.stopVideoStream()
  },

  onShow: function () {
    // 页面显示时恢复自动刷新
    if (this.data.autoRefreshImage && !this.data.refreshTimer) {
      this.startAutoRefresh()
    }
    // 恢复视频流
    if (this.data.videoStreamMode && !this.data.streamTimer) {
      this.startVideoStream()
    }
  },

  onHide: function () {
    // 页面隐藏时暂停自动刷新
    this.stopAutoRefresh()
    // 暂停视频流
    this.pauseVideoStream()
  },

  // 检测网络状态
  checkNetworkStatus: function() {
    wx.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType)
        if (res.networkType === 'none') {
          wx.showToast({
            title: '网络连接异常',
            icon: 'none'
          })
        }
      }
    })
  },

  // 图片URL输入
  onImageUrlInput: function(e) {
    this.setData({
      imageUrl: e.detail.value
    })
  },

  // 添加时间戳避免缓存
  addTimestamp: function() {
    const { imageUrl } = this.data
    if (!imageUrl) {
      wx.showToast({
        title: '请先输入图片URL',
        icon: 'none'
      })
      return
    }
    
    const separator = imageUrl.includes('?') ? '&' : '?'
    const timestampUrl = `${imageUrl}${separator}t=${Date.now()}`
    
    this.setData({
      imageUrl: timestampUrl
    })
    
    wx.showToast({
      title: '已添加时间戳',
      icon: 'success'
    })
  },

  // 手动刷新图片
  refreshImage: function() {
    if (!this.data.imageUrl) {
      wx.showToast({
        title: '请先输入图片URL',
        icon: 'none'
      })
      return
    }
    
    this.loadImage()
  },

  // 加载图片
  loadImage: function() {
    const { imageUrl } = this.data
    const timestamp = Date.now()
    const separator = imageUrl.includes('?') ? '&' : '?'
    const timestampUrl = `${imageUrl}${separator}_t=${timestamp}`
    
    this.setData({
      imageStatus: 'loading',
      imageStatusText: '加载中...',
      currentImageUrl: timestampUrl
    })
    
    console.log('开始加载图片:', timestampUrl)
  },

  // 图片加载成功
  onImageLoad: function(e) {
    console.log('图片加载成功', e)
    const now = new Date()
    const timeString = now.toLocaleTimeString()
    
    // 重置错误计数器（成功加载时）
    this.setData({
      imageStatus: 'success',
      imageStatusText: this.data.videoStreamMode ? '播放中' : '加载成功',
      lastUpdateTime: timeString,
      imageRefreshCount: this.data.imageRefreshCount + 1,
      isLoading: false, // 确保清除加载状态
      // 重置所有错误计数器
      retryCount_socket_hang_up: 0,
      retryCount_network_error: 0,
      consecutiveErrorCount: 0,
      lastErrorType: '',
      networkQuality: 'good' // 成功加载表示网络质量良好
    })
  },

  // 图片加载失败
  onImageError: function(e) {
    console.log('图片加载失败', e)
    const errorDetail = e.detail || {}
    const errorMsg = errorDetail.errMsg || '未知错误'
    
    // 增加连续错误计数
    this.setData({
      consecutiveErrorCount: this.data.consecutiveErrorCount + 1,
      lastErrorType: errorMsg
    })
    
    // 专门处理socket hang up错误
    if (errorMsg.includes('socket hang up')) {
      console.log('Image组件检测到socket hang up错误')
      this.setData({
        imageStatus: 'error',
        imageStatusText: '连接中断',
        currentImageUrl: '', // 清空图片，显示占位符
        errorMsg: 'Socket连接被意外中断\n\n这是网络连接不稳定的表现\n\n解决建议：\n• 稍后重试\n• 检查网络连接稳定性\n• 切换到更稳定的网络环境',
        networkQuality: 'poor'
      })
      
      wx.showToast({
        title: '网络连接不稳定',
        icon: 'none',
        duration: 3000
      })
      return
    }
    
    // 详细错误分析
    let errorAnalysis = '加载失败原因分析：\n'
    if (errorMsg.includes('domain')) {
      errorAnalysis += '• 域名配置问题：需要在小程序后台配置业务域名\n'
    }
    if (errorMsg.includes('cert') || errorMsg.includes('ssl') || errorMsg.includes('tls')) {
      errorAnalysis += '• HTTPS证书问题：证书验证失败\n'
    }
    if (errorMsg.includes('timeout')) {
      errorAnalysis += '• 网络超时：请检查网络连接\n'
    }
    if (errorMsg.includes('404') || errorMsg.includes('not found')) {
      errorAnalysis += '• 资源不存在：请检查URL是否正确\n'
    }
    if (errorMsg.includes('403') || errorMsg.includes('forbidden')) {
      errorAnalysis += '• 访问被拒绝：服务器拒绝请求\n'
    }
    
    this.setData({
      imageStatus: 'error',
      imageStatusText: '加载失败',
      currentImageUrl: '', // 清空图片，显示占位符
      errorMsg: `${errorMsg}\n\n${errorAnalysis}`,
      networkQuality: errorMsg.includes('timeout') || errorMsg.includes('network') ? 'poor' : 'unknown'
    })
    
    // 显示详细错误信息
    wx.showModal({
      title: '图片加载失败',
      content: `错误信息：${errorMsg}\n\n${errorAnalysis}\n建议解决方案：\n1. 检查小程序后台域名配置\n2. 尝试使用其他测试URL\n3. 检查网络连接状态`,
      showCancel: true,
      cancelText: '取消',
      confirmText: '测试连接',
      success: (res) => {
        if (res.confirm) {
          this.testImageConnectivity()
        }
      }
    })
  },

  // 新增：测试图片连通性
  testImageConnectivity: function() {
    wx.showLoading({
      title: '测试连接中...'
    })
    
    // 测试多个已知可用的图片URL
    const testUrls = [
      'https://s1.v100.vip:33501/jpg', // 正确的主要接口
      'https://s1.v100.vip:33501/snapshot/1', // 备用接口路径
      'https://s11.v100.vip:33501/jpg', // 原错误URL作为备用
      'https://picsum.photos/400/300', // 公共测试图片服务
      'https://jsonplaceholder.typicode.com/photos/1' // JSON占位图片
    ]
    
    const testResults = []
    let completedTests = 0
    
    testUrls.forEach((url, index) => {
      // 创建测试用的临时image对象进行连通性测试
      const timestamp = Date.now()
      const testUrl = url.includes('?') ? `${url}&test=${timestamp}` : `${url}?test=${timestamp}`
      
      // 使用下载接口测试连通性
      wx.downloadFile({
        url: testUrl,
        success: (res) => {
          testResults.push({
            url: url,
            status: 'success',
            statusCode: res.statusCode,
            message: '连接成功'
          })
        },
        fail: (err) => {
          testResults.push({
            url: url,
            status: 'failed',
            error: err.errMsg,
            message: '连接失败'
          })
        },
        complete: () => {
          completedTests++
          if (completedTests === testUrls.length) {
            wx.hideLoading()
            this.showConnectivityResults(testResults)
          }
        }
      })
    })
  },

  // 显示连通性测试结果
  showConnectivityResults: function(results) {
    const successResults = results.filter(r => r.status === 'success')
    const failedResults = results.filter(r => r.status === 'failed')
    
    let resultText = `连通性测试结果：\n\n`
    
    if (successResults.length > 0) {
      resultText += `✅ 可用连接 (${successResults.length}个)：\n`
      successResults.forEach(r => {
        resultText += `• ${r.url}\n`
      })
      resultText += '\n'
    }
    
    if (failedResults.length > 0) {
      resultText += `❌ 失败连接 (${failedResults.length}个)：\n`
      failedResults.forEach(r => {
        resultText += `• ${r.url}\n  错误：${r.error}\n`
      })
    }
    
    if (successResults.length > 0) {
      const recommendedUrl = successResults[0].url
      resultText += `\n建议使用：${recommendedUrl}`
      
      wx.showModal({
        title: '连通性测试完成',
        content: resultText,
        showCancel: true,
        cancelText: '取消',
        confirmText: '使用推荐URL',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              imageUrl: recommendedUrl
            })
            wx.showToast({
              title: '已更新图片URL',
              icon: 'success'
            })
          }
        }
      })
    } else {
      wx.showModal({
        title: '连通性测试完成',
        content: resultText + '\n\n所有连接都失败，请检查：\n1. 网络连接\n2. 小程序域名配置\n3. 服务器状态',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 切换自动刷新
  toggleAutoRefresh: function() {
    if (this.data.autoRefreshImage) {
      this.stopAutoRefresh()
    } else {
      this.startAutoRefresh()
    }
  },

  // 开始自动刷新
  startAutoRefresh: function() {
    if (!this.data.imageUrl) {
      wx.showToast({
        title: '请先输入图片URL',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      autoRefreshImage: true
    })
    
    // 立即加载一次
    this.loadImage()
    
    // 设置定时器
    this.data.refreshTimer = setInterval(() => {
      this.loadImage()
    }, this.data.refreshInterval)
    
    wx.showToast({
      title: `已开启自动刷新(${this.data.refreshInterval/1000}秒)`,
      icon: 'success'
    })
  },

  // 停止自动刷新
  stopAutoRefresh: function() {
    if (this.data.refreshTimer) {
      clearInterval(this.data.refreshTimer)
      this.setData({
        refreshTimer: null
      })
    }
    
    this.setData({
      autoRefreshImage: false
    })
    
    if (this.data.autoRefreshImage !== false) {
      wx.showToast({
        title: '已停止自动刷新',
        icon: 'success'
      })
    }
  },

  // 设置刷新间隔
  setRefreshInterval: function(e) {
    const interval = parseInt(e.currentTarget.dataset.interval)
    this.setData({
      refreshInterval: interval
    })
    
    // 如果正在自动刷新，重启定时器
    if (this.data.autoRefreshImage) {
      this.stopAutoRefresh()
      this.startAutoRefresh()
    }
    
    wx.showToast({
      title: `刷新间隔设置为${interval/1000}秒`,
      icon: 'success'
    })
  },

  // 保存图片到相册
  saveImage: function() {
    if (!this.data.currentImageUrl) {
      wx.showToast({
        title: '没有可保存的图片',
        icon: 'none'
      })
      return
    }
    
    wx.showLoading({
      title: '保存中...'
    })
    
    // 先下载图片
    wx.downloadFile({
      url: this.data.currentImageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading()
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              wx.hideLoading()
              if (err.errMsg.includes('auth')) {
                wx.showModal({
                  title: '需要授权',
                  content: '需要获取保存到相册的权限',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting()
                    }
                  }
                })
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '图片下载失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '图片下载失败',
          icon: 'none'
        })
      }
    })
  },

  // 复制图片链接
  copyImageUrl: function() {
    if (!this.data.currentImageUrl) {
      wx.showToast({
        title: '没有可复制的链接',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: this.data.currentImageUrl,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  // 查看全屏图片
  viewFullscreen: function() {
    if (!this.data.currentImageUrl) {
      return
    }

    wx.previewImage({
      urls: [this.data.currentImageUrl],
      current: this.data.currentImageUrl
    })
  },

  // ===== 视频流生成功能 =====
  
  // 切换视频流模式
  toggleVideoStream: function() {
    if (this.data.videoStreamMode) {
      this.stopVideoStream()
    } else {
      this.startVideoStream()
    }
  },

  // 开始视频流
  startVideoStream: function() {
    if (!this.data.imageUrl) {
      wx.showToast({
        title: '请先输入图片URL',
        icon: 'none'
      })
      return
    }

    // 如果正在自动刷新，先停止
    if (this.data.autoRefreshImage) {
      this.stopAutoRefresh()
    }

    const now = Date.now()
    this.setData({
      videoStreamMode: true,
      streamFrameCount: 0,
      streamStartTime: now,
      streamStatusText: '播放中',
      showStreamSettings: false,
      // 重置加载状态
      isLoading: false,
      skipFrameCount: 0,
      lastLoadTime: 0
    })

    // 立即加载第一帧
    const firstFrameUrl = `${this.data.imageUrl}${this.data.imageUrl.includes('?') ? '&' : '?'}_t=${Date.now()}&frame=1`
    this.setData({
      currentImageUrl: firstFrameUrl
    })

    // 设置定时器按帧率播放
    const intervalMs = 1000 / this.data.streamFps
    this.data.streamTimer = setInterval(() => {
      this.loadStreamFrame()
      this.updateStreamInfo()
    }, intervalMs)

    wx.showToast({
      title: `视频流已开始 (${this.data.streamFps}FPS)`,
      icon: 'success'
    })
  },

  // 停止视频流
  stopVideoStream: function() {
    this.pauseVideoStream()
    
    // 清理所有状态和定时器
    if (this.data.loadingTimeout) {
      clearTimeout(this.data.loadingTimeout)
    }
    
    this.setData({
      videoStreamMode: false,
      streamStatusText: '已停止',
      showStreamSettings: false,
      // 清理加载状态
      isLoading: false,
      nextImageUrl: '',
      skipFrameCount: 0,
      lastLoadTime: 0,
      loadingTimeout: null,
      // 重置所有错误计数器
      retryCount_socket_hang_up: 0,
      retryCount_network_error: 0,
      consecutiveErrorCount: 0,
      lastErrorType: '',
      networkQuality: 'unknown'
    })

    // 强制垃圾回收（如果支持）
    if (wx.triggerGC) {
      wx.triggerGC()
    }

    wx.showToast({
      title: '视频流已停止',
      icon: 'success'
    })
  },

  // 暂停视频流（保持模式但停止定时器）
  pauseVideoStream: function() {
    if (this.data.streamTimer) {
      clearInterval(this.data.streamTimer)
      this.setData({
        streamTimer: null,
        streamStatusText: this.data.videoStreamMode ? '已暂停' : '已停止'
      })
    }
    
    // 清理加载超时
    if (this.data.loadingTimeout) {
      clearTimeout(this.data.loadingTimeout)
      this.setData({
        loadingTimeout: null,
        isLoading: false
      })
    }
  },

  // 加载视频流帧 - 内存优化版本
  loadStreamFrame: function() {
    // 如果正在加载，跳过这次加载（避免堆积）
    if (this.data.isLoading) {
      this.setData({
        skipFrameCount: this.data.skipFrameCount + 1
      })
      console.log('跳过加载，正在处理中...')
      return
    }
    
    const now = Date.now()
    const { imageUrl, lastLoadTime, maxLoadTime } = this.data
    
    // 如果距离上次加载时间太短，跳过（防止过快加载）
    if (now - lastLoadTime < 100) {
      return
    }
    
    const timestamp = now
    const separator = imageUrl.includes('?') ? '&' : '?'
    const frameUrl = `${imageUrl}${separator}_t=${timestamp}&frame=${this.data.streamFrameCount + 1}`
    
    console.log(`开始加载第${this.data.streamFrameCount + 1}帧`)
    
    // 设置加载状态
    this.setData({
      isLoading: true,
      nextImageUrl: frameUrl,
      lastLoadTime: now
    })
    
    // 预加载图片
    this.preloadImage(frameUrl)
    
    // 设置超时保护
    this.data.loadingTimeout = setTimeout(() => {
      if (this.data.isLoading) {
        console.log('加载超时，强制切换')
        this.forceSwitch(frameUrl)
      }
    }, maxLoadTime)
  },
  
  // 预加载图片
  preloadImage: function(imageUrl) {
    wx.getImageInfo({
      src: imageUrl,
      success: (res) => {
        console.log('图片预加载成功')
        this.switchToNewFrame(imageUrl)
      },
      fail: (err) => {
        console.log('图片预加载失败:', err)
        
        // 详细的错误分析和处理
        const errorMsg = err.errMsg || err.message || '未知错误'
        console.log('错误详情:', errorMsg)
        
        // 专门处理socket hang up错误
        if (errorMsg.includes('socket hang up')) {
          console.log('检测到socket hang up错误，进行重试')
          this.handleSocketHangUpError(imageUrl)
          return
        }
        
        // 其他网络错误处理
        if (errorMsg.includes('timeout') || errorMsg.includes('network')) {
          console.log('检测到网络错误，进行重试')
          this.handleNetworkError(imageUrl, errorMsg)
          return
        }
        
        // 一般错误处理
        this.handleLoadError()
      }
    })
  },
  
  // 切换到新帧
  switchToNewFrame: function(frameUrl) {
    // 清除超时定时器
    if (this.data.loadingTimeout) {
      clearTimeout(this.data.loadingTimeout)
      this.setData({
        loadingTimeout: null
      })
    }
    
    // 平滑切换
    this.setData({
      currentImageUrl: frameUrl,
      streamFrameCount: this.data.streamFrameCount + 1,
      isLoading: false,
      skipFrameCount: Math.max(0, this.data.skipFrameCount - 1) // 减少跳帧计数
    })
  },
  
  // 强制切换（超时保护）
  forceSwitch: function(frameUrl) {
    console.log('强制切换到新帧')
    this.setData({
      currentImageUrl: frameUrl,
      streamFrameCount: this.data.streamFrameCount + 1,
      isLoading: false
    })
  },
  
  // 处理加载错误
  handleLoadError: function() {
    this.setData({
      isLoading: false,
      skipFrameCount: this.data.skipFrameCount + 1
    })
    console.log('图片加载失败，跳过此帧')
  },

  // 前台图片加载完成
  onFrontImageLoad: function(e) {
    console.log('前台图片加载完成', e)
    this.setData({
      frontImageLoaded: true
    })
    
    // 如果前台图片加载完成且当前应该显示前台，则切换
    if (!this.data.frontImageActive && this.data.videoStreamMode) {
      this.switchToFrontBuffer()
    }
    
    this.updateLoadStats()
  },

  // 后台图片加载完成
  onBackImageLoad: function(e) {
    console.log('后台图片加载完成', e)
    this.setData({
      backImageLoaded: true
    })
    
    // 如果后台图片加载完成且当前应该显示后台，则切换
    if (this.data.frontImageActive && this.data.videoStreamMode) {
      this.switchToBackBuffer()
    }
    
    this.updateLoadStats()
  },

  // 切换到前台缓冲区
  switchToFrontBuffer: function() {
    console.log('切换到前台缓冲区')
    this.setData({
      frontImageActive: true,
      streamFrameCount: this.data.streamFrameCount + 1,
      currentImageUrl: this.data.frontImageUrl
    })
  },

  // 切换到后台缓冲区
  switchToBackBuffer: function() {
    console.log('切换到后台缓冲区')
    this.setData({
      frontImageActive: false,
      streamFrameCount: this.data.streamFrameCount + 1,
      currentImageUrl: this.data.backImageUrl
    })
  },

  // 更新加载统计
  updateLoadStats: function() {
    if (this.data.videoStreamMode) {
      const now = new Date()
      const timeString = now.toLocaleTimeString()
      
      this.setData({
        imageStatus: 'success',
        imageStatusText: '播放中',
        lastUpdateTime: timeString,
        imageRefreshCount: this.data.imageRefreshCount + 1
      })
    }
  },

  // 更新流信息
  updateStreamInfo: function() {
    if (!this.data.streamStartTime) return

    const now = Date.now()
    const durationMs = now - this.data.streamStartTime
    const seconds = Math.floor(durationMs / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    
    const formattedTime = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    
    this.setData({
      streamDuration: formattedTime
    })
  },

  // 设置视频流帧率
  setStreamFps: function(e) {
    const fps = parseInt(e.currentTarget.dataset.fps)
    this.setData({
      streamFps: fps
    })

    // 如果正在播放，重启流以应用新帧率
    if (this.data.videoStreamMode && this.data.streamTimer) {
      this.pauseVideoStream()
      
      setTimeout(() => {
        const intervalMs = 1000 / fps
        this.data.streamTimer = setInterval(() => {
          this.loadStreamFrame()
          this.updateStreamInfo()
        }, intervalMs)
        
        this.setData({
          streamStatusText: '播放中'
        })
      }, 200)
    }

    wx.showToast({
      title: `帧率设置为 ${fps}FPS`,
      icon: 'success'
    })
  },

  // 切换流设置面板显示
  toggleStreamSettings: function() {
    this.setData({
      showStreamSettings: !this.data.showStreamSettings
    })
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '实时图片查看器',
      path: '/pages/image-viewer/image-viewer'
    }
  },

  // 处理socket hang up错误
  handleSocketHangUpError: function(imageUrl) {
    const { videoStreamMode } = this.data
    
    if (videoStreamMode) {
      // 视频流模式：智能重试
      this.retryImageLoad(imageUrl, 'socket_hang_up')
    } else {
      // 非视频流模式：显示详细错误
      this.setData({
        imageStatus: 'error',
        imageStatusText: '连接中断',
        errorMsg: 'Socket连接被意外中断\n\n可能原因：\n• 服务器主动断开连接\n• 网络不稳定\n• 负载均衡器超时\n• CDN节点问题\n\n建议：\n• 稍后重试\n• 检查网络连接\n• 尝试切换网络环境'
      })
      
      wx.showToast({
        title: '连接中断，请重试',
        icon: 'none',
        duration: 3000
      })
    }
  },
  
  // 处理一般网络错误
  handleNetworkError: function(imageUrl, errorMsg) {
    const { videoStreamMode } = this.data
    
    if (videoStreamMode) {
      // 视频流模式：重试
      this.retryImageLoad(imageUrl, 'network_error')
    } else {
      // 非视频流模式：显示错误信息
      this.setData({
        imageStatus: 'error',
        imageStatusText: '网络错误',
        errorMsg: `网络连接失败：${errorMsg}\n\n建议解决方案：\n• 检查网络连接状态\n• 尝试刷新页面\n• 切换到其他网络环境\n• 稍后重试`
      })
    }
  },
  
  // 重试图片加载
  retryImageLoad: function(imageUrl, errorType) {
    // 获取重试次数
    const retryKey = `retryCount_${errorType}`
    const currentRetryCount = this.data[retryKey] || 0
    const maxRetries = errorType === 'socket_hang_up' ? 3 : 2
    
    if (currentRetryCount < maxRetries) {
      // 增加重试计数
      this.setData({
        [retryKey]: currentRetryCount + 1,
        skipFrameCount: this.data.skipFrameCount + 1
      })
      
      console.log(`${errorType} 错误，第${currentRetryCount + 1}次重试`)
      
      // 延迟重试（socket hang up需要更长的延迟）
      const retryDelay = errorType === 'socket_hang_up' ? 1000 : 500
      
      setTimeout(() => {
        // 生成新的URL避免缓存
        const timestamp = Date.now()
        const separator = imageUrl.includes('?') ? '&' : '?'
        const retryUrl = `${imageUrl}${separator}retry=${timestamp}`
        
        // 直接切换显示，不再预加载
        this.setData({
          currentImageUrl: retryUrl,
          streamFrameCount: this.data.streamFrameCount + 1,
          isLoading: false
        })
        
        // 清理加载超时
        if (this.data.loadingTimeout) {
          clearTimeout(this.data.loadingTimeout)
          this.setData({
            loadingTimeout: null
          })
        }
      }, retryDelay)
    } else {
      // 超过最大重试次数，跳过此帧
      console.log(`${errorType} 错误重试次数超限，跳过此帧`)
      this.setData({
        skipFrameCount: this.data.skipFrameCount + 1,
        isLoading: false,
        [retryKey]: 0 // 重置重试计数
      })
      
      // 清理加载超时
      if (this.data.loadingTimeout) {
        clearTimeout(this.data.loadingTimeout)
        this.setData({
          loadingTimeout: null
        })
      }
    }
  }
}) 