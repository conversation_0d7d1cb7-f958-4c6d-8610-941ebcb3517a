<!--实时图片查看器页面-->
<view class="container">
  <view class="header">
    <text class="title">📷 实时图片查看器</text>
    <text class="subtitle">监控画面实时显示</text>
  </view>

  <!-- 图片显示区域 -->
  <view class="image-section">
    <view class="image-container">
      <view class="image-wrapper {{videoStreamMode ? 'stream-mode' : ''}}">
        <!-- 视频流播放指示器 -->
        <view class="stream-indicator" wx:if="{{videoStreamMode}}">
          <text>LIVE {{streamFps}}FPS</text>
          <text class="skip-info" wx:if="{{skipFrameCount > 0}}">(跳帧{{skipFrameCount}})</text>
        </view>
        
        <!-- 双缓冲图片显示 - 完全消除闪烁 -->
        <view class="image-dual-buffer" wx:if="{{currentImageUrl}}">
          <!-- 前台图片 -->
          <image 
            src="{{frontImageUrl}}" 
            mode="aspectFit"
            class="live-image buffer-front {{frontImageActive ? 'active' : 'inactive'}}"
            binderror="onImageError"
            bindload="onFrontImageLoad"
            bindtap="viewFullscreen"
            style="z-index: {{frontImageActive ? 2 : 1}};"
          />
          <!-- 后台图片 -->
          <image 
            src="{{backImageUrl}}" 
            mode="aspectFit"
            class="live-image buffer-back {{!frontImageActive ? 'active' : 'inactive'}}"
            binderror="onImageError"
            bindload="onBackImageLoad"
            bindtap="viewFullscreen"
            style="z-index: {{!frontImageActive ? 2 : 1}};"
          />
        </view>
        
        <!-- 优化的单图片显示 -->
        <image 
          wx:if="{{currentImageUrl}}"
          src="{{currentImageUrl}}" 
          mode="aspectFit"
          class="live-image {{videoStreamMode ? 'stream-mode' : 'single-mode'}} {{isLoading ? 'loading' : 'loaded'}}"
          binderror="onImageError"
          bindload="onImageLoad"
          bindtap="viewFullscreen"
          lazy-load="{{!videoStreamMode}}"
        />
        
        <view wx:else class="image-placeholder">
          <text class="placeholder-icon">📷</text>
          <text class="placeholder-text">等待加载图片...</text>
          <text class="placeholder-hint">点击下方"手动刷新"开始</text>
        </view>
      </view>
      
      <!-- 图片信息显示 -->
      <view class="image-info">
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">状态</text>
            <text class="info-value {{imageStatus === 'error' ? 'error' : ''}}">{{imageStatusText}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">更新时间</text>
            <text class="info-value">{{lastUpdateTime}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">刷新次数</text>
            <text class="info-value">{{imageRefreshCount}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">图片源</text>
            <text class="info-value">{{imageUrl}}</text>
          </view>
          <view class="info-item" wx:if="{{errorMsg}}">
            <text class="info-label">错误信息</text>
            <text class="info-value error">{{errorMsg}}</text>
          </view>
          <view class="info-item" wx:if="{{videoStreamMode}}">
            <text class="info-label">网络质量</text>
            <text class="info-value {{networkQuality === 'good' ? 'success' : networkQuality === 'poor' ? 'error' : ''}}">
              {{networkQuality === 'good' ? '🟢 良好' : networkQuality === 'poor' ? '🔴 不稳定' : '⚪ 未知'}}
            </text>
          </view>
          <view class="info-item" wx:if="{{videoStreamMode && consecutiveErrorCount > 0}}">
            <text class="info-label">连续错误</text>
            <text class="info-value error">{{consecutiveErrorCount}}次</text>
          </view>
        </view>
      </view>
    </view>
  </view>
    
  <!-- 控制面板 -->
  <view class="control-panel">
    <view class="panel-title">📱 控制面板</view>
    
    <!-- URL配置 -->
    <view class="url-config">
      <view class="config-label">图片地址配置</view>
      <view class="url-input-group">
        <input 
          class="url-input" 
          placeholder="请输入图片URL" 
          value="{{imageUrl}}" 
          bindinput="onImageUrlInput"
        />
        <button class="btn btn-small" bindtap="addTimestamp">🕐 添加时间戳</button>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-primary {{autoRefreshImage ? 'btn-active' : ''}}" bindtap="toggleAutoRefresh">
        {{autoRefreshImage ? '⏹️ 停止自动刷新' : '🔄 开启自动刷新'}}
      </button>
      <button class="btn btn-secondary" bindtap="refreshImage">🖼️ 手动刷新</button>
      <button class="btn btn-success" bindtap="saveImage">💾 保存图片</button>
      <button class="btn btn-info" bindtap="copyImageUrl">📋 复制链接</button>
      <button class="btn btn-warning" bindtap="testImageConnectivity">🔧 诊断连接</button>
    </view>
    
    <!-- 视频流生成控制 -->
    <view class="video-stream-section">
      <view class="stream-control">
        <button class="btn {{videoStreamMode ? 'btn-danger' : 'btn-warning'}} stream-btn" bindtap="toggleVideoStream">
          {{videoStreamMode ? '⏹️ 停止视频流' : '🎬 生成视频流'}}
        </button>
        <button class="btn btn-info stream-btn" bindtap="toggleStreamSettings" wx:if="{{videoStreamMode}}">
          ⚙️ 流设置
        </button>
      </view>
      
      <!-- 视频流设置面板 -->
      <view class="stream-settings" wx:if="{{videoStreamMode && showStreamSettings}}">
        <view class="setting-title">🎬 视频流设置</view>
        <view class="stream-config">
          <view class="config-row">
            <text class="config-label">帧率 (FPS)</text>
            <view class="fps-selector">
              <button 
                class="fps-btn {{streamFps === 1 ? 'active' : ''}}" 
                bindtap="setStreamFps" 
                data-fps="1">1</button>
              <button 
                class="fps-btn {{streamFps === 2 ? 'active' : ''}}" 
                bindtap="setStreamFps" 
                data-fps="2">2</button>
              <button 
                class="fps-btn {{streamFps === 5 ? 'active' : ''}}" 
                bindtap="setStreamFps" 
                data-fps="5">5</button>
              <button 
                class="fps-btn {{streamFps === 10 ? 'active' : ''}}" 
                bindtap="setStreamFps" 
                data-fps="10">10</button>
              <button 
                class="fps-btn {{streamFps === 15 ? 'active' : ''}}" 
                bindtap="setStreamFps" 
                data-fps="15">15</button>
              <button 
                class="fps-btn {{streamFps === 30 ? 'active' : ''}}" 
                bindtap="setStreamFps" 
                data-fps="30">30</button>
            </view>
          </view>
          <view class="config-row">
            <text class="config-label">流状态</text>
            <text class="config-value">{{streamStatusText}}</text>
          </view>
          <view class="config-row">
            <text class="config-label">已播放帧数</text>
            <text class="config-value">{{streamFrameCount}}</text>
          </view>
          <view class="config-row">
            <text class="config-label">播放时长</text>
            <text class="config-value">{{streamDuration}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 自动刷新设置 -->
    <view class="refresh-settings" wx:if="{{autoRefreshImage && !videoStreamMode}}">
      <view class="setting-title">⏱️ 刷新间隔设置</view>
      <view class="interval-selector">
        <button 
          class="interval-btn {{refreshInterval === 1000 ? 'active' : ''}}" 
          bindtap="setRefreshInterval" 
          data-interval="1000">1秒</button>
        <button 
          class="interval-btn {{refreshInterval === 3000 ? 'active' : ''}}" 
          bindtap="setRefreshInterval" 
          data-interval="3000">3秒</button>
        <button 
          class="interval-btn {{refreshInterval === 5000 ? 'active' : ''}}" 
          bindtap="setRefreshInterval" 
          data-interval="5000">5秒</button>
        <button 
          class="interval-btn {{refreshInterval === 10000 ? 'active' : ''}}" 
          bindtap="setRefreshInterval" 
          data-interval="10000">10秒</button>
      </view>
    </view>
  </view>

  <!-- 功能说明 -->
  <view class="info-panel">
    <view class="panel-title">💡 功能说明</view>
    <view class="feature-list">
      <view class="feature-item">
        <text class="feature-icon">🔄</text>
        <text class="feature-text">支持1-10秒自动刷新间隔</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🎬</text>
        <text class="feature-text">视频流模拟播放1-30FPS</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">📱</text>
        <text class="feature-text">点击图片可全屏查看</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">💾</text>
        <text class="feature-text">一键保存到手机相册</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🕐</text>
        <text class="feature-text">时间戳避免缓存问题</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">⚙️</text>
        <text class="feature-text">实时帧率调节和统计</text>
      </view>
    </view>
  </view>
</view> 