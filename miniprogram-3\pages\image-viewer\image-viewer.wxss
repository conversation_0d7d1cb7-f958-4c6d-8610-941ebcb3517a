/* 实时图片查看器页面样式 */

.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}

/* 图片显示区域 */
.image-section {
  margin-bottom: 30rpx;
}

.image-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.image-wrapper {
  width: 100%;
  height: 500rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  overflow: hidden;
  margin-bottom: 25rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx dashed #dee2e6;
  transition: all 0.3s ease;
}

.image-wrapper:active {
  transform: scale(0.98);
}

.live-image {
  width: 100%;
  height: 100%;
  transition: opacity 0.2s ease-in-out;
}

.live-image.loading {
  opacity: 0.7;
  filter: blur(0.5px);
}

.live-image.loaded {
  opacity: 1;
  filter: none;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  text-align: center;
  padding: 40rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.placeholder-hint {
  font-size: 24rpx;
  opacity: 0.7;
}

/* 图片信息网格 */
.image-info {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
  padding: 25rpx;
  border-radius: 15rpx;
  border: 2rpx solid #e3f2fd;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10rpx;
}

.info-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-weight: 500;
  text-transform: uppercase;
}

.info-value {
  font-size: 26rpx;
  color: #28a745;
  font-weight: 600;
  word-break: break-all;
}

.info-value.error {
  color: #dc3545;
  font-size: 22rpx;
  line-height: 1.4;
  max-height: 120rpx;
  overflow-y: auto;
  white-space: pre-line;
  word-break: break-all;
}

.info-value.success {
  color: #28a745;
  font-weight: 600;
}

/* 控制面板 */
.control-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

/* URL配置 */
.url-config {
  margin-bottom: 30rpx;
}

.config-label {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.url-input-group {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 18rpx 25rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 30rpx;
  font-size: 26rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.url-input:focus {
  border-color: #4facfe;
  background-color: white;
  box-shadow: 0 0 0 6rpx rgba(79, 172, 254, 0.1);
}

/* 操作按钮 */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.action-buttons .btn:nth-child(5) {
  grid-column: span 2;
}

.btn {
  padding: 18rpx 25rpx;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-small {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  min-width: auto;
  grid-column: span 1;
}

.btn-active {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 6rpx 16rpx rgba(40, 167, 69, 0.3);
}

/* 刷新设置 */
.refresh-settings {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 25rpx;
  border-radius: 15rpx;
  border: 2rpx solid #dee2e6;
}

.setting-title {
  font-size: 28rpx;
  color: #495057;
  font-weight: 600;
  margin-bottom: 20rpx;
  text-align: center;
}

.interval-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.interval-btn {
  padding: 15rpx 10rpx;
  font-size: 24rpx;
  border: 2rpx solid #6c757d;
  background: white;
  color: #6c757d;
  border-radius: 25rpx;
  transition: all 0.3s ease;
  text-align: center;
  font-weight: 500;
}

.interval-btn.active {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-color: #007bff;
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 12rpx rgba(0, 123, 255, 0.3);
}

.interval-btn:not(.active):active {
  transform: translateY(2rpx);
}

/* 信息面板 */
.info-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.feature-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border: 2rpx solid #dee2e6;
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.feature-icon {
  font-size: 32rpx;
  min-width: 32rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #495057;
  font-weight: 500;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .action-buttons {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .interval-selector {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .url-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-small {
    width: 100%;
    margin-top: 10rpx;
  }
}

/* 动画效果 */
.image-container, .control-panel, .info-panel {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态动画 */
.live-image {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 视频流生成功能样式 */
.video-stream-section {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  padding: 25rpx;
  border-radius: 15rpx;
  margin-bottom: 25rpx;
  border: 2rpx solid #e17055;
}

.stream-control {
  display: flex;
  gap: 15rpx;
  margin-bottom: 20rpx;
  justify-content: center;
}

.stream-btn {
  flex: 1;
  max-width: 200rpx;
}

.btn-warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(253, 203, 110, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(225, 112, 85, 0.4);
}

.stream-settings {
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e17055;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stream-config {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.config-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.config-row:last-child {
  border-bottom: none;
}

.config-label {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
  min-width: 120rpx;
}

.config-value {
  font-size: 26rpx;
  color: #e17055;
  font-weight: 600;
}

.fps-selector {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.fps-btn {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  border: 2rpx solid #6c757d;
  background: white;
  color: #6c757d;
  border-radius: 18rpx;
  transition: all 0.3s ease;
  min-width: 50rpx;
  text-align: center;
  font-weight: 500;
}

.fps-btn.active {
  background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
  color: white;
  border-color: #e17055;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 8rpx rgba(225, 112, 85, 0.3);
}

.fps-btn:not(.active):active {
  transform: translateY(2rpx);
}

/* 双缓冲图片显示样式 - 完全消除闪烁 */
.image-dual-buffer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.buffer-front,
.buffer-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease-in-out;
}

.buffer-front.active,
.buffer-back.active {
  opacity: 1;
}

.buffer-front.inactive,
.buffer-back.inactive {
  opacity: 0;
}

/* 单图模式样式 */
.single-mode {
  transition: opacity 0.3s ease-in-out;
}

/* 跳帧信息样式 */
.skip-info {
  font-size: 18rpx;
  color: #ff6b6b;
  margin-left: 10rpx;
}

/* 视频流模式下的特殊样式 */
.image-wrapper.stream-mode {
  border-color: #e17055;
  background: linear-gradient(135deg, #ffeaa7 20%, #fab1a0 100%);
  animation: streamPulse 2s infinite;
}

.image-wrapper.stream-mode .live-image {
  /* 简化过渡效果，减少GPU负担 */
  transition: opacity 0.15s ease-out;
}

/* 视频流平滑过渡效果 */
.live-image.stream-transition {
  opacity: 0.95;
}

.live-image.stream-loading {
  opacity: 0.8;
  filter: blur(0.5px);
}

.live-image.stream-ready {
  opacity: 1;
  filter: none;
  transition: all 0.2s ease-out;
}

@keyframes streamPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(225, 112, 85, 0.4);
  }
  50% {
    box-shadow: 0 0 0 15rpx rgba(225, 112, 85, 0);
  }
}

/* 视频流播放指示器 */
.stream-indicator {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  background: rgba(225, 112, 85, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10rpx);
  animation: fadeInDown 0.3s ease-out;
}

.stream-indicator::before {
  content: '●';
  color: #ff4757;
  animation: streamBlink 1.5s infinite;
}

@keyframes streamBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计更新 */
@media (max-width: 750rpx) {
  .action-buttons {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .interval-selector {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .url-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-small {
    width: 100%;
    margin-top: 10rpx;
  }
  
  .stream-control {
    flex-direction: column;
  }
  
  .stream-btn {
    max-width: none;
  }
  
  .fps-selector {
    justify-content: center;
  }
  
  .config-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
  
  .config-label {
    min-width: auto;
  }
} 