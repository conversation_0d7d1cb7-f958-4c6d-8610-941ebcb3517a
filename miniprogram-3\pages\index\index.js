// index.js
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

Page({
  data: {
    motto: 'Hello World',
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '',
    },
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
    pageLoaded: false,
    isNavigating: false,
    networkStatus: 'unknown'
  },

  onLoad: function() {
    console.log('宠物监控首页加载')
    
    try {
      this.setData({ pageLoaded: true })
      
      this.checkNetworkStatus()
      
      this.checkSystemCompatibility()
      
    } catch (error) {
      console.error('页面初始化失败:', error)
      this.handlePageError('页面初始化失败', error)
    }
  },

  onShow: function() {
    console.log('首页显示')
    
    this.setData({ isNavigating: false })
    
    this.checkNetworkStatus()
  },

  checkNetworkStatus: function() {
    wx.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType)
        this.setData({ networkStatus: res.networkType })
        
        if (res.networkType === 'none') {
          wx.showModal({
            title: '网络提醒',
            content: '当前无网络连接，部分功能可能无法使用。请检查网络设置后重试。',
            showCancel: false,
            confirmText: '知道了'
          })
        }
      },
      fail: (error) => {
        console.error('网络状态检查失败:', error)
        this.setData({ networkStatus: 'unknown' })
        
        const app = getApp()
        if (app && app.reportError) {
          app.reportError('network_check_failed_index', error)
        }
      }
    })
  },

  checkSystemCompatibility: function() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      console.log('系统信息:', systemInfo)
      
      const libVersion = systemInfo.SDKVersion
      if (libVersion) {
        const version = libVersion.split('.').map(Number)
        const minVersion = [2, 10, 0]
        
        let isCompatible = true
        for (let i = 0; i < 3; i++) {
          if (version[i] < minVersion[i]) {
            isCompatible = false
            break
          } else if (version[i] > minVersion[i]) {
            break
          }
        }
        
        if (!isCompatible) {
          wx.showModal({
            title: '版本提醒',
            content: `检测到您的微信版本较低，建议升级到最新版本以获得更好的使用体验。\n\n当前版本: ${libVersion}\n建议版本: 2.10.0+`,
            showCancel: false,
            confirmText: '知道了'
          })
        }
      }
    } catch (error) {
      console.error('系统兼容性检查失败:', error)
    }
  },

  handlePageError: function(title, error) {
    console.error(`${title}:`, error)
    
    const app = getApp()
    if (app && app.reportError) {
      app.reportError('page_error_index', { title, error })
    }
    
    wx.showModal({
      title: title,
      content: '页面遇到问题，请稍后重试。如问题持续存在，请重启小程序。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  safeNavigateTo: function(options) {
    const { url, title, beforeNavigate, afterNavigate } = options
    
    if (!url || typeof url !== 'string') {
      console.error('无效的跳转URL:', url)
      wx.showToast({
        title: '页面地址无效',
        icon: 'none'
      })
      return Promise.reject(new Error('Invalid URL'))
    }
    
    if (this.data.isNavigating) {
      console.log('正在跳转中，忽略重复请求')
      return Promise.reject(new Error('Navigation in progress'))
    }
    
    if (this.data.networkStatus === 'none') {
      wx.showModal({
        title: '网络异常',
        content: '当前无网络连接，无法跳转页面。请检查网络后重试。',
        showCancel: false
      })
      return Promise.reject(new Error('Network unavailable'))
    }
    
    this.setData({ isNavigating: true })
    
    return new Promise((resolve, reject) => {
      if (beforeNavigate && typeof beforeNavigate === 'function') {
        try {
          beforeNavigate()
        } catch (error) {
          console.error('跳转前处理失败:', error)
        }
      }
      
      wx.navigateTo({
        url: url,
        success: (res) => {
          console.log(`成功跳转到${title || url}`)
          
          if (title) {
            wx.showToast({
              title: `正在进入${title}`,
              icon: 'success',
              duration: 1500
            })
          }
          
          if (afterNavigate && typeof afterNavigate === 'function') {
            try {
              afterNavigate(res)
            } catch (error) {
              console.error('跳转后处理失败:', error)
            }
          }
          
          resolve(res)
        },
        fail: (error) => {
          console.error(`跳转到${title || url}失败:`, error)
          
          this.setData({ isNavigating: false })
          
          const app = getApp()
          if (app && app.reportError) {
            app.reportError('navigation_failed', { url, title, error })
          }
          
          wx.showModal({
            title: '跳转失败',
            content: `无法进入${title || '目标页面'}，可能的原因：\n\n1. 页面正在维护\n2. 网络连接问题\n3. 应用版本过低\n\n是否重试？`,
            showCancel: true,
            confirmText: '重试',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                setTimeout(() => {
                  this.safeNavigateTo(options)
                }, 1000)
              }
            }
          })
          
          reject(error)
        },
        complete: () => {
          setTimeout(() => {
            this.setData({ isNavigating: false })
          }, 1000)
        }
      })
    })
  },

  bindViewTap() {
    this.safeNavigateTo({
      url: '../logs/logs',
      title: '系统日志'
    })
  },

  onChooseAvatar(e) {
    try {
      const { avatarUrl } = e.detail
      const { nickName } = this.data.userInfo
      
      if (!avatarUrl || typeof avatarUrl !== 'string') {
        console.error('无效的头像URL:', avatarUrl)
        wx.showToast({
          title: '头像设置失败',
          icon: 'none'
        })
        return
      }
      
      this.setData({
        "userInfo.avatarUrl": avatarUrl,
        hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
      })
      
      wx.showToast({
        title: '头像设置成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('头像选择处理失败:', error)
      this.handlePageError('头像设置失败', error)
    }
  },

  onInputChange(e) {
    try {
      const nickName = e.detail.value
      const { avatarUrl } = this.data.userInfo
      
      if (nickName && nickName.length > 20) {
        wx.showToast({
          title: '昵称长度不能超过20个字符',
          icon: 'none'
        })
        return
      }
      
      this.setData({
        "userInfo.nickName": nickName,
        hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
      })
    } catch (error) {
      console.error('昵称输入处理失败:', error)
      this.handlePageError('昵称设置失败', error)
    }
  },

  getUserProfile(e) {
    wx.getUserProfile({
      desc: '展示用户信息',
      success: (res) => {
        console.log('获取用户信息成功:', res)
        
        try {
          if (!res.userInfo) {
            throw new Error('用户信息为空')
          }
          
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          })
          
          wx.showToast({
            title: '用户信息获取成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('用户信息处理失败:', error)
          this.handlePageError('用户信息设置失败', error)
        }
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        
        const app = getApp()
        if (app && app.reportError) {
          app.reportError('get_user_profile_failed', error)
        }
        
        wx.showModal({
          title: '获取用户信息失败',
          content: '无法获取用户信息，您仍可以正常使用小程序的其他功能。',
          showCancel: false,
          confirmText: '知道了'
        })
      }
    })
  },
  
  goToCamViewer() {
    this.safeNavigateTo({
      url: '/pages/cam-viewer/cam-viewer',
      title: '宠物视角摄像头',
      beforeNavigate: () => {
        console.log('准备进入摄像头页面')
      },
      afterNavigate: () => {
        console.log('已进入摄像头页面')
      }
    }).catch(error => {
      console.error('进入摄像头页面失败:', error)
    })
  },
  


  goToImageViewer() {
    this.safeNavigateTo({
      url: '/pages/image-viewer/image-viewer',
      title: '图片查看器'
    }).catch(error => {
      console.error('进入图片查看器失败:', error)
    })
  }
})
