/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40rpx;
  box-sizing: border-box;
}

/* 主标题样式 */
.main-header {
  text-align: center;
  margin-bottom: 120rpx;
  padding: 60rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  box-shadow: 0 15rpx 45rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  display: block;
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

/* 主要功能入口 */
.main-entrance {
  margin-bottom: 80rpx;
}

.main-cam-btn {
  width: 400rpx;
  height: 120rpx;
  background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
  color: white;
  border: none;
  border-radius: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 15rpx 35rpx rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.main-cam-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.main-cam-btn:active::before {
  left: 100%;
}

.main-cam-btn:active {
  transform: translateY(3rpx);
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
}

.btn-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
  display: block;
}

.btn-text {
  font-size: 32rpx;
  line-height: 1.2;
  display: block;
  font-weight: bold;
}

/* 简单说明 */
.simple-description {
  text-align: center;
  padding: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5rpx);
}

.desc-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .main-header {
    margin-bottom: 80rpx;
    padding: 40rpx 30rpx;
  }
  
  .app-title {
    font-size: 42rpx;
  }
  
  .app-subtitle {
    font-size: 28rpx;
  }
  
  .main-cam-btn {
    width: 350rpx;
    height: 100rpx;
  }
  
  .btn-icon {
    font-size: 42rpx;
  }
  
  .btn-text {
    font-size: 28rpx;
  }
}

@media (max-width: 600rpx) {
  .container {
    padding: 30rpx;
  }
  
  .main-header {
    margin-bottom: 60rpx;
    padding: 30rpx 20rpx;
  }
  
  .app-title {
    font-size: 36rpx;
  }
  
  .app-subtitle {
    font-size: 24rpx;
  }
  
  .main-cam-btn {
    width: 300rpx;
    height: 90rpx;
  }
  
  .btn-icon {
    font-size: 36rpx;
  }
  
  .btn-text {
    font-size: 24rpx;
  }
  
  .desc-text {
    font-size: 24rpx;
  }
}
