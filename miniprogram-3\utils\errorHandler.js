/**
 * 通用异常处理和边界检查工具
 * 遵循SOLID原则，提供可复用的错误处理机制
 * <AUTHOR>
 */

// 错误类型常量
const ERROR_TYPES = {
  NETWORK_ERROR: 'network_error',
  PAGE_ERROR: 'page_error', 
  NAVIGATION_ERROR: 'navigation_error',
  DATA_VALIDATION_ERROR: 'data_validation_error',
  RESOURCE_ERROR: 'resource_error',
  MEMORY_ERROR: 'memory_error'
}

// 网络状态常量
const NETWORK_STATUS = {
  WIFI: 'wifi',
  MOBILE: '2g,3g,4g,5g'.split(','),
  NONE: 'none',
  UNKNOWN: 'unknown'
}

/**
 * 异常处理器类
 */
class ErrorHandler {
  constructor() {
    this.errorHistory = [] // 错误历史记录
    this.maxErrorHistory = 50 // 最大错误记录数
  }

  /**
   * 通用错误处理
   * @param {string} type - 错误类型
   * @param {Error|string} error - 错误对象或错误信息
   * @param {Object} context - 错误上下文信息
   * @param {Object} options - 处理选项
   */
  handleError(type, error, context = {}, options = {}) {
    const errorInfo = {
      type: type,
      error: error,
      context: context,
      timestamp: new Date().toISOString(),
      page: getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].route : 'unknown'
    }

    // 记录错误
    this.recordError(errorInfo)

    // 上报错误到全局处理器
    this.reportErrorToGlobal(errorInfo)

    // 用户提示处理
    if (options.showToUser !== false) {
      this.showUserFriendlyError(type, error, options)
    }

    // 自动恢复处理
    if (options.autoRecover) {
      this.attemptAutoRecover(type, error, options.autoRecover)
    }

    return errorInfo
  }

  /**
   * 记录错误到本地历史
   */
  recordError(errorInfo) {
    try {
      this.errorHistory.unshift(errorInfo)
      
      // 限制历史记录数量
      if (this.errorHistory.length > this.maxErrorHistory) {
        this.errorHistory.splice(this.maxErrorHistory)
      }

      console.error('错误记录:', errorInfo)
    } catch (recordError) {
      console.error('错误记录失败:', recordError)
    }
  }

  /**
   * 上报错误到全局处理器
   */
  reportErrorToGlobal(errorInfo) {
    try {
      const app = getApp()
      if (app && app.reportError) {
        app.reportError(errorInfo.type, errorInfo)
      }
    } catch (reportError) {
      console.error('错误上报失败:', reportError)
    }
  }

  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyError(type, error, options = {}) {
    const errorMessages = {
      [ERROR_TYPES.NETWORK_ERROR]: '网络连接异常，请检查网络设置',
      [ERROR_TYPES.PAGE_ERROR]: '页面出现问题，正在尝试恢复',
      [ERROR_TYPES.NAVIGATION_ERROR]: '页面跳转失败，请稍后重试',
      [ERROR_TYPES.DATA_VALIDATION_ERROR]: '数据格式错误，请检查输入',
      [ERROR_TYPES.RESOURCE_ERROR]: '资源加载失败，请稍后重试',
      [ERROR_TYPES.MEMORY_ERROR]: '内存不足，已优化性能'
    }

    const message = options.customMessage || errorMessages[type] || '操作失败，请稍后重试'

    if (options.useModal) {
      wx.showModal({
        title: '提示',
        content: message,
        showCancel: !!options.onRetry,
        confirmText: options.onRetry ? '重试' : '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm && options.onRetry) {
            options.onRetry()
          }
        }
      })
    } else {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: options.duration || 2000
      })
    }
  }

  /**
   * 自动恢复处理
   */
  attemptAutoRecover(type, error, recoverConfig) {
    if (typeof recoverConfig === 'function') {
      setTimeout(() => {
        try {
          recoverConfig()
        } catch (recoverError) {
          console.error('自动恢复失败:', recoverError)
        }
      }, recoverConfig.delay || 1000)
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory() {
    return [...this.errorHistory]
  }

  /**
   * 清理错误历史
   */
  clearErrorHistory() {
    this.errorHistory = []
  }
}

/**
 * 边界检查工具类
 */
class BoundaryChecker {
  /**
   * 检查字符串边界
   */
  static checkString(value, options = {}) {
    const { 
      minLength = 0, 
      maxLength = Infinity, 
      required = false,
      pattern = null 
    } = options

    if (required && (!value || value.trim() === '')) {
      throw new Error('字符串不能为空')
    }

    if (value && typeof value !== 'string') {
      throw new Error('值必须是字符串类型')
    }

    if (value && (value.length < minLength || value.length > maxLength)) {
      throw new Error(`字符串长度必须在${minLength}-${maxLength}之间`)
    }

    if (value && pattern && !pattern.test(value)) {
      throw new Error('字符串格式不符合要求')
    }

    return true
  }

  /**
   * 检查数字边界
   */
  static checkNumber(value, options = {}) {
    const { 
      min = -Infinity, 
      max = Infinity, 
      required = false,
      integer = false 
    } = options

    if (required && (value === null || value === undefined)) {
      throw new Error('数字不能为空')
    }

    if (value !== null && value !== undefined) {
      if (typeof value !== 'number' || isNaN(value)) {
        throw new Error('值必须是有效数字')
      }

      if (integer && !Number.isInteger(value)) {
        throw new Error('值必须是整数')
      }

      if (value < min || value > max) {
        throw new Error(`数字必须在${min}-${max}之间`)
      }
    }

    return true
  }

  /**
   * 检查URL格式
   */
  static checkURL(url, options = {}) {
    const { required = false, allowedProtocols = ['http', 'https'] } = options

    if (required && !url) {
      throw new Error('URL不能为空')
    }

    if (url) {
      if (typeof url !== 'string') {
        throw new Error('URL必须是字符串')
      }

      try {
        const urlObj = new URL(url)
        if (!allowedProtocols.includes(urlObj.protocol.replace(':', ''))) {
          throw new Error(`URL协议必须是${allowedProtocols.join('或')}`)
        }
      } catch (error) {
        throw new Error('URL格式无效')
      }
    }

    return true
  }

  /**
   * 检查图片尺寸边界
   */
  static checkImageSize(width, height, options = {}) {
    const { 
      minWidth = 0, 
      maxWidth = 4096, 
      minHeight = 0, 
      maxHeight = 4096,
      maxFileSize = 5 * 1024 * 1024 // 5MB
    } = options

    BoundaryChecker.checkNumber(width, { min: minWidth, max: maxWidth, required: true, integer: true })
    BoundaryChecker.checkNumber(height, { min: minHeight, max: maxHeight, required: true, integer: true })

    // 估算文件大小（RGB图像）
    const estimatedSize = width * height * 3
    if (estimatedSize > maxFileSize) {
      throw new Error(`图像过大，预估大小${(estimatedSize / 1024 / 1024).toFixed(1)}MB，最大允许${(maxFileSize / 1024 / 1024).toFixed(1)}MB`)
    }

    return true
  }
}

/**
 * 网络状态检查器
 */
class NetworkChecker {
  static async checkNetworkStatus() {
    return new Promise((resolve, reject) => {
      wx.getNetworkType({
        success: (res) => {
          const status = {
            type: res.networkType,
            isConnected: res.networkType !== 'none',
            isWifi: res.networkType === 'wifi',
            isMobile: NETWORK_STATUS.MOBILE.includes(res.networkType),
            timestamp: Date.now()
          }
          resolve(status)
        },
        fail: (error) => {
          reject(new Error(`网络状态检查失败: ${error.errMsg || '未知错误'}`))
        }
      })
    })
  }

  static async validateNetworkForOperation(operationType = 'default') {
    try {
      const networkStatus = await this.checkNetworkStatus()
      
      if (!networkStatus.isConnected) {
        throw new Error('当前无网络连接')
      }

      // 根据操作类型进行不同的网络质量检查
      const networkRequirements = {
        'image_load': { requireWifi: false, minSpeed: 'any' },
        'video_stream': { requireWifi: true, minSpeed: 'fast' },
        'page_navigation': { requireWifi: false, minSpeed: 'any' }
      }

      const requirement = networkRequirements[operationType] || networkRequirements['default']
      
      if (requirement.requireWifi && !networkStatus.isWifi) {
        console.warn(`操作${operationType}建议在WiFi环境下进行`)
      }

      return networkStatus
    } catch (error) {
      throw new Error(`网络验证失败: ${error.message}`)
    }
  }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  static startTiming(label) {
    console.time(label)
    return {
      end: () => console.timeEnd(label)
    }
  }

  static checkMemoryUsage() {
    try {
      const pages = getCurrentPages()
      const pageCount = pages.length
      
      console.log(`当前页面栈深度: ${pageCount}`)
      
      if (pageCount > 10) {
        console.warn('页面栈过深，可能存在内存泄漏风险')
        return { warning: true, pageCount }
      }
      
      return { warning: false, pageCount }
    } catch (error) {
      console.error('内存使用检查失败:', error)
      return { warning: true, error: error.message }
    }
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler()

// 导出模块
module.exports = {
  ErrorHandler,
  BoundaryChecker,
  NetworkChecker,
  PerformanceMonitor,
  ERROR_TYPES,
  NETWORK_STATUS,
  // 便捷方法
  handleError: (type, error, context, options) => errorHandler.handleError(type, error, context, options),
  checkBoundary: BoundaryChecker,
  checkNetwork: NetworkChecker,
  monitor: PerformanceMonitor
} 