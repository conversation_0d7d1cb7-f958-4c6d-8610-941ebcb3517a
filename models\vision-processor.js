/**
 * 视觉处理模型
 */

/**
 * 视觉模拟选项配置
 */
const VISION_OPTIONS = {
  // 色盲模拟选项
  COLOR_BLIND: {
    PROTANOPIA: { name: '红色盲', id: 'protanopia' },
    DEUTERANOPIA: { name: '绿色盲', id: 'deuteranopia' },
    TRITANOPIA: { name: '蓝色盲', id: 'tritanopia' },
    ACHROMATOPSIA: { name: '全色盲', id: 'achromatopsia' }
  },
  
  // 视力模拟选项
  VISUAL_ACUITY: {
    NORMAL: { name: '正常', level: 0, id: 'normal', blur: 0 },
    MILD: { name: '轻度模糊', level: 1, id: 'mild', blur: 1 },
    MODERATE: { name: '中度模糊', level: 2, id: 'moderate', blur: 3 },
    SEVERE: { name: '重度模糊', level: 3, id: 'severe', blur: 6 },
    PROFOUND: { name: '极重度模糊', level: 4, id: 'profound', blur: 10 }
  },
  
  // 视野模拟选项
  VISUAL_FIELD: {
    NORMAL: { name: '正常', id: 'normal_field', vignette: 0 },
    TUNNEL_VISION: { name: '隧道视野', id: 'tunnel', vignette: 0.7 },
    PERIPHERAL_VISION_LOSS: { name: '周边视野缺失', id: 'peripheral', vignette: 0.6 },
    HEMIANOPSIA: { name: '半侧偏盲', id: 'hemianopsia', type: 'left' },
    CENTRAL_VISION_LOSS: { name: '中央视野缺失', id: 'central_loss', type: 'circular' }
  }
};

/**
 * 初始化处理上下文
 */
function initProcessingContext() {
  // 返回一个模拟的处理上下文对象
  return {
    canvasId: null,
    canvas: null,
    ctx: null,
    width: 0,
    height: 0,
    initialized: false,
    lastOptions: {}
  };
}

/**
 * 设置画布
 * @param {Object} context - 处理上下文
 * @param {string} canvasId - 画布ID
 * @param {Object} component - 组件实例
 */
function setCanvas(context, canvasId, component) {
  return new Promise((resolve, reject) => {
    try {
      const query = component.createSelectorQuery();
      query.select('#' + canvasId)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0] && res[0].node) {
            context.canvasId = canvasId;
            context.canvas = res[0].node;
            context.ctx = context.canvas.getContext('2d');
            context.width = res[0].width;
            context.height = res[0].height;
            context.initialized = true;
            
            // 设置2D背景
            context.canvas.width = context.width;
            context.canvas.height = context.height;
            
            resolve(context);
          } else {
            reject(new Error('画布不存在'));
          }
        });
    } catch (err) {
      reject(err);
    }
  });
}

/**
 * 应用视觉处理效果
 * @param {Object} context - 处理上下文
 * @param {Object} frame - 帧数据（图像源）
 * @param {Object} options - 处理选项
 */
function applyVisionEffects(context, frame, options) {
  if (!context.initialized || !context.ctx || !frame) {
    return false;
  }
  
  // 记录处理选项
  context.lastOptions = options || {};
  
  // 绘制原始图像到画布
  context.ctx.clearRect(0, 0, context.width, context.height);
  context.ctx.drawImage(frame, 0, 0, context.width, context.height);
  
  // 获取图像数据
  const imageData = context.ctx.getImageData(0, 0, context.width, context.height);
  const pixels = imageData.data;
  
  // 颜色处理
  if (options.colorBlindType && options.colorBlindType !== 'normal') {
    applyColorBlindFilter(pixels, options.colorBlindType);
  }
  
  // 清晰度处理
  if (options.blurLevel && options.blurLevel > 0) {
    applyBlurFilter(context, imageData, options.blurLevel);
  } else {
    // 无模糊效果，直接绘制处理后的图像数据
    context.ctx.putImageData(imageData, 0, 0);
  }
  
  // 视野处理
  if (options.visualField && options.visualField !== 'normal_field') {
    applyVisualFieldEffect(context, options.visualField);
  }
  
  return true;
}

/**
 * 应用色盲滤镜
 * @param {Uint8ClampedArray} pixels - 图像像素数据
 * @param {string} type - 色盲类型
 */
function applyColorBlindFilter(pixels, type) {
  const len = pixels.length;
  
  // 模拟不同类型的色盲
  switch (type) {
    case 'protanopia': // 红色盲
      for (let i = 0; i < len; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        
        // 红色盲模拟算法
        pixels[i] = 0.567 * r + 0.433 * g + 0 * b;
        pixels[i + 1] = 0.558 * r + 0.442 * g + 0 * b;
        pixels[i + 2] = 0 * r + 0.242 * g + 0.758 * b;
      }
      break;
      
    case 'deuteranopia': // 绿色盲
      for (let i = 0; i < len; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        
        // 绿色盲模拟算法
        pixels[i] = 0.625 * r + 0.375 * g + 0 * b;
        pixels[i + 1] = 0.7 * r + 0.3 * g + 0 * b;
        pixels[i + 2] = 0 * r + 0.3 * g + 0.7 * b;
      }
      break;
      
    case 'tritanopia': // 蓝色盲
      for (let i = 0; i < len; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        
        // 蓝色盲模拟算法
        pixels[i] = 0.95 * r + 0.05 * g + 0 * b;
        pixels[i + 1] = 0 * r + 0.433 * g + 0.567 * b;
        pixels[i + 2] = 0 * r + 0.475 * g + 0.525 * b;
      }
      break;
      
    case 'achromatopsia': // 全色盲
      for (let i = 0; i < len; i += 4) {
        const r = pixels[i];
        const g = pixels[i + 1];
        const b = pixels[i + 2];
        
        // 全色盲（灰度）模拟算法
        const gray = 0.299 * r + 0.587 * g + 0.114 * b;
        pixels[i] = gray;
        pixels[i + 1] = gray;
        pixels[i + 2] = gray;
      }
      break;
  }
}

/**
 * 应用模糊滤镜
 * @param {Object} context - 处理上下文
 * @param {ImageData} imageData - 图像数据
 * @param {number} blurLevel - 模糊级别
 */
function applyBlurFilter(context, imageData, blurLevel) {
  // 使用CSS滤镜实现模糊效果
  context.ctx.putImageData(imageData, 0, 0);
  
  // 创建一个临时画布
  const tempCanvas = wx.createOffscreenCanvas({
    type: '2d',
    width: context.width,
    height: context.height
  });
  const tempCtx = tempCanvas.getContext('2d');
  
  // 将当前画布内容复制到临时画布
  tempCtx.drawImage(context.canvas, 0, 0);
  
  // 清除原画布
  context.ctx.clearRect(0, 0, context.width, context.height);
  
  // 设置模糊滤镜
  context.ctx.filter = `blur(${blurLevel}px)`;
  
  // 绘制临时画布到原画布
  context.ctx.drawImage(tempCanvas, 0, 0);
  
  // 重置滤镜
  context.ctx.filter = 'none';
}

/**
 * 应用视野效果
 * @param {Object} context - 处理上下文
 * @param {string} fieldType - 视野类型
 */
function applyVisualFieldEffect(context, fieldType) {
  const width = context.width;
  const height = context.height;
  const centerX = width / 2;
  const centerY = height / 2;
  
  switch (fieldType) {
    case 'tunnel': // 隧道视野
      context.ctx.save();
      context.ctx.globalCompositeOperation = 'destination-in';
      
      // 创建径向渐变
      const gradient = context.ctx.createRadialGradient(
        centerX, centerY, 0,
        centerX, centerY, Math.min(width, height) * 0.4
      );
      gradient.addColorStop(0, 'rgba(0, 0, 0, 1)');
      gradient.addColorStop(0.6, 'rgba(0, 0, 0, 0.8)');
      gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
      
      context.ctx.fillStyle = gradient;
      context.ctx.fillRect(0, 0, width, height);
      context.ctx.restore();
      break;
      
    case 'peripheral': // 周边视野缺失
      context.ctx.save();
      context.ctx.globalCompositeOperation = 'destination-out';
      
      // 创建径向渐变
      const peripheralGradient = context.ctx.createRadialGradient(
        centerX, centerY, Math.min(width, height) * 0.3,
        centerX, centerY, Math.min(width, height) * 0.6
      );
      peripheralGradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
      peripheralGradient.addColorStop(0.6, 'rgba(0, 0, 0, 0.5)');
      peripheralGradient.addColorStop(1, 'rgba(0, 0, 0, 1)');
      
      context.ctx.fillStyle = peripheralGradient;
      context.ctx.fillRect(0, 0, width, height);
      context.ctx.restore();
      break;
      
    case 'hemianopsia': // 半侧偏盲
      context.ctx.save();
      context.ctx.globalCompositeOperation = 'destination-out';
      context.ctx.fillStyle = 'black';
      context.ctx.fillRect(0, 0, centerX, height);
      context.ctx.restore();
      break;
      
    case 'central_loss': // 中央视野缺失
      context.ctx.save();
      context.ctx.globalCompositeOperation = 'destination-out';
      
      context.ctx.beginPath();
      context.ctx.arc(centerX, centerY, Math.min(width, height) * 0.2, 0, Math.PI * 2);
      context.ctx.fillStyle = 'black';
      context.ctx.fill();
      context.ctx.restore();
      break;
  }
}

/**
 * 重置处理上下文
 * @param {Object} context - 处理上下文
 */
function resetContext(context) {
  if (context.ctx) {
    context.ctx.clearRect(0, 0, context.width, context.height);
  }
  context.lastOptions = {};
}

module.exports = {
  VISION_OPTIONS,
  initProcessingContext,
  setCanvas,
  applyVisionEffects,
  resetContext
}; 