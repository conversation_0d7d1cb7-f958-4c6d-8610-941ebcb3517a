// 关于我们页面逻辑
const app = getApp();

Page({
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 44, // 默认导航栏高度
    currentTheme: 'theme1',
    teamInfo: {
      name: '爱宠视觉团队',
      slogan: 'AI科技 · 泰迪铲屎官 · 爱护每一只毛孩子',
      vision: '希望通过技术手段帮助人类更好地理解宠物，促进人宠和谐共处'
    }
  },

  onLoad: function() {
    // 获取系统信息，设置状态栏高度
    this.getSystemInfo();
    
    // 获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('关于我们页面加载，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
    
    // 注册主题变化回调
    if (app.themeChangeCallbacks) {
      // 添加当前页面的主题更新回调
      app.themeChangeCallbacks.push(this.onThemeChanged.bind(this));
    }
  },
  
  onShow: function() {
    // 页面显示时获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('关于我们页面显示，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  // 主题变化回调
  onThemeChanged: function() {
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('主题变化回调触发，新主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  onUnload: function() {
    // 页面卸载时移除回调
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback => 
        callback.toString() === this.onThemeChanged.bind(this).toString());
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
      }
    }
  },
  
  // 获取系统信息，设置状态栏高度
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        navBarHeight: 44 // 导航栏固定高度
      });
      console.log('状态栏高度:', systemInfo.statusBarHeight);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 导航到反馈与建议页面
  navigateToFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  }
}); 