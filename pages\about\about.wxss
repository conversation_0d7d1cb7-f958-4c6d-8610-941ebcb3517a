/* 关于我们页面样式 */
.about-container {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

.status-bar-height {
  width: 100%;
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 30rpx;
}

.nav-bar-back {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
  color: #ffffff;
  font-weight: 500;
}

.nav-bar-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  flex: 1;
}

/* 导航栏占位符，防止内容被导航栏遮挡 */
.nav-bar-placeholder {
  width: 100%;
}

/* 主题1: 天空蓝 */
.theme1 {
  --dark-bg: var(--theme1-dark-bg);
  --card-bg: var(--theme1-card-bg);
  background: var(--theme1-gradient);
}

/* 天空蓝主题导航栏 */
.theme1 .custom-nav-bar {
  background: var(--theme1-dark-bg);
}

/* 主题2: 薄荷青 */
.theme2 {
  --dark-bg: var(--theme2-dark-bg);
  --card-bg: var(--theme2-card-bg);
  background: var(--theme2-gradient);
}

/* 薄荷青主题导航栏 */
.theme2 .custom-nav-bar {
  background: var(--theme2-dark-bg);
}

/* 主题4: 海洋蓝 */
.theme4 {
  --dark-bg: var(--theme4-dark-bg);
  --card-bg: var(--theme4-card-bg);
  background: var(--theme4-gradient);
}

/* 海洋蓝主题导航栏 */
.theme4 .custom-nav-bar {
  background: var(--theme4-dark-bg);
}

.about-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.07)"/></svg>');
  opacity: 0.15;
  pointer-events: none;
  will-change: transform;
}


/* 关于我们卡片样式 */
.about-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 30rpx;
  margin: 20rpx 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

/* 调整第一个卡片的顶部边距 */
.about-card:first-of-type {
  margin-top: 20rpx;
}

/* 调整最后一个卡片的底部边距 */
.about-card:last-of-type {
  margin-bottom: 40rpx;
}

/* 卡片标题和内容 */
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.card-content {
  padding: 6rpx 0;
}

/* 内容块 */
.content-block {
  margin-bottom: 24rpx;
  padding-left: 10rpx;
}

.content-block:last-child {
  margin-bottom: 10rpx;
}

.block-title {
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8rpx;
  position: relative;
  padding-left: 20rpx;
}

.block-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 24rpx;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  border-radius: 4rpx;
}

.block-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  padding-left: 20rpx;
}

/* 愿景卡片 */
.vision-card {
  background: rgba(255, 255, 255, 0.12);
}

.vision-highlight {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.6;
  text-align: center;
  font-style: italic;
}

.vision-points {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.vision-point {
  display: flex;
  align-items: flex-start;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12rpx;
  padding: 16rpx;
}

.point-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.point-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  flex: 1;
}

/* 价值观卡片 */
.values-card {
  background: rgba(255, 255, 255, 0.08);
}

.values-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.value-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.value-name {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8rpx;
  position: relative;
}

.value-name::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 2rpx;
}

.value-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 联系我们部分 */
.contact-section {
  margin: 20rpx 30rpx 40rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.contact-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.contact-desc {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30rpx;
}

.contact-btn {
  padding: 16rpx 40rpx;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
} 