// 反馈与建议页面逻辑
const app = getApp();

Page({
  data: {
    feedbackContent: '',
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 44, // 默认导航栏高度
    currentTheme: 'theme1',
    wechatContacts: ['yanjuncv', 'wowolongbillion'],
    submitDisabled: true // 提交按钮是否禁用
  },

  onLoad: function() {
    // 获取系统信息，设置状态栏高度
    this.getSystemInfo();
    
    // 获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('反馈与建议页面加载，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
    
    // 注册主题变化回调
    if (app.themeChangeCallbacks) {
      // 添加当前页面的主题更新回调
      app.themeChangeCallbacks.push(this.onThemeChanged.bind(this));
    }
  },
  
  onShow: function() {
    // 页面显示时获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('反馈与建议页面显示，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  // 主题变化回调
  onThemeChanged: function() {
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('主题变化回调触发，新主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  onUnload: function() {
    // 页面卸载时移除回调
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback => 
        callback.toString() === this.onThemeChanged.bind(this).toString());
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
      }
    }
  },
  
  // 获取系统信息，设置状态栏高度
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        navBarHeight: 44 // 导航栏固定高度
      });
      console.log('状态栏高度:', systemInfo.statusBarHeight);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 输入反馈内容
  onFeedbackInput: function(e) {
    const content = e.detail.value;
    this.setData({
      feedbackContent: content,
      submitDisabled: !content.trim() // 内容为空时禁用提交按钮
    });
  },
  
  // 复制微信号
  copyWechatID: function(e) {
    const { id } = e.currentTarget.dataset;
    const wechatID = this.data.wechatContacts[id];
    
    wx.setClipboardData({
      data: wechatID,
      success: () => {
        wx.showToast({
          title: '已复制微信号',
          icon: 'success'
        });
      }
    });
  },
  
  // 提交反馈
  submitFeedback: function() {
    // 检查是否输入了反馈内容
    if (!this.data.feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '提交中...'
    });
    
    // 调用云函数提交反馈
    wx.cloud.callFunction({
      name: 'submitFeedback',
      data: {
        content: this.data.feedbackContent
      },
      success: res => {
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          wx.showToast({
            title: '反馈提交成功',
            icon: 'success'
          });
          
          // 清空输入
          this.setData({
            feedbackContent: '',
            submitDisabled: true
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.result.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('提交反馈失败', err);
        
        wx.showToast({
          title: '提交失败，请稍后再试',
          icon: 'none'
        });
      }
    });
  }
}); 