/* 反馈与建议页面样式 */
.feedback-container {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

.status-bar-height {
  width: 100%;
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 30rpx;
}

.nav-bar-back {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
  color: #ffffff;
  font-weight: 500;
}

.nav-bar-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  flex: 1;
}

/* 导航栏占位符，防止内容被导航栏遮挡 */
.nav-bar-placeholder {
  width: 100%;
}

/* 主题1: 天空蓝 */
.theme1 {
  --dark-bg: var(--theme1-dark-bg);
  --card-bg: var(--theme1-card-bg);
  background: var(--theme1-gradient);
}

/* 天空蓝主题导航栏 */
.theme1 .custom-nav-bar {
  background: var(--theme1-dark-bg);
}

/* 主题2: 薄荷青 */
.theme2 {
  --dark-bg: var(--theme2-dark-bg);
  --card-bg: var(--theme2-card-bg);
  background: var(--theme2-gradient);
}

/* 薄荷青主题导航栏 */
.theme2 .custom-nav-bar {
  background: var(--theme2-dark-bg);
}

/* 主题4: 海洋蓝 */
.theme4 {
  --dark-bg: var(--theme4-dark-bg);
  --card-bg: var(--theme4-card-bg);
  background: var(--theme4-gradient);
}

/* 海洋蓝主题导航栏 */
.theme4 .custom-nav-bar {
  background: var(--theme4-dark-bg);
}

.feedback-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.07)"/></svg>');
  opacity: 0.15;
  pointer-events: none;
  will-change: transform;
}

/* 反馈表单 */
.feedback-form {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 30rpx;
  margin: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.form-title {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 30rpx;
  font-weight: 500;
  text-align: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.item-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
}

.feedback-textarea {
  width: 100%;
  height: 240rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  box-sizing: border-box;
  color: #fff;
  font-size: 28rpx;
  line-height: 1.5;
}

.text-counter {
  text-align: right;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 10rpx;
}

.contact-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  color: #fff;
  font-size: 28rpx;
}

.contact-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 10rpx;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
}

.submit-button {
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
  margin-top: 20rpx;
  width: 100%;
  transition: all 0.3s ease;
}

.submit-button.disabled {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.6);
  box-shadow: none;
}

.submit-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
}

/* 联系我们部分 */
.contact-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 30rpx;
  font-weight: 500;
  text-align: center;
}

.contact-options {
  margin-bottom: 20rpx;
}

.wechat-contact {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.wechat-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
}

.wechat-id-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wechat-id {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 15rpx 20rpx;
  color: #fff;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-icon {
  font-size: 24rpx;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
}

.contact-tips {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.8;
  margin-top: 20rpx;
} 