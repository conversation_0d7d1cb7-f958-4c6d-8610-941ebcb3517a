// 获取应用实例
const app = getApp()

// 主题配置
const themes = {
  theme1: {
    darkBg: '#4C5D99',
    cardBg: 'rgba(85, 105, 155, 0.85)',
    gradient: 'linear-gradient(145deg, #4C5D99, #6275B3)',
    name: '天空蓝',
    textColor: '#ffffff',
    borderColor: 'rgba(255, 255, 255, 0.1)'
  },
  theme2: {
    darkBg: '#4A7A8C',
    cardBg: 'rgba(80, 135, 145, 0.85)',
    gradient: 'linear-gradient(145deg, #4A7A8C, #5E9CAF)',
    name: '薄荷青',
    textColor: '#ffffff',
    borderColor: 'rgba(255, 255, 255, 0.1)'
  },
  theme4: {
    darkBg: '#3D6B96',
    cardBg: 'rgba(70, 115, 160, 0.85)',
    gradient: 'linear-gradient(145deg, #3D6B96, #5284B4)',
    name: '海洋蓝',
    textColor: '#ffffff',
    borderColor: 'rgba(255, 255, 255, 0.1)'
  }
};

Page({
  data: {
    currentPetType: 'dog', // 当前选中的宠物类型（默认为dog）
    showBreedGrid: false, // 品种宫格是否显示
    showThemePanel: false, // 主题面板是否显示
    showThemeTip: false, // 主题切换提示是否显示
    currentTheme: 'theme1', // 当前主题，默认为天空蓝
    themeStyle: {
      background: 'var(--gradient-primary)',
      backgroundDark: 'var(--dark-bg)',
      cardBg: 'var(--card-bg)'
    },
    dogBreeds: [
      {
        id: 1,
        name: '拉布拉多',
        icon: '🦮',
      },
      {
        id: 2,
        name: '金毛寻回',
        icon: '🐕‍🦺',
      },
      {
        id: 3,
        name: '哈士奇',
        icon: '🐺',
      },
      {
        id: 4,
        name: '边境牧羊',
        icon: '🐩',
      },
      {
        id: 5,
        name: '贵宾(泰迪)',
        icon: '🐕',
      },
      {
        id: 6,
        name: '柯基',
        icon: '🐶',
      },
      {
        id: 7,
        name: '吉娃娃',
        icon: '🐕',
      },
      {
        id: 8,
        name: '法国斗牛',
        icon: '🐶',
      },
      {
        id: 9,
        name: '比熊',
        icon: '🦮',
      }
    ],
    catBreeds: [
      {
        id: 1,
        name: '狸花猫',
        icon: '😺',
      },
      {
        id: 2,
        name: '黄狸猫',
        icon: '😸',
      },
      {
        id: 3,
        name: '玄猫',
        icon: '🐈',
      },
      {
        id: 4,
        name: '三花猫',
        icon: '😽',
      },
      {
        id: 5,
        name: '奶牛猫',
        icon: '😺',
      },
      {
        id: 6,
        name: '英国短毛',
        icon: '😼',
      },
      {
        id: 7,
        name: '美国短毛',
        icon: '🐈',
      },
      {
        id: 8,
        name: '布偶',
        icon: '😻',
      },
      {
        id: 9,
        name: '暹罗',
        icon: '🐱',
      }
    ]
  },
  onLoad: function() {
    // 设置默认主题为天空蓝
    const defaultTheme = 'theme1';
    
    // 检查并获取保存的主题，如果没有则使用默认主题
    let currentTheme = wx.getStorageSync('currentTheme') || defaultTheme;
    
    // 如果是已移除的主题，重置为默认主题（天空蓝）
    if (currentTheme === 'theme3' || currentTheme === 'theme5') {
      currentTheme = defaultTheme;
      wx.setStorageSync('currentTheme', currentTheme);
    }
    
    // 立即更新全局数据
    app.globalData.currentTheme = currentTheme;
    app.globalData.themeConfig = themes[currentTheme];
    
    // 更新页面数据
    this.setData({
      currentTheme: currentTheme,
      themeStyle: {
        background: themes[currentTheme].gradient,
        backgroundDark: themes[currentTheme].darkBg,
        cardBg: themes[currentTheme].cardBg
      }
    });
    
    // 应用主题
    this.applyTheme(currentTheme);
    
    // 注册主题变化回调
    this.registerThemeCallback();
    
    // 继续执行其他onLoad逻辑
  },
  
  // 注册主题变化回调
  registerThemeCallback: function() {
    // 先移除可能存在的旧回调，避免重复
    this.unregisterThemeCallback();
    
    // 添加新的回调
    if (app.themeChangeCallbacks) {
      app.themeChangeCallbacks.push(this.onThemeChanged.bind(this));
      console.log('首页：已注册主题变化回调');
    }
  },
  
  // 移除主题变化回调
  unregisterThemeCallback: function() {
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback => 
        callback.toString() === this.onThemeChanged.bind(this).toString());
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
        console.log('首页：已移除主题变化回调');
      }
    }
  },
  
  // 页面显示时，确保重新注册主题回调
  onShow: function() {
    // 重新注册主题回调
    this.registerThemeCallback();
    
    // 从全局获取当前主题并应用
    const currentTheme = app.globalData.currentTheme || 'theme1';
    if (currentTheme !== this.data.currentTheme) {
      console.log('首页显示：检测到主题变化，从', this.data.currentTheme, '到', currentTheme);
      this.applyTheme(currentTheme);
    }
  },
  
  // 页面隐藏时，可以选择是否移除回调
  onHide: function() {
    // 在这里不移除回调，避免出现主题更新丢失的问题
    // 通过在onShow中重新注册以确保回调正常工作
  },
  
  // 主题变化回调
  onThemeChanged: function() {
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('首页：主题变化回调触发，新主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
    
    // 应用主题
    this.applyTheme(currentTheme);
  },
  
  onUnload: function() {
    // 页面卸载时移除回调
    this.unregisterThemeCallback();
  },
  
  // 显示犬类品种宫格
  showDogBreeds: function() {
    app.globalData.breedType = 'dog';
    this.setData({
      currentPetType: 'dog',
      showBreedGrid: true,
      showThemePanel: false // 确保关闭主题面板
    });
  },
  
  // 显示猫类品种宫格
  showCatBreeds: function() {
    app.globalData.breedType = 'cat';
    this.setData({
      currentPetType: 'cat',
      showBreedGrid: true,
      showThemePanel: false // 确保关闭主题面板
    });
  },
  
  // 关闭品种宫格
  closeBreedGrid: function() {
    this.setData({
      showBreedGrid: false
    });
  },
  
  // 防止事件冒泡
  preventBubble: function(e) {
    // 阻止事件冒泡，防止触发closeBreedGrid
  },
  // 选择品种并跳转到视觉模拟页面
  selectBreed: function(e) {
    const breed = e.currentTarget.dataset.breed;
    
    // 根据当前宠物类型设置category属性
    const breedWithCategory = {
      ...breed,
      category: app.globalData.breedType // 使用全局数据中的breedType
    };
    
    app.globalData.selectedBreed = breedWithCategory;
    console.log('选择的品种信息:', breedWithCategory);
    
    wx.navigateTo({
      url: '/pages/vision-simulation/vision-simulation'
    });
  },
  // 主题相关函数
  toggleThemePanel: function() {
    const willShow = !this.data.showThemePanel;
    
    // 隐藏主题切换提示
    this.setData({
      showThemeTip: false
    });
    
    // 使用setTimeout确保 UI更新有时间执行
    setTimeout(() => {
      this.setData({
        showThemePanel: willShow
      });
      
      // 如果关闭面板，添加额外延迟
      if (!willShow) {
        setTimeout(() => {
          console.log('主题面板已关闭');
        }, 300);
      }
    }, 50);
  },
  
  changeTheme: function(e) {
    const theme = e.currentTarget.dataset.theme;
    this.setData({
      currentTheme: theme,
      showThemePanel: false
    });
    
    // 使用全局主题管理机制切换主题
    app.switchTheme(theme);
    
    // 应用主题到当前页面
    this.applyTheme(theme);
  },
  
  // 隐藏主题提示
  hideThemeTip: function(e) {
    // 阻止事件冒泡，防止触发toggleThemePanel
    e.stopPropagation();
    
    this.setData({
      showThemeTip: false
    });
  },
  
  applyTheme: function(theme) {
    const themeConfig = themes[theme];
    if (!themeConfig) return;
    
    // 更新当前主题
    this.setData({ currentTheme: theme });
    
    // 应用主题变量到CSS
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: themeConfig.darkBg,
      animation: {
        duration: 300,
        timingFunc: 'easeIn'
      }
    });
    
    // 通过样式变量动态修改全局样式
    this.updateThemeStyle(theme);
  },
  
  updateThemeStyle: function(theme) {
    // 微信小程序环境下更新主题
    const themeConfig = themes[theme];
    
    // 设置当前页面的主题样式
    wx.nextTick(() => {
      // 更新全局变量
      app.globalData.currentTheme = theme;
      app.globalData.themeConfig = themeConfig;
      
      // 检查是否为浅色系主题
      const isLightTheme = theme === 'theme6' || theme === 'theme7' || theme === 'theme8';
      
      // 直接更新页面数据，触发样式更新
      this.setData({
        themeStyle: {
          background: themeConfig.gradient,
          backgroundDark: themeConfig.darkBg,
          cardBg: themeConfig.cardBg,
          textColor: themeConfig.textColor || '#ffffff',
          borderColor: themeConfig.borderColor || 'rgba(255, 255, 255, 0.2)',
          isLightTheme: isLightTheme
        },
        isLightTheme: isLightTheme // 将浅色主题标志添加到页面数据中，便于在WXML中使用
      });
      
      // 如果是浅色系主题，修改状态栏文字颜色
      if (isLightTheme) {
        wx.setNavigationBarColor({
          frontColor: '#000000',
          backgroundColor: themeConfig.darkBg
        });
      }
    });
  },
  
  // 分享小程序给朋友
  onShareAppMessage: function() {
    // 构建分享信息
    let title = '爱宠看世界 - 体验犬猫眼中的世界';
    
    return {
      title: title,
      path: '/pages/index/index',
      imageUrl: '/images/share-image.png', // 分享图片，需要确保这个图片存在
      success: function(res) {
        // 分享成功回调
        console.log('分享成功:', res);
      },
      fail: function(res) {
        // 分享失败回调
        console.log('分享失败:', res);
      }
    };
  },
  
  // 分享到朋友圈
  onShareTimeline: function() {
    return {
      title: '爱宠看世界 - 看见犬猫眼中的世界',
      query: '',
      imageUrl: '/images/share-image.png'
    };
  }
}) 