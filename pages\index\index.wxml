<view class="container welcome-screen" style="background: {{themeStyle.background || 'var(--gradient-primary)'}}">
  <view class="welcome-content {{isLightTheme ? 'light-theme-content' : ''}}">
    <view class="main-content">
      <view class="logo-container">
        <view class="logo-wrapper">
          <view class="logo">
            <text class="dog-icon">🐶</text>
            <text class="cat-icon">🐱</text>
            <text class="paw-icon">🐾</text>
          </view>
          <view class="tech-circles"></view>
        </view>
        
        <view class="title-container">
          <view class="welcome-title {{isLightTheme ? 'light-theme-text' : ''}}">爱宠看世界</view>
        </view>
        
        <view class="welcome-subtitle {{isLightTheme ? 'light-theme-subtitle' : ''}}">
          <text class="tech-text">探索</text>
          <text>爱宠眼中的奇妙世界</text>
        </view>
      </view>
    </view>
    
    <view class="selection-area">  
      <!-- 探索引导提示 -->
      <view class="explore-guide-simple">
        <view class="guide-icon">🐾</view>
        <view class="guide-text">点击下方品种卡片，探索爱宠视角模拟</view>
        <view class="guide-arrow">↓</view>
      </view>
    
      <!-- 宠物类型选择区 - 同一行布局 -->
      <view class="pet-type-selection">
        <!-- 犬类选择卡片 -->
        <view class="pet-type-card {{currentPetType === 'dog' ? 'active-card' : ''}}" bindtap="showDogBreeds">
          <view class="pet-card-icon">🐶</view>
          <view class="pet-card-title {{isLightTheme ? 'light-theme-text' : ''}}">汪汪品种</view>
          <view class="pet-card-arrow">→</view>
        </view>
        
        <!-- 猫科选择卡片 -->
        <view class="pet-type-card {{currentPetType === 'cat' ? 'active-card' : ''}}" bindtap="showCatBreeds">
          <view class="pet-card-icon">😺</view>
          <view class="pet-card-title {{isLightTheme ? 'light-theme-text' : ''}}">喵喵品种</view>
          <view class="pet-card-arrow">→</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 品种选择弹窗 -->
  <view class="breed-popup-mask" wx:if="{{showBreedGrid}}" bindtap="closeBreedGrid"></view>
  <view class="breed-popup-container {{showBreedGrid ? 'show' : ''}} {{isLightTheme ? 'light-theme-popup' : ''}}" catchtap="preventBubble" style="background: {{themeStyle.cardBg || 'rgba(40, 44, 52, 0.95)'}}">
    <view class="breed-popup-header">
      <view class="breed-popup-title">
        <text class="breed-popup-icon">{{currentPetType === 'dog' ? '🐶' : '😺'}}</text>
        <text class="{{isLightTheme ? 'light-theme-text' : ''}}">{{currentPetType === 'dog' ? '汪汪品种选择' : '喵喵品种选择'}}</text>
      </view>
      <view class="breed-popup-close" bindtap="closeBreedGrid">×</view>
    </view>
    
    <!-- 品种计数提示 -->
    <view class="breed-count-tip {{isLightTheme ? 'light-theme-count-tip' : ''}}">
      <view class="count-badge">{{currentPetType === 'dog' ? '9' : '9'}}</view>
      <text>种{{currentPetType === 'dog' ? '汪星人' : '喵星人'}}视角</text>
      <view class="update-badge">
        <text class="update-text">持续更新中</text>
      </view>
    </view>
    
    <!-- 犬类品种宫格 -->
    <view class="breed-grid" wx:if="{{currentPetType === 'dog'}}">
      <block wx:for="{{dogBreeds}}" wx:key="id">
        <view class="breed-grid-item" bindtap="selectBreed" data-breed="{{item}}">
          <view class="breed-grid-img">
            <text>{{item.icon}}</text>
            <view class="tech-ring"></view>
          </view>
          <view class="breed-grid-name {{isLightTheme ? 'light-theme-text' : ''}}">{{item.name}}</view>
        </view>
      </block>
    </view>
    
    <!-- 猫科品种宫格 -->
    <view class="breed-grid" wx:if="{{currentPetType === 'cat'}}">
      <block wx:for="{{catBreeds}}" wx:key="id">
        <view class="breed-grid-item" bindtap="selectBreed" data-breed="{{item}}">
          <view class="breed-grid-img">
            <text>{{item.icon}}</text>
            <view class="tech-ring"></view>
          </view>
          <view class="breed-grid-name {{isLightTheme ? 'light-theme-text' : ''}}">{{item.name}}</view>
        </view>
      </block>
    </view>
  </view>
</view>