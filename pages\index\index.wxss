/* 首页样式 */
.welcome-screen {
  background: var(--gradient-primary);
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 0 40rpx 0; /* 修改：减少上下内边距，上部边距小于下部 */
  position: relative;
  overflow: hidden;
}

.welcome-screen::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.07)"/></svg>');
  opacity: 0.15;
  pointer-events: none;
  will-change: transform; /* 优化性能 */
}

.welcome-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 修改：将内容向上对齐 */
  padding: 0 20rpx; /* 添加：小的左右内边距，让页面整体有呼吸空间 */
  box-sizing: border-box; /* 确保padding计入宽度 */
}

/* 移除不再使用的app-header样式 */

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.selection-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto; /* 添加：使选择区域自动占据剩余空间 */
  margin-bottom: 120rpx; /* 增加：让选择区域距离底部更远 */
  transform: translateY(-80rpx); /* 添加：整体向上移动 */
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.logo-wrapper {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  will-change: transform; /* 优化动画性能 */
}

.logo text {
  position: absolute;
  font-size: 80rpx;
  color: var(--primary-color);
  text-shadow: 0 0 40rpx rgba(255, 138, 91, 0.6);
}

.dog-icon {
  animation: petAnimation 6s infinite ease-in-out;
  transform-origin: center;
  opacity: 0;
}

.cat-icon {
  animation: petAnimation 6s infinite ease-in-out 2s;
  transform-origin: center;
  opacity: 0;
}

.paw-icon {
  animation: petAnimation 6s infinite ease-in-out 4s;
  transform-origin: center;
  opacity: 0;
  font-size: 100rpx !important;
}

@keyframes petAnimation {
  0% { transform: scale(0.6); opacity: 0; }
  5% { transform: scale(1.1); opacity: 1; }
  10% { transform: scale(1); opacity: 1; }
  28% { transform: scale(1); opacity: 1; }
  33% { transform: scale(0.6); opacity: 0; }
  100% { transform: scale(0.6); opacity: 0; }
}

/* 科技风格元素 */
.tech-circles {
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 138, 91, 0.4);
  box-shadow: 0 0 40rpx rgba(255, 138, 91, 0.25);
  z-index: 1;
  overflow: hidden;
}

.tech-circles::before,
.tech-circles::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.tech-circles::before {
  width: 140rpx;
  height: 140rpx;
  border: 2rpx solid rgba(255, 138, 91, 0.6);
  animation: rotateSlow 20s linear infinite;
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 140rpx;
  height: 140rpx;
  border: 2rpx solid rgba(255, 138, 91, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: rotateSlow 20s linear infinite;
  box-shadow: inset 0 0 20rpx rgba(255, 138, 91, 0.2);
}

.tech-circles::after {
  width: 220rpx;
  height: 220rpx;
  border: 1rpx dashed rgba(255, 138, 91, 0.35);
  animation: rotateReverse 30s linear infinite;
}

/* 优化动画性能的柔和脉动 */
@keyframes gentlePulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes rotateSlow {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes rotateReverse {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(-360deg); }
}

/* 标题容器 */
.title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  position: relative;
  width: 100%;
}

.welcome-title {
  font-size: 60rpx;
  font-weight: bold;
  text-align: center;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color)); /* 主题渐变色 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

/* 主题切换容器 - 已移除，不再使用 */

/* 主题切换按钮包装器 */
.theme-switch-wrapper {
  position: relative;
  margin-left: 20rpx;
  z-index: 1003;
  transition: opacity 0.3s, visibility 0.3s;
}

/* 品种选择时隐藏主题切换按钮 */
.hidden-theme-switch {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  z-index: 999; /* 确保在弹窗下方 */
}

/* 主题切换按钮 */
.theme-switch {
  position: relative;
  width: 60rpx; /* 调整大小 */
  height: 60rpx; /* 调整大小 */
  border-radius: 50%;
  background: rgba(255, 138, 91, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
  z-index: 1001; /* 确保按钮在提示上方 */
}

.theme-switch:active {
  transform: scale(0.92);
  background: rgba(255, 138, 91, 0.3); /* 点击时增加背景透明度 */
}

.theme-icon {
  font-size: 36rpx; /* 增加图标大小 */
}

/* 主题切换提示样式 */
.theme-tip {
  position: absolute;
  left: 70rpx; /* 从按钮的右侧显示 */
  top: 50%;
  transform: translate(0, -50%);
  opacity: 0;
  transition: all 0.5s ease;
  pointer-events: auto;
  z-index: 1000;
}

.theme-tip-show {
  opacity: 1;
  transform: translate(0, -50%);
  pointer-events: auto;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.theme-tip-content {
  background: rgba(255, 138, 91, 0.95);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.25);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  width: max-content;
}

.theme-tip-arrow {
  position: absolute;
  left: -12rpx; /* 从左侧显示 */
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-right: 12rpx solid rgba(255, 138, 91, 0.95); /* 改为右边框 */
  border-left: none;
}

.panel-arrow {
  position: absolute;
  width: 0;
  height: 0;
  bottom: -8rpx;
  right: 100rpx;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid rgba(40, 44, 52, 0.95);
  border-bottom: none;
}

.theme-tip-icon {
  font-size: 28rpx; /* 减小图标大小 */
  margin-right: 8rpx; /* 减小间距 */
}

.theme-tip-text {
  color: #ffffff;
  font-size: 24rpx; /* 减小字体大小 */
  font-weight: 600;
  white-space: nowrap;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 添加伪元素扩大点击区域 */
.theme-switch::after {
  content: '';
  position: absolute;
  top: -15rpx;
  left: -15rpx;
  right: -15rpx;
  bottom: -15rpx;
}

/* 主题切换面板 */
.theme-panel {
  position: absolute;
  top: 60rpx;
  right: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 16rpx;
  width: 320rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-10rpx);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 100;
}

.theme-panel-show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
  z-index: 1005;
}

.theme-panel-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 12rpx;
  padding-bottom: 10rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-options-grid {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 10rpx;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  border-radius: 10rpx;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.1);
  flex: 1;
}

.theme-option:active {
  transform: scale(0.95);
}

.theme-option.active {
  background: rgba(255, 255, 255, 0.15);
}

.color-preview {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 浅色系主题特殊样式 */
[data-theme="theme6"] .color-preview,
[data-theme="theme7"] .color-preview,
[data-theme="theme8"] .color-preview {
  border: 2rpx solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.8);
}

.theme-option text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4rpx;
  white-space: nowrap;
}

.welcome-subtitle {
  font-size: 34rpx;
  text-align: center;
  margin-bottom: 10rpx; /* 进一步减少底部间距 */
  color: rgba(255, 255, 255, 0.95); /* 增加文字亮度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 探索引导提示样式 - 原始版本 */
.explore-guide {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20rpx auto 20rpx;
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16rpx;
  width: 80%;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(20rpx);
}

/* 简化版探索引导提示样式 - 无边框 */
.explore-guide-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
  padding: 10rpx 0;
  width: 80%;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(20rpx);
}

/* 浅色主题引导提示 */
.light-theme-guide {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.guide-icon {
  font-size: 36rpx;
  margin-bottom: 6rpx;
  color: #FF8A5B;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  animation: float 2s ease-in-out infinite;
}

.guide-text {
  font-size: 26rpx;
  color: var(--text-primary, rgba(255, 255, 255, 0.95));
  text-align: center;
  margin-bottom: 4rpx;
  font-weight: 500;
  line-height: 1.3;
}

.guide-arrow {
  font-size: 28rpx;
  color: #FF8A5B;
  animation: bounce 1.5s ease infinite;
  margin-top: 2rpx;
  display: block;
}

/* 浅色主题文字颜色 */
.light-theme-guide .guide-text {
  color: rgba(60, 60, 60, 0.9);
}

/* 淡入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 上下弹跳动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 浮动动画 */
@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5rpx);
  }
  100% {
    transform: translateY(0);
  }
}

.tech-text {
  color: var(--primary-color);
  background: rgba(255, 138, 91, 0.2);
  padding: 4rpx 14rpx;
  border-radius: 20rpx;
  margin-right: 8rpx;
  font-weight: 500;
}

/* 宠物类型选择区样式 - 同一行布局 */
.pet-type-selection {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20rpx;
  padding: 0 40rpx;
  margin-top: 0;
  margin-bottom: 0; /* 移除底部间距 */
  width: 100%;
  box-sizing: border-box;
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

.pet-type-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 30rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  flex: 1;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  will-change: transform, box-shadow, background;
  min-height: 130rpx;
}

/* 卡片悬停效果 */
.pet-type-card:active {
  transform: scale(0.98) translateZ(0);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 138, 91, 0.3);
}

/* 激活卡片样式 */
.active-card {
  background: rgba(255, 138, 91, 0.15);
  border-color: rgba(255, 138, 91, 0.3);
}

/* 卡片光效果 */
.pet-type-card::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 120%;
  height: 120%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 1;
  pointer-events: none;
}

.pet-type-card:active::after {
  top: 100%;
  left: 100%;
}

.pet-card-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.pet-card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  flex: 1;
}

.pet-card-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: auto;
}

.pet-card-arrow {
  font-size: 36rpx;
  color: rgba(255, 138, 91, 0.9);
  margin-left: 16rpx;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.pet-type-card:active .pet-card-arrow {
  transform: translateX(10rpx);
}

/* 品种弹窗样式 */
.breed-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 1100; /* 提高z-index值，确保覆盖主题面板 */
  transition: all 0.3s ease;
}

.breed-popup-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.95);
  border-radius: 30rpx 30rpx 0 0;
  padding: 45rpx 35rpx; /* 增大内边距 */
  z-index: 1101; /* 提高z-index值，确保在mask上方 */
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.3);
  transform: translateY(100%);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  max-height: 80vh;
  overflow-y: auto;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色主题弹窗样式 */
.light-theme-popup {
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.15);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.breed-popup-container.show {
  transform: translateY(0);
}

/* 品种计数提示样式 */
.breed-count-tip {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx; /* 增大下边距 */
  font-size: 28rpx; /* 增大字体 */
  color: rgba(255, 255, 255, 0.7);
}

.light-theme-popup .breed-count-tip {
  color: rgba(60, 60, 60, 0.7);
}

.count-badge {
  background: rgba(255, 138, 91, 0.9);
  color: white;
  border-radius: 20rpx;
  padding: 6rpx 14rpx; /* 增大内边距 */
  font-size: 26rpx; /* 增大字体 */
  font-weight: bold;
  margin-right: 12rpx; /* 增大右边距 */
}

.update-badge {
  margin-left: 18rpx; /* 增大左边距 */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 6rpx 14rpx; /* 增大内边距 */
  font-size: 24rpx; /* 增大字体 */
}

.light-theme-popup .update-badge {
  background: rgba(0, 0, 0, 0.05);
}

.update-text {
  color: rgba(255, 255, 255, 0.8);
}

.light-theme-popup .update-text {
  color: rgba(60, 60, 60, 0.8);
}

/* 弹窗标题样式 */
.breed-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme-popup .breed-popup-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.breed-popup-title {
  display: flex;
  align-items: center;
  font-size: 38rpx; /* 增大字体 */
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
}

.light-theme-popup .breed-popup-title text:not(.breed-popup-icon) {
  color: rgba(60, 60, 60, 0.9);
}

.breed-popup-icon {
  margin-right: 16rpx;
  font-size: 44rpx; /* 增大图标字体 */
}

.breed-popup-close {
  width: 70rpx; /* 增大宽度 */
  height: 70rpx; /* 增大高度 */
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 46rpx; /* 增大关闭图标 */
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.light-theme-popup .breed-popup-close {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(60, 60, 60, 0.7);
}

.breed-popup-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

.light-theme-popup .breed-popup-close:active {
  background: rgba(0, 0, 0, 0.1);
}

/* 品种宫格样式 */
.breed-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx; /* 增大间距 */
  padding: 15rpx 10rpx; /* 增大内边距 */
  margin-top: 25rpx; /* 增大上边距 */
  animation: fadeIn 0.5s ease-out forwards;
}

/* 品种卡片样式 */
.breed-grid-item {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 24rpx; /* 增大圆角 */
  padding: 28rpx 18rpx; /* 增大内边距 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  will-change: transform, box-shadow, background;
  height: 160rpx; /* 增大高度 */
}

.breed-grid-name {
  font-size: 32rpx; /* 增大字体 */
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-top: 20rpx; /* 增大上边距 */
  text-align: center;
  line-height: 1.3;
}

.light-theme-popup .breed-grid-name {
  color: rgba(60, 60, 60, 0.9);
}

.breed-grid-img {
  width: 100rpx; /* 增大宽度 */
  height: 100rpx; /* 增大高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.breed-grid-img text {
  font-size: 52rpx; /* 增大字体 */
  z-index: 2;
}

.tech-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 138, 91, 0.3);
  animation: pulse 2s infinite;
}

.light-theme-popup .tech-ring {
  border-color: rgba(255, 138, 91, 0.4);
}

.light-theme-popup .breed-grid-item {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 品种卡片悬停效果 */
.breed-grid-item:active {
  transform: scale(0.95) translateZ(0);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 138, 91, 0.3);
}

.light-theme-popup .breed-grid-item:active {
  background: rgba(0, 0, 0, 0.08);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 品种卡片光效果 */
.breed-grid-item::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 120%;
  height: 120%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 1;
  pointer-events: none;
}

.breed-grid-item:active::after {
  top: 100%;
  left: 100%;
}

/* 品种特性样式 */
.breed-grid-feature {
  font-size: 22rpx;
  color: rgba(255, 138, 91, 0.9);
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.light-theme-popup .breed-grid-feature {
  color: rgba(255, 138, 91, 1);
}

.feature-dot {
  margin-right: 4rpx;
  color: rgba(255, 138, 91, 0.9);
}

.pet-option {
  width: 48%; /* 设置固定宽度百分比 */
  height: 300rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.pet-option::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  transition: 0.5s;
}

.pet-option:active {
  transform: scale(0.95);
}

.option-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
  color: var(--primary-color);
  transition: all 0.3s;
}

.option-text {
  font-size: 32rpx;
  color: var(--text-primary);
}

/* 弹窗相关样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.65);
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px); /* iOS支持 */
  animation: fadeIn 0.25s ease;
  will-change: opacity; /* 优化过渡性能 */
}

.popup-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 80vh;
  background: linear-gradient(to bottom right, #3A4980, #303D6E);
  border-radius: 24rpx;
  z-index: 1001;
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.5);
  overflow: hidden;
  animation: scaleIn 0.25s ease;
  border: 1px solid rgba(255, 138, 91, 0.15);
  will-change: transform, opacity; /* 优化性能 */
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 138, 91, 0.2);
  background: rgba(255, 138, 91, 0.08);
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary, #FFFFFF);
  display: flex;
  align-items: center;
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
  color: var(--text-secondary, #aaaaaa);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.2s;
}

.popup-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.popup-content {
  max-height: calc(80vh - 100rpx);
  padding: 30rpx;
}

.breed-card {
  background: rgba(43, 58, 103, 0.6);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.breed-card:active {
  transform: translateX(10rpx);
  box-shadow: -10rpx 0 30rpx rgba(255, 138, 91, 0.2);
  border-color: var(--primary-color);
  background: rgba(43, 58, 103, 0.8);
}

.breed-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 138, 91, 0.1);
  margin-right: 24rpx;
  position: relative;
  font-size: 60rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.breed-arrow {
  margin-left: auto;
  font-size: 40rpx;
  color: var(--primary-color);
  opacity: 0.7;
  padding: 0 10rpx;
}

.breed-card:active .breed-arrow {
  transform: translateX(4rpx);
  opacity: 1;
}

.breed-info {
  flex: 1;
  overflow: hidden;
}

.breed-name {
  font-size: 38rpx;
  font-weight: bold;
  color: var(--text-primary, #FFFFFF);
  margin-bottom: 12rpx;
}

.breed-desc {
  font-size: 30rpx;
  color: var(--text-secondary, #aaaaaa);
  line-height: 1.5;
}

.breed-feature {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.feature-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: var(--primary-color);
}

/* 选项卡样式 */
.selection-tabs {
  display: flex;
  width: 100%; /* 修改：占据100%宽度 */
  border-radius: 24rpx;
  background: rgba(0, 0, 0, 0.15);
  padding: 20rpx;
  margin: 0 auto 30rpx;
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-sizing: border-box;
}

.tab {
  flex: 1;
  height: 80rpx; /* 修改：调整高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15rpx;
  transition: all 0.3s;
  position: relative;
  margin: 0 8rpx; /* 添加：保持左右间距 */
}

.tab.active {
  color: white;
  background: rgba(255, 138, 91, 0.15);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-indicator {
  position: absolute;
  bottom: 10rpx; /* 修改：调整指示器位置 */
  left: 50%;
  width: 0;
  height: 0;
  transform: translateX(-50%);
  transition: all 0.3s ease;
}

.tab.active .tab-indicator {
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 4rpx;
}

.tab-icon {
  font-size: 36rpx; /* 修改：略微减小图标大小 */
  margin-right: 10rpx;
  transition: all 0.3s ease;
}

.tab.active .tab-icon {
  transform: translateY(-2rpx) scale(1.1);
}

.tab-text {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
}

.tab.active .tab-text {
  color: white;
  font-weight: 500;
}

/* 品种宫格样式 */
.breed-grid-container {
  width: 100%; /* 修改：占据100%宽度 */
  margin: 20rpx auto 0;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-sizing: border-box; /* 确保padding计入宽度 */
}

.breed-grid-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx; /* 减少底部间距，因为下面有计数提示 */
  color: rgba(255, 255, 255, 0.95);
  font-size: 36rpx;
  font-weight: 500;
  padding: 10rpx 0; /* 添加上下内边距 */
}

/* 品种计数提示样式 */
.breed-count-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  padding: 8rpx 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 浅色主题计数提示 */
.light-theme-count-tip {
  color: rgba(60, 60, 60, 0.85);
}

/* 数量徽章 */
.count-badge {
  background: linear-gradient(135deg, #FF8A5B, #FF5F6D);
  color: white;
  font-weight: bold;
  font-size: 26rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 90, 90, 0.3);
  animation: pulse 2s infinite;
}

/* 更新徽章 */
.update-badge {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  margin-left: 12rpx;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.update-text {
  font-size: 22rpx;
  background: linear-gradient(to right, #A1C4FD, #C2E9FB);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 500;
}

/* 脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 90, 90, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(255, 90, 90, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 90, 90, 0.3);
  }
}

.title-icon {
  margin-right: 10rpx; /* 增加右边距 */
  font-size: 36rpx; /* 增大图标 */
  animation: float 2s ease-in-out infinite;
}

.breed-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  width: 100%;
  will-change: transform; /* 优化滚动性能 */
  margin: 0 auto;
  justify-content: center; /* 确保网格居中 */
}

.breed-grid-item {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  padding: 16rpx 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  will-change: transform, box-shadow, background;
}

.breed-grid-item:active {
  transform: scale(0.95) translateZ(0);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 138, 91, 0.3);
}

.breed-grid-item::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 120%;
  height: 120%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 1;
  pointer-events: none;
}

.breed-grid-item:active::after {
  top: 100%;
  left: 100%;
}

.breed-grid-item::before {
  content: '';
  position: absolute;
  top: -150%;
  left: -150%;
  width: 300%;
  height: 300%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shimmerLight 5s infinite linear;
  animation-delay: calc(var(--delay, 0) * 0.3s);
  opacity: 0.4;
  will-change: transform; /* 优化动画性能 */
}

/* 优化性能的闪光动画 */
@keyframes shimmerLight {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.breed-grid-item:nth-child(3n+1) { --delay: 0; }
.breed-grid-item:nth-child(3n+2) { --delay: 1; }
.breed-grid-item:nth-child(3n+3) { --delay: 2; }

.breed-grid-item:active {
  transform: scale(0.95);
  background: rgba(255, 138, 91, 0.15);
  border-color: var(--primary-color);
}

.breed-grid-img {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  font-size: 40rpx;
  background: rgba(255, 138, 91, 0.1);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

/* 科技风格环 */
.tech-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid rgba(255, 138, 91, 0.3);
  box-sizing: border-box;
  z-index: -1;
}

.tech-ring::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border-radius: 50%;
  border: 1px dashed rgba(255, 138, 91, 0.2);
  animation: rotateSmooth 10s linear infinite;
  will-change: transform; /* 优化动画性能 */
}

@keyframes rotateSmooth {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.breed-grid-name {
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影，增强可读性 */
}

/* 浅色主题下的文字样式 */
.light-theme-text {
  color: #333333 !important;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

/* 浅色主题下的副标题样式 */
.light-theme-subtitle {
  color: #333333 !important;
}

/* 浅色主题下的其他文字元素样式 */
.light-theme-text .tab-text,
.light-theme-text .breed-grid-title text,
.light-theme-text .welcome-title {
  color: #333333 !important;
}

/* 浅色主题下的标题图标样式 */
.breed-grid-title .title-icon {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 浅色主题内容样式 */
.light-theme-content .welcome-subtitle {
  color: #333333 !important;
}

.light-theme-content .breed-grid-title {
  color: #333333 !important;
}

.light-theme-content .selection-tabs .tab-text {
  color: #333333 !important;
}

/* 浅色主题下的宫格容器样式 */
.light-theme-content .breed-grid-container {
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 浅色主题下的选项卡样式 */
.light-theme-content .selection-tabs {
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.breed-grid-label {
  font-size: 26rpx;
  color: var(--text-secondary, #aaaaaa);
  text-align: center;
}

/* 优化后的淡入淡出动画，更加平滑且低消耗 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: translate(-50%, -50%) scale(0.9); opacity: 0; }
  to { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

.popup-paw-icon {
  margin-right: 8rpx;
  animation: float 2s ease-in-out infinite;
} 