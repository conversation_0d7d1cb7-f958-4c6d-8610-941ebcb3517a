// 个人中心页面逻辑
const app = getApp();

Page({
  data: {
    userInfo: null,
    userId: '',
    hasUserInfo: false,
    tempAvatarUrl: '',
    tempNickName: '',
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 44, // 默认导航栏高度
    showThemePanel: false, // 是否显示主题面板
    currentTheme: 'theme1' // 当前主题，默认为天空蓝
  },

  onLoad: function() {
    // 获取系统信息，设置状态栏高度
    this.getSystemInfo();
    
    // 获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('个人中心页面加载，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
    
    // 注册主题变化回调
    this.registerThemeCallback();
    
    // 从全局状态获取登录信息
    this.checkLoginFromGlobalData();
  },
  
  onShow: function() {
    // 页面显示时重新注册回调
    this.registerThemeCallback();
    
    // 页面显示时获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('个人中心页面显示，当前主题:', currentTheme);
    
    // 如果主题变化，则更新页面主题
    if (currentTheme !== this.data.currentTheme) {
      console.log('个人中心：检测到主题变化，从', this.data.currentTheme, '到', currentTheme);
      this.setData({ currentTheme: currentTheme });
    }
    
    // 每次页面显示时检查全局登录状态
    this.checkLoginFromGlobalData();
  },
  
  // 从全局状态获取登录信息
  checkLoginFromGlobalData: function() {
    // 首先检查全局数据是否有登录信息
    if (app.globalData.isLoggedIn && app.globalData.userInfo) {
      // 全局已登录，直接使用全局数据
      this.setData({
        userInfo: app.globalData.userInfo,
        userId: app.globalData.userId,
        hasUserInfo: true
      });
      
      console.log('从全局数据获取登录状态：已登录');
    } else {
      // 全局未登录或数据不完整，调用云函数验证
      this.checkLoginStatus();
    }
  },
  
  // 注册主题变化回调
  registerThemeCallback: function() {
    // 先移除可能存在的旧回调，避免重复
    this.unregisterThemeCallback();
    
    // 添加新的回调
    if (app.themeChangeCallbacks) {
      app.themeChangeCallbacks.push(this.onThemeChanged.bind(this));
      console.log('个人中心：已注册主题变化回调');
    }
  },
  
  // 移除主题变化回调
  unregisterThemeCallback: function() {
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback => 
        callback.toString() === this.onThemeChanged.bind(this).toString());
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
        console.log('个人中心：已移除主题变化回调');
      }
    }
  },
  
  // 主题变化回调
  onThemeChanged: function() {
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('个人中心：主题变化回调触发，新主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  onUnload: function() {
    // 页面卸载时移除回调
    this.unregisterThemeCallback();
  },
  
  // 检查登录状态
  checkLoginStatus: function() {
    wx.showLoading({
      title: '正在检查登录状态...',
      mask: true
    });
    
    wx.cloud.callFunction({
      name: 'checkLogin',
      success: res => {
        wx.hideLoading();
        console.log('检查登录状态返回', res.result);
        
        if (res.result && res.result.isLoggedIn) {
          // 已登录，更新用户信息
          const userInfo = res.result.userInfo || {};
          
          // 更新页面数据
          this.setData({
            userInfo: userInfo,
            userId: res.result.userId,
            hasUserInfo: true
          });
          
          // 同步更新全局数据
          app.globalData.userInfo = userInfo;
          app.globalData.userId = res.result.userId;
          app.globalData.isLoggedIn = true;
          
          // 更新本地存储
          wx.setStorageSync('userInfo', userInfo);
          wx.setStorageSync('userId', res.result.userId);
        } else {
          // 未登录或登录已过期
          this.setData({
            userInfo: null,
            userId: '',
            hasUserInfo: false,
            tempAvatarUrl: '',
            tempNickName: ''
          });
          
          // 同步更新全局数据
          app.globalData.userInfo = null;
          app.globalData.userId = '';
          app.globalData.isLoggedIn = false;
          
          // 清除本地存储
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userId');
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('检查登录状态失败', err);
      }
    });
  },
  
  // 微信登录（只获取openid并自动获取已有用户信息）
  login: function() {
    wx.showLoading({
      title: '登录中...',
      mask: true
    });
    
    // 调用云函数登录，获取用户信息
    wx.cloud.callFunction({
      name: 'login',
      data: {
        fetchUserInfo: true // 增加参数，请求获取已有用户信息
      },
      success: res => {
        wx.hideLoading();
        console.log('登录成功', res.result);
        
        if (res.result && res.result.code === 0) {
          // 检查是否已有完整用户信息
          if (res.result.hasUserInfo && res.result.userInfo && res.result.userInfo.nickName && res.result.userInfo.avatarUrl) {
            // 已有完整用户信息，无需重新授权
            const userInfo = res.result.userInfo;
            userInfo.hasUserDetail = true;
            
            // 更新页面数据
            this.setData({
              userInfo: userInfo,
              userId: res.result.userId,
              hasUserInfo: true
            });
            
            // 同步更新全局数据
            app.globalData.userInfo = userInfo;
            app.globalData.userId = res.result.userId;
            app.globalData.isLoggedIn = true;
            
            // 存储用户信息到本地
            wx.setStorageSync('userInfo', userInfo);
            wx.setStorageSync('userId', res.result.userId);
            
            wx.showToast({
              title: '登录成功',
              icon: 'success',
              duration: 2000
            });
          } else {
            // 创建一个基本的用户信息对象，没有头像和昵称或者信息不完整
            const userInfo = {
              hasUserDetail: false
            };
            
            // 更新页面数据
            this.setData({
              userInfo: userInfo,
              userId: res.result.userId,
              hasUserInfo: true
            });
            
            // 同步更新全局数据
            app.globalData.userInfo = userInfo;
            app.globalData.userId = res.result.userId;
            app.globalData.isLoggedIn = true;
            
            // 存储用户ID到本地
            wx.setStorageSync('userInfo', userInfo);
            wx.setStorageSync('userId', res.result.userId);
            
            // 使用模态窗口提供详细的引导说明
            wx.showModal({
              title: '请完善个人资料',
              content: '• 点击头像可更换\n• 输入昵称后点击保存',
              showCancel: false,
              confirmText: '我知道了',
              success: () => {
                // 模态窗口已经足够引导用户，不需要额外的Toast提示
              }
            });
          }
        } else {
          // 登录失败
          wx.showToast({
            title: res.result.message || '登录失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('登录失败', err);
        wx.showToast({
          title: '登录失败，请稍后再试',
          icon: 'none'
        });
      }
    });
  },
  
  // 选择头像
  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    console.log('选择的头像', avatarUrl);
    
    this.setData({
      tempAvatarUrl: avatarUrl
    });
  },
  
  // 输入昵称
  onInputNickname: function(e) {
    const nickName = e.detail.value;
    console.log('输入的昵称', nickName);
    
    this.setData({
      tempNickName: nickName
    });
  },
  
  // 保存用户资料
  saveUserInfo: function() {
    // 检查是否选择了头像和输入了昵称
    if (!this.data.tempAvatarUrl) {
      wx.showToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.tempNickName) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中...',
    });
    
    // 先上传头像到云存储
    this.uploadAvatarToCloud(this.data.tempAvatarUrl);
  },
  
  // 上传头像到云存储
  uploadAvatarToCloud: function(tempFilePath) {
    const cloudPath = `avatars/${this.data.userId}_${new Date().getTime()}.jpg`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: tempFilePath,
      success: res => {
        console.log('头像上传成功', res.fileID);
        // 头像上传成功后，更新用户信息
        this.updateUserInfo(res.fileID, this.data.tempNickName);
      },
      fail: err => {
        wx.hideLoading();
        console.error('头像上传失败', err);
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 更新用户信息
  updateUserInfo: function(avatarUrl, nickName) {
    // 调用云函数更新用户信息
    wx.cloud.callFunction({
      name: 'updateUserInfo',
      data: {
        avatarUrl: avatarUrl,
        nickName: nickName
      },
      success: res => {
        wx.hideLoading();
        console.log('更新用户信息成功', res.result);
        
        if (res.result && res.result.code === 0) {
          // 更新成功，更新本地用户信息
          const userInfo = this.data.userInfo || {};
          userInfo.avatarUrl = avatarUrl;
          userInfo.nickName = nickName;
          userInfo.hasUserDetail = true;
          
          // 更新页面数据
          this.setData({
            userInfo: userInfo,
            tempAvatarUrl: '',
            tempNickName: ''
          });
          
          // 同步更新全局数据
          app.globalData.userInfo = userInfo;
          
          // 更新本地存储
          wx.setStorageSync('userInfo', userInfo);
          
          wx.showToast({
            title: '资料保存成功',
            icon: 'success'
          });
        } else {
          // 更新失败
          wx.showToast({
            title: res.result.message || '资料保存失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('更新用户信息失败', err);
        wx.showToast({
          title: '资料保存失败，请稍后再试',
          icon: 'none'
        });
      }
    });
  },
  
  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出登录中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'logout',
            success: () => {
              wx.hideLoading();
              
              // 清除本地存储
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('userId');
              
              // 更新页面状态
              this.setData({
                userInfo: null,
                userId: '',
                hasUserInfo: false
              });
              
              // 同步更新全局数据
              app.globalData.userInfo = null;
              app.globalData.userId = '';
              app.globalData.isLoggedIn = false;
              
              wx.showToast({
                title: '已退出登录',
                icon: 'success'
              });
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('退出登录失败', err);
              
              wx.showToast({
                title: '退出登录失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  
  // 导航到绑定手机页面（此功能暂时隐藏，不在UI中显示）
  navigateToDevice: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },
  
  // 获取系统信息，设置状态栏高度
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        navBarHeight: 44 // 导航栏固定高度
      });
      console.log('状态栏高度:', systemInfo.statusBarHeight);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },
  
  // 导航到设置页面
  navigateToSettings: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },
  
  // 导航到关于我们页面
  navigateToAbout: function() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },
  
  // 导航到反馈与建议页面
  navigateToFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },
  
  // 导航到探索精度页面
  navigateToSimulationAccuracy: function() {
    wx.navigateTo({
      url: '/pages/simulation-accuracy/simulation-accuracy'
    });
  },
  
  // 导航到AI爱宠百科页面
  navigateToAIEncyclopedia: function() {
    wx.showModal({
      title: 'AI爱宠百科',
      content: '这是一个通过AI大模型训练出来的爱宠视觉大模型，未来将智能回答关于爱宠眼睛相关的知识内容。此功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },
  
  // 打开主题面板
  openThemePanel: function() {
    this.setData({
      showThemePanel: true
    });
  },
  
  // 关闭主题面板
  closeThemePanel: function() {
    this.setData({
      showThemePanel: false
    });
  },
  
  // 切换主题
  changeTheme: function(e) {
    const theme = e.currentTarget.dataset.theme;
    this.setData({
      currentTheme: theme,
      showThemePanel: false
    });
    
    // 添加延迟确保UI先更新
    setTimeout(() => {
      // 使用全局主题管理机制切换主题
      app.switchTheme(theme);
      console.log('个人中心：切换主题为', theme);
    }, 100);
  }
});
