<view class="profile-container {{currentTheme}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav-bar {{currentTheme}}">
    <!-- 状态栏占位 -->
    <view class="status-bar-height" style="height: {{statusBarHeight}}px;"></view>
    <!-- 导航栏内容 -->
    <view class="nav-bar-content" style="height: {{navBarHeight}}px;">
      <view class="nav-bar-title">个人中心</view>
    </view>
  </view>

  <!-- 为自定义导航栏预留空间 -->
  <view class="nav-bar-placeholder" style="height: {{statusBarHeight + navBarHeight}}px;"></view>
  <!-- 未登录状态 -->
  <view class="profile-header" wx:if="{{!hasUserInfo}}">
    <view class="avatar-container">
      <view class="default-avatar">
        <view class="avatar-placeholder"></view>
      </view>
    </view>
    <view class="login-button-container">
      <button class="login-button" bindtap="login">微信一键登录</button>
      <view class="login-desc">登录后可同步您的个性化设置</view>
    </view>
  </view>

  <!-- 已登录但未完善资料 -->
  <view class="profile-header" wx:elif="{{!userInfo.hasUserDetail}}">
    <view class="avatar-container">
      <button class="avatar-button" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
        <image class="user-avatar" src="{{tempAvatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      </button>
    </view>
    <view class="user-info">
      <view class="nickname-container">
        <input type="nickname" class="nickname-input" placeholder="请输入昵称" bindchange="onInputNickname" />
      </view>
      <button class="save-button" bindtap="saveUserInfo">保存资料</button>
    </view>
  </view>

  <!-- 已完善资料 -->
  <view class="profile-header" wx:else>
    <view class="avatar-container">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
    </view>
    <view class="user-info">
      <view class="nickname">{{userInfo.nickName}}</view>
      <view class="user-id">ID: {{userId}}</view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-list">
    <view class="function-group">
      <view class="group-title">我的功能</view>
      
      <view class="function-item" bindtap="navigateToAIEncyclopedia">
        <view class="function-icon ai-icon">
          <text class="icon-text">🔍</text>
        </view>
        <view class="function-name">AI爱宠百科</view>
        <view class="function-tag">开发中</view>
        <view class="function-arrow">→</view>
      </view>
      
      <view class="function-item" bindtap="navigateToFeedback">
        <view class="function-icon feedback-icon">
          <text class="icon-text">📝</text>
        </view>
        <view class="function-name">反馈与建议</view>
        <view class="function-arrow">→</view>
      </view>
      
      <!-- 切换主题功能 -->
      <view class="function-item" bindtap="openThemePanel">
        <view class="function-icon theme-icon">
          <text class="icon-text">🎨</text>
        </view>
        <view class="function-name">切换主题</view>
        <view class="function-arrow">→</view>
      </view>
      
      <view class="function-item" bindtap="navigateToSimulationAccuracy">
        <view class="function-icon accuracy-icon">
          <text class="icon-text">📊</text>
        </view>
        <view class="function-name">探索精度</view>
        <view class="function-arrow">→</view>
      </view>
      
      <view class="function-item" bindtap="navigateToAbout">
        <view class="function-icon about-icon">
          <text class="icon-text">ℹ️</text>
        </view>
        <view class="function-name">关于我们</view>
        <view class="function-desc">用科技促进人宠和谐</view>
        <view class="function-arrow">→</view>
      </view>
      
      <!-- 退出登录按钮，作为我的功能最后一项，仅在登录状态下显示 -->
      <view class="function-item logout-item" wx:if="{{userInfo}}" bindtap="logout">
        <view class="function-icon logout-icon">
          <text class="icon-text">🔒</text>
        </view>
        <view class="function-name logout-text">退出登录</view>
      </view>
    </view>
  </view>

  <!-- 主题切换面板 -->
  <view class="theme-panel-container" wx:if="{{showThemePanel}}">
    <view class="theme-panel-mask" bindtap="closeThemePanel"></view>
    <view class="theme-panel">
      <view class="theme-panel-title">请选择主题风格</view>
      <view class="theme-panel-close" bindtap="closeThemePanel">×</view>
      <view class="theme-options-grid">
        <view class="theme-option {{currentTheme === 'theme1' ? 'active' : ''}}" data-theme="theme1" bindtap="changeTheme">
          <view class="color-preview" style="background: linear-gradient(145deg, #4C5D99, #6275B3);"></view>
          <text>天空蓝</text>
        </view>
        <view class="theme-option {{currentTheme === 'theme2' ? 'active' : ''}}" data-theme="theme2" bindtap="changeTheme">
          <view class="color-preview" style="background: linear-gradient(145deg, #4A7A8C, #5E9CAF);"></view>
          <text>薄荷青</text>
        </view>
        <view class="theme-option {{currentTheme === 'theme4' ? 'active' : ''}}" data-theme="theme4" bindtap="changeTheme">
          <view class="color-preview" style="background: linear-gradient(145deg, #3D6B96, #5284B4);"></view>
          <text>海洋蓝</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="app-info">
    <view class="app-name">爱宠看世界 © 2025</view>
    <view class="app-version">版本 1.1.8</view>
  </view>
</view>
