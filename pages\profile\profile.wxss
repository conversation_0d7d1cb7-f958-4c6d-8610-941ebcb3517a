/* 个人中心页面样式 */
.profile-container {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

.status-bar-height {
  width: 100%;
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.nav-bar-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 导航栏占位符，防止内容被导航栏遮挡 */
.nav-bar-placeholder {
  width: 100%;
}

/* 主题1: 天空蓝 */
.theme1 {
  --dark-bg: var(--theme1-dark-bg);
  --card-bg: var(--theme1-card-bg);
  background: var(--theme1-gradient);
  --primary-color: #4C5D99;
  --gradient-primary: linear-gradient(145deg, #4C5D99, #6275B3);
}

/* 天空蓝主题导航栏 */
.theme1 .custom-nav-bar {
  background: var(--theme1-dark-bg);
}

/* 主题2: 薄荷青 */
.theme2 {
  --dark-bg: var(--theme2-dark-bg);
  --card-bg: var(--theme2-card-bg);
  background: var(--theme2-gradient);
  --primary-color: #4A7A8C;
  --gradient-primary: linear-gradient(145deg, #4A7A8C, #5E9CAF);
}

/* 薄荷青主题导航栏 */
.theme2 .custom-nav-bar {
  background: var(--theme2-dark-bg);
}

/* 主题4: 海洋蓝 */
.theme4 {
  --dark-bg: var(--theme4-dark-bg);
  --card-bg: var(--theme4-card-bg);
  background: var(--theme4-gradient);
  --primary-color: #3D6B96;
  --gradient-primary: linear-gradient(145deg, #3D6B96, #5284B4);
}

/* 海洋蓝主题导航栏 */
.theme4 .custom-nav-bar {
  background: var(--theme4-dark-bg);
}

.profile-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.07)"/></svg>');
  opacity: 0.15;
  pointer-events: none;
  will-change: transform;
}

/* 个人信息头部 */
.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  position: relative;
  z-index: 1;
}

.avatar-container {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  position: relative;
  transition: all 0.3s ease;
}

.avatar-container:active {
  transform: scale(0.96);
}

.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
}

.avatar-placeholder {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 头像选择按钮样式 */
.avatar-button {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-button::after {
  border: none;
}

/* 为未完善资料状态的头像添加点击动画效果 */
.profile-header:nth-child(3) .avatar-container {
  animation: pulse 2s infinite;
  border: 4rpx solid rgba(255, 255, 255, 0.4);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* 昵称输入框样式 */
.nickname-input {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 40rpx;
  padding: 16rpx 30rpx;
  margin: 20rpx 0;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 48rpx;
}

/* 保存按钮样式 */
.save-button {
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 138, 91, 0.3);
  margin-top: 20rpx;
  transition: all 0.3s ease;
}

.save-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(255, 138, 91, 0.2);
}

.login-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.login-button {
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 138, 91, 0.3);
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  width: 80%;
}

.login-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(255, 138, 91, 0.2);
}

.login-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 用户信息 */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 新增提示文字样式 */
.profile-tips {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
  width: 85%;
  font-weight: 500;
}

.avatar-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-top: 10rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  position: absolute;
  bottom: -14rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

/* 昵称容器样式增加宽度 */
.nickname-container {
  position: relative;
  width: 100%;
  margin-bottom: 10rpx;
  max-width: 500rpx;
}

.nickname-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-top: 6rpx;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary, #ffffff);
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

/* 功能列表 */
.function-list {
  flex: 1;
  padding: 0 30rpx;
  position: relative;
  z-index: 1;
}

.function-group {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.group-title {
  font-size: 28rpx;
  color: var(--text-secondary, rgba(255, 255, 255, 0.9));
  margin-bottom: 20rpx;
  font-weight: 500;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 36rpx;
}

.function-name {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-primary, rgba(255, 255, 255, 0.9));
}

.function-arrow {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 功能标签样式 */
.function-tag {
  font-size: 22rpx;
  color: #ffffff;
  background: rgba(255, 138, 91, 0.8);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

/* 退出登录按钮样式 */
.logout-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: none;
  color: #ff8800;
  font-weight: 500;
}

/* 让退出登录与功能项布局、间距完全一致 */
.logout-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.logout-item .logout-text {
  flex: 1;
  font-size: 30rpx;
  color: #ff8800;
}

.logout-item .logout-icon .icon-text {
  font-size: 36rpx;
  color: #ff8800;
}

/* 版本信息 */
.app-info {
  text-align: center;
  padding: 40rpx 0 20rpx;
  position: relative;
  z-index: 1;
}

.app-name {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 6rpx;
}

.app-version {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 功能项描述样式 */
.function-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(0, 0, 0, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

/* 主题切换相关样式 */
.theme-icon {
  background: transparent;
}

/* 主题面板容器 */
.theme-panel-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-panel-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.theme-panel {
  position: relative;
  width: 80%;
  background: rgba(40, 44, 52, 0.95);
  border-radius: 16px;
  padding: 20px;
  z-index: 1001;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease;
}

.theme-panel-title {
  font-size: 18px;
  text-align: center;
  margin-bottom: 20px;
  color: #fff;
  font-weight: 500;
}

.theme-panel-close {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  font-size: 22px;
  color: #fff;
  opacity: 0.8;
}

.theme-options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  padding: 5px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border-radius: 12px;
  transition: all 0.3s;
}

.theme-option.active {
  background-color: rgba(255, 255, 255, 0.15);
}

.color-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-option text {
  font-size: 14px;
  color: #fff;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
