// 探索精度页面逻辑
const app = getApp();

Page({
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 44, // 默认导航栏高度
    currentTheme: 'theme1',
    simulationAccuracy: '本应用使用AI视觉技术模拟爱宠视觉，基于兽医学和动物视觉研究提供的数据。模拟了色彩感知、视场角度、夜视能力和动态视觉等核心特性，精度约为90%。实际动物视觉体验可能因个体差异、年龄和健康状况而有所不同。',
    visionHighlight: '不断探索，更接近宠物真实视觉体验'
  },

  onLoad: function() {
    // 获取系统信息，设置状态栏高度
    this.getSystemInfo();
    
    // 获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('探索精度页面加载，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
    
    // 注册主题变化回调
    if (app.themeChangeCallbacks) {
      // 添加当前页面的主题更新回调
      app.themeChangeCallbacks.push(this.onThemeChanged.bind(this));
    }
  },
  
  onShow: function() {
    // 页面显示时获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('探索精度页面显示，当前主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  // 主题变化回调
  onThemeChanged: function() {
    const currentTheme = app.globalData.currentTheme || 'theme1';
    console.log('主题变化回调触发，新主题:', currentTheme);
    
    // 更新页面主题
    this.setData({ currentTheme: currentTheme });
  },
  
  onUnload: function() {
    // 页面卸载时移除回调
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback => 
        callback.toString() === this.onThemeChanged.bind(this).toString());
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
      }
    }
  },
  
  // 获取系统信息，设置状态栏高度
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        navBarHeight: 44 // 导航栏固定高度
      });
      console.log('状态栏高度:', systemInfo.statusBarHeight);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
}); 