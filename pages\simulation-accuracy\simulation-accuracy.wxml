<view class="accuracy-container {{currentTheme}}">
  <!-- 自定义导航栏 -->
  <view class="custom-nav-bar {{currentTheme}}">
    <!-- 状态栏占位 -->
    <view class="status-bar-height" style="height: {{statusBarHeight}}px;"></view>
    <!-- 导航栏内容 -->
    <view class="nav-bar-content" style="height: {{navBarHeight}}px;">
      <view class="nav-bar-back" bindtap="navigateBack">←</view>
      <view class="nav-bar-title">探索精度</view>
    </view>
  </view>

  <!-- 为自定义导航栏预留空间 -->
  <view class="nav-bar-placeholder" style="height: {{statusBarHeight + navBarHeight}}px;"></view>

  <!-- 视觉模拟技术 -->
  <view class="accuracy-card">
    <view class="card-title">
      <text class="title-icon">ℹ️</text>
      <text>视觉模拟技术</text>
    </view>
    <view class="card-content">
      <view class="content-block">
        <view class="block-text">{{simulationAccuracy}}</view>
      </view>
    </view>
  </view>

  <!-- 局限性说明 -->
  <view class="accuracy-card">
    <view class="card-title">
      <text class="title-icon">⚠️</text>
      <text>局限性说明</text>
    </view>
    <view class="card-content">
      <view class="content-block">
        <view class="block-text">
          本应用只关注爱宠的眼睛视觉系统，不包含听力、嗅觉等其他感官模拟。由于硬件和算法限制，某些视觉特性（如动态范围、紫外线感知）无法完全模拟。不同品种的视觉特性差异可能存在简化，部分参数基于科学推测而非直接测量数据。
        </view>
      </view>
    </view>
  </view>

  <!-- 未来展望 -->
  <view class="accuracy-card vision-card">
    <view class="card-title">
      <text class="title-icon">🔮</text>
      <text>未来展望</text>
    </view>
    <view class="card-content">
      <view class="vision-highlight">
        <text>{{visionHighlight}}</text>
      </view>
      
      <!-- 移除vision points部分 -->
    </view>
  </view>
</view> 