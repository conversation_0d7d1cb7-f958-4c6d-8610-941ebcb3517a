/* 探索精度页面样式 */
.accuracy-container {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

.status-bar-height {
  width: 100%;
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 30rpx;
}

.nav-bar-back {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
  color: #ffffff;
  font-weight: 500;
}

.nav-bar-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  flex: 1;
}

/* 导航栏占位符，防止内容被导航栏遮挡 */
.nav-bar-placeholder {
  width: 100%;
}

/* 主题1: 天空蓝 */
.theme1 {
  --dark-bg: var(--theme1-dark-bg);
  --card-bg: var(--theme1-card-bg);
  background: var(--theme1-gradient);
}

/* 天空蓝主题导航栏 */
.theme1 .custom-nav-bar {
  background: var(--theme1-dark-bg);
}

/* 主题2: 薄荷青 */
.theme2 {
  --dark-bg: var(--theme2-dark-bg);
  --card-bg: var(--theme2-card-bg);
  background: var(--theme2-gradient);
}

/* 薄荷青主题导航栏 */
.theme2 .custom-nav-bar {
  background: var(--theme2-dark-bg);
}

/* 主题4: 海洋蓝 */
.theme4 {
  --dark-bg: var(--theme4-dark-bg);
  --card-bg: var(--theme4-card-bg);
  background: var(--theme4-gradient);
}

/* 海洋蓝主题导航栏 */
.theme4 .custom-nav-bar {
  background: var(--theme4-dark-bg);
}

.accuracy-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.07)"/></svg>');
  opacity: 0.15;
  pointer-events: none;
  will-change: transform;
}

/* 卡片样式 */
.accuracy-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 30rpx;
  margin: 20rpx 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

/* 卡片标题和内容 */
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.card-content {
  padding: 6rpx 0;
}

/* 内容块 */
.content-block {
  margin-bottom: 24rpx;
  padding-left: 10rpx;
}

.content-block:last-child {
  margin-bottom: 10rpx;
}

.block-title {
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8rpx;
  position: relative;
  padding-left: 20rpx;
}

.block-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 24rpx;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  border-radius: 4rpx;
}

.block-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  padding-left: 20rpx;
}

/* 愿景卡片 */
.vision-card {
  background: rgba(255, 255, 255, 0.12);
}

.vision-highlight {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.6;
  text-align: center;
  font-style: italic;
}

/* 已移除vision-points、vision-point、point-icon和point-text相关的样式 */ 