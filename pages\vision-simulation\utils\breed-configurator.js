/**
 * 品种特性配置器
 * 负责处理不同品种动物的视觉参数配置
 */

const visionConfig = require('./vision-config');
const visionManager = require('./vision-manager');

class BreedConfigurator {
  constructor() {
    this.pageInstance = null;
    this.breedConfigs = {
      // 犬科动物配置
      dog: {
        '拉布拉多': {
          resolutionFactor: 1.0,
          brightness: 1.5,
          contrast: 1.3,
          description: '拉布拉多视力接近人类水平'
        },
        '金毛': {
          resolutionFactor: 0.33,
          brightness: 1.5,
          contrast: 1.3,
          description: '金毛视力约为人类的1/3'
        },
        '边境牧羊犬': {
          resolutionFactor: 0.4,
          brightness: 1.5,
          contrast: 1.3,
          description: '边境牧羊犬视力约为人类的2/5'
        },
        '贵宾犬': {
          resolutionFactor: 0.27,
          brightness: 1.5,
          contrast: 1.3,
          description: '贵宾犬视力约为人类的4/15'
        },
        '柯基': {
          resolutionFactor: 0.27,
          brightness: 1.5,
          contrast: 1.3,
          description: '柯基视力约为人类的4/15'
        },
        '比熊': {
          resolutionFactor: 0.27,
          brightness: 1.5,
          contrast: 1.3,
          description: '比熊视力约为人类的4/15'
        },
        '哈士奇': {
          resolutionFactor: 0.27,
          brightness: 2.0,
          contrast: 1.5,
          description: '哈士奇具有优秀的夜视能力'
        },
        '吉娃娃': {
          resolutionFactor: 0.25,
          brightness: 1.5,
          contrast: 1.3,
          description: '吉娃娃视力约为人类的1/4'
        },
        '法国斗牛犬': {
          resolutionFactor: 0.25,
          brightness: 1.5,
          contrast: 1.3,
          description: '法国斗牛犬视力约为人类的1/4'
        }
      },
      // 猫科动物配置
      cat: {
        '暹罗猫': {
          resolutionFactor: 0.15,
          brightness: 1.5,
          contrast: 1.3,
          description: '暹罗猫视力相对较好，但夜视能力稍弱'
        },
        '英国短毛猫': {
          resolutionFactor: 0.2,
          brightness: 2.0,
          contrast: 1.5,
          description: '英国短毛猫具有典型的猫科夜视能力'
        },
        '美国短毛猫': {
          resolutionFactor: 0.2,
          brightness: 2.0,
          contrast: 1.5,
          description: '美国短毛猫具有典型的猫科夜视能力'
        },
        '波斯猫': {
          resolutionFactor: 0.2,
          brightness: 2.0,
          contrast: 1.5,
          description: '波斯猫具有典型的猫科夜视能力'
        },
        '布偶猫': {
          resolutionFactor: 0.2,
          brightness: 2.0,
          contrast: 1.5,
          description: '布偶猫具有典型的猫科夜视能力'
        }
      }
    };
  }

  /**
   * 初始化品种配置器
   * @param {Object} pageInstance - 页面实例
   */
  init(pageInstance) {
    this.pageInstance = pageInstance;
  }

  /**
   * 根据动物种类和品种设置视力清晰度因子
   * @param {string} breedName - 品种名称
   * @param {string} animalType - 动物类型 ('cat' 或 'dog')
   */
  setBreedVisualAcuity(breedName, animalType) {
    // 默认值
    let resolutionFactor = 0.5;
    let brightness = 1.5;
    let contrast = 1.3;
    
    // 根据动物类型和品种设置不同的清晰度因子
    if (animalType === 'cat') {
      // 猫科动物视力较差，默认设置为1/5 (20/100)
      resolutionFactor = visionConfig.VISUAL_ACUITY_CONFIG.CAT.RESOLUTION_FACTOR;
      brightness = visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS;
      contrast = visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST;
      
      // 检查是否有特定品种配置
      const catConfig = this._findBreedConfig(breedName, 'cat');
      if (catConfig) {
        resolutionFactor = catConfig.resolutionFactor;
        brightness = catConfig.brightness;
        contrast = catConfig.contrast;
        console.log('设置猫科动物[' + breedName + ']的视觉参数:', catConfig);
        
        // 暹罗猫的特殊处理
        if (breedName && breedName.includes('暹罗')) {
          this.setSiameseNightVision();
        }
      } else {
        console.log('设置猫科动物[' + breedName + ']的默认视觉参数');
      }
    } else {
      // 犬科动物视力设置
      const dogConfig = this._findBreedConfig(breedName, 'dog');
      if (dogConfig) {
        resolutionFactor = dogConfig.resolutionFactor;
        brightness = dogConfig.brightness;
        contrast = dogConfig.contrast;
        console.log('设置犬种[' + breedName + ']的视觉参数:', dogConfig);
        
        // 哈士奇的特殊处理
        if (breedName && breedName.includes('哈士奇')) {
          this.setHuskyNightVision();
        }
      } else {
        console.log('设置犬种[' + breedName + ']的默认视觉参数');
      }
    }
    
    // 输出调试信息，确认最终设置的视觉参数
    console.log('最终设置的视觉参数:', {
      resolutionFactor,
      brightness,
      contrast,
      animalType
    });
    
    // 强制设置数据，确保在UI上显示正确的值
    this.pageInstance.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor,
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast,
      'originalDogVisionParams.resolutionFactor': resolutionFactor,
      'originalDogVisionParams.brightness': brightness,
      'originalDogVisionParams.contrast': contrast
    });
    
    // 如果视觉管理器已初始化，更新其配置
    if (visionManager) {
      // 强制更新视觉管理器中的参数
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: resolutionFactor
      });
      
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness,
        CONTRAST: contrast
      });
      
      console.log('更新视觉管理器的参数完成');
      
      // 强制重新渲染当前帧
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        console.log('重新渲染帧以应用新的视觉参数');
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    } else {
      console.warn('视觉管理器尚未初始化，无法应用视觉参数');
    }
    
    return { resolutionFactor, brightness, contrast }; // 返回设置的值，便于调用者使用
  }

  /**
   * 查找品种配置
   * @param {string} breedName - 品种名称
   * @param {string} animalType - 动物类型
   * @returns {Object|null} 品种配置或null
   */
  _findBreedConfig(breedName, animalType) {
    if (!breedName || !this.breedConfigs[animalType]) {
      return null;
    }
    
    const configs = this.breedConfigs[animalType];
    
    // 精确匹配
    if (configs[breedName]) {
      return configs[breedName];
    }
    
    // 模糊匹配
    for (const configName in configs) {
      if (breedName.includes(configName) || configName.includes(breedName)) {
        return configs[configName];
      }
    }
    
    return null;
  }

  /**
   * 设置暹罗猫的特殊夜视能力参数
   */
  setSiameseNightVision() {
    // 暹罗猫因为眼睛有轻微的视轴对位问题，夜视能力较弱
    const brightness = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_BRIGHTNESS; // 暹罗猫特殊亮度增强因子：1.5
    const contrast = visionConfig.NIGHT_VISION_CONFIG.CAT.SIAMESE_CONTRAST;     // 暹罗猫特殊对比度因子：1.3
    
    console.log('设置暹罗猫的夜视能力参数 - 亮度增强:', brightness, '对比度:', contrast);
    
    // 更新UI显示的夜视参数
    this.pageInstance.setData({
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast,
      'originalDogVisionParams.brightness': brightness,
      'originalDogVisionParams.contrast': contrast
    });
    
    // 如果视觉管理器已初始化，更新其配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness,
        CONTRAST: contrast
      });
      
      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 设置哈士奇的夜视能力参数
   */
  setHuskyNightVision() {
    // 哈士奇的夜视能力要比其它品种的犬科动物要强
    // 设置与猫科动物相同的夜视参数
    const brightness = 2.0; // 与猫科动物相同的亮度增强
    const contrast = 1.5;   // 与猫科动物相同的对比度
    
    console.log('设置哈士奇的夜视能力参数 - 亮度增强:', brightness, '对比度:', contrast);
    
    // 更新UI显示的夜视参数
    this.pageInstance.setData({
      'dogVisionParams.brightness': brightness,
      'dogVisionParams.contrast': contrast,
      'originalDogVisionParams.brightness': brightness,
      'originalDogVisionParams.contrast': contrast
    });
    
    // 如果视觉管理器已初始化，更新其配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness,
        CONTRAST: contrast
      });
      
      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 获取品种描述
   * @param {string} breedName - 品种名称
   * @param {string} animalType - 动物类型
   * @returns {string} 品种描述
   */
  getBreedDescription(breedName, animalType) {
    const config = this._findBreedConfig(breedName, animalType);
    return config ? config.description : '标准视觉能力';
  }

  /**
   * 获取所有支持的品种列表
   * @param {string} animalType - 动物类型
   * @returns {Array} 品种列表
   */
  getSupportedBreeds(animalType) {
    if (!this.breedConfigs[animalType]) {
      return [];
    }
    
    return Object.keys(this.breedConfigs[animalType]);
  }

  /**
   * 检查品种是否被支持
   * @param {string} breedName - 品种名称
   * @param {string} animalType - 动物类型
   * @returns {boolean} 是否支持
   */
  isBreedSupported(breedName, animalType) {
    return !!this._findBreedConfig(breedName, animalType);
  }
}

module.exports = BreedConfigurator;
