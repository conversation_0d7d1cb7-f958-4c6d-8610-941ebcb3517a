/**
 * CAM硬件相机管理器
 * 负责管理外部硬件相机的连接、图片流获取、双缓冲显示等功能
 */

// CAM配置常量
const CAM_CONFIG = {
  BASE_URL: 'https://s1.v100.vip:33501/petvision/1',
  REFRESH_RATE: 300,        // 刷新频率(ms) - 提高到3.3fps，提高流畅度
  MAX_RETRY: 5,             // 最大重试次数
  TIMEOUT: 8000,            // 超时时间(ms)
  IMAGE_WIDTH: 640,         // 图片宽度
  IMAGE_HEIGHT: 480,        // 图片高度
  MAX_LOADING_TIME: 4000,   // 单张图片最大加载时间，增加到4秒
  ERROR_THRESHOLD: 20,      // 连续错误阈值，提高到20次
  TIMEOUT_THRESHOLD: 15,    // 连续超时阈值，提高到15次
  SUCCESS_RESET_COUNT: 3    // 连续成功多少次后重置错误计数
};

let camContext = null;  // CAM上下文对象

/**
 * 初始化CAM管理器
 * @param {Object} pageInstance - 页面实例
 * @returns {Promise} 初始化结果
 */
function initCamManager(pageInstance) {
  return new Promise((resolve, reject) => {
    try {
      // 创建CAM上下文
      camContext = {
        pageInstance: pageInstance,
        refreshTimer: null,
        retryCount: 0,
        isConnected: false,
        currentBuffer: 'buffer1',
        nextBuffer: 'buffer2',
        lastImageUrl: '',
        errorCallback: null,
        isRefreshing: false,        // 是否正在刷新图片
        imageLoadingTimer: null,    // 图片加载超时定时器
        consecutiveErrors: 0,       // 连续错误计数
        consecutiveTimeouts: 0,     // 连续超时计数
        consecutiveSuccess: 0,      // 连续成功计数
        lastUpdateTime: Date.now(), // 最后更新时间
        totalErrors: 0,             // 总错误计数
        totalSuccess: 0             // 总成功计数
      };
      
      console.log('CAM管理器初始化成功');
      resolve(camContext);
    } catch (error) {
      console.error('CAM管理器初始化失败:', error);
      reject(error);
    }
  });
}

/**
 * 连接CAM硬件相机
 * @returns {Promise} 连接结果
 */
function connectCam() {
  return new Promise((resolve, reject) => {
    if (!camContext) {
      reject(new Error('CAM管理器未初始化'));
      return;
    }
    
    console.log('开始连接CAM硬件相机...');
    
    // 重置状态
    camContext.retryCount = 0;
    camContext.consecutiveErrors = 0;
    camContext.consecutiveTimeouts = 0;
    camContext.consecutiveSuccess = 0;
    camContext.totalErrors = 0;
    camContext.totalSuccess = 0;
    camContext.isRefreshing = false;
    
    // 设置连接状态
    camContext.pageInstance.setData({
      camStatus: 'connecting',
      camIsLoading: true
    });
    
    // 生成初始双缓冲图片URL
    const imageUrl1 = _fetchCamImage();
    const imageUrl2 = _fetchCamImage(); // 立即生成第二个URL用于预加载
    
    // 连接成功，设置双缓冲初始状态
    camContext.isConnected = true;
    camContext.currentBuffer = 'buffer1';
    camContext.pageInstance.setData({
      camStatus: 'connected',
      camIsLoading: false,
      camImageUrl: imageUrl1,      // buffer1显示第一张图片
      camNextImageUrl: imageUrl2,  // buffer2预加载第二张图片
      camCurrentBuffer: 'buffer1'
    });
    
    console.log('CAM硬件相机连接成功');
    
    // 启动图片流刷新
    _startImageRefresh();
    
    resolve(true);
  });
}

/**
 * 断开CAM硬件相机连接
 */
function disconnectCam() {
  if (!camContext) {
    return;
  }
  
  console.log('断开CAM硬件相机连接');
  
  // 停止图片流刷新
  _stopImageRefresh();
  
  // 清除加载超时定时器
  if (camContext.imageLoadingTimer) {
    clearTimeout(camContext.imageLoadingTimer);
    camContext.imageLoadingTimer = null;
  }
  
  // 重置状态
  camContext.isConnected = false;
  camContext.isRefreshing = false;
  camContext.consecutiveErrors = 0;
  camContext.consecutiveTimeouts = 0;
  camContext.consecutiveSuccess = 0;
  camContext.totalErrors = 0;
  camContext.totalSuccess = 0;
  
  camContext.pageInstance.setData({
    camStatus: 'disconnected',
    camMode: false,
    camImageUrl: '',
    camNextImageUrl: '',
    camIsLoading: false,
    camErrorMsg: ''
  });
}

/**
 * 获取CAM图片URL
 * @returns {string} 图片URL
 */
function _fetchCamImage() {
  // 添加时间戳和随机数防止缓存
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  const imageUrl = `${CAM_CONFIG.BASE_URL}?t=${timestamp}&r=${random}&nocache=1`;
  
  console.log('生成CAM图片URL:', imageUrl);
  return imageUrl;
}

/**
 * 启动图片流刷新
 */
function _startImageRefresh() {
  if (!camContext || camContext.refreshTimer) {
    return;
  }
  
  console.log('启动CAM图片流刷新，刷新频率:', CAM_CONFIG.REFRESH_RATE + 'ms');
  
  camContext.refreshTimer = setInterval(() => {
    if (!camContext.isConnected) {
      _stopImageRefresh();
      return;
    }
    
    // 如果正在刷新，跳过这次更新，避免重叠请求
    if (camContext.isRefreshing) {
      console.log('跳过图片更新：上次更新仍在进行中');
      return;
    }
    
    // 开始新的图片更新
    _updateNextImage();
  }, CAM_CONFIG.REFRESH_RATE);
}

/**
 * 停止图片流刷新
 */
function _stopImageRefresh() {
  if (camContext && camContext.refreshTimer) {
    clearInterval(camContext.refreshTimer);
    camContext.refreshTimer = null;
    console.log('停止CAM图片流刷新');
  }
}

/**
 * 更新下一张图片
 */
function _updateNextImage() {
  if (!camContext || !camContext.isConnected) {
    return;
  }
  
  // 标记正在刷新
  camContext.isRefreshing = true;
  
  // 获取新的图片URL
  const imageUrl = _fetchCamImage();
  const nextBuffer = camContext.currentBuffer === 'buffer1' ? 'buffer2' : 'buffer1';
  const nextImageKey = nextBuffer === 'buffer1' ? 'camImageUrl' : 'camNextImageUrl';
  
  console.log('更新下一张图片:', { nextBuffer, imageUrl });
  
  // 设置图片加载超时检测
  if (camContext.imageLoadingTimer) {
    clearTimeout(camContext.imageLoadingTimer);
  }
  
  camContext.imageLoadingTimer = setTimeout(() => {
    console.warn('图片加载超时，重置刷新状态');
    camContext.isRefreshing = false;
    _handleImageLoadTimeout();
  }, CAM_CONFIG.MAX_LOADING_TIME);
  
  // 更新下一个缓冲区的图片URL
  const updateData = {};
  updateData[nextImageKey] = imageUrl;
  
  // 标记下一个缓冲区为待切换状态
  camContext.nextBuffer = nextBuffer;
  
  camContext.pageInstance.setData(updateData, () => {
    console.log('图片URL已更新到', nextImageKey);
  });
}

/**
 * 处理图片加载完成事件
 * @param {string} bufferType - 缓冲区类型
 */
function handleImageLoadSuccess(bufferType) {
  if (!camContext || !camContext.isConnected) {
    return;
  }
  
  console.log('图片加载成功:', bufferType, '当前缓冲区:', camContext.currentBuffer);
  
  // 如果加载完成的是下一个缓冲区，则切换显示
  if (bufferType === camContext.nextBuffer && camContext.isRefreshing) {
    console.log('切换显示缓冲区从', camContext.currentBuffer, '到', bufferType);
    
    // 切换当前显示的缓冲区
    camContext.currentBuffer = bufferType;
    camContext.pageInstance.setData({
      camCurrentBuffer: bufferType
    });
    
    // 增加成功计数
    camContext.consecutiveSuccess++;
    camContext.totalSuccess++;
    camContext.lastUpdateTime = Date.now();
    
    // 如果连续成功达到阈值，重置错误计数
    if (camContext.consecutiveSuccess >= CAM_CONFIG.SUCCESS_RESET_COUNT) {
      if (camContext.consecutiveErrors > 0 || camContext.consecutiveTimeouts > 0) {
        console.log('连续成功', CAM_CONFIG.SUCCESS_RESET_COUNT, '次，重置错误计数');
        camContext.consecutiveErrors = 0;
        camContext.consecutiveTimeouts = 0;
      }
    }
    
    // 确保状态是连接成功
    if (camContext.pageInstance.data.camStatus !== 'connected') {
      camContext.pageInstance.setData({
        camStatus: 'connected',
        camErrorMsg: ''
      });
    }
  }
  
  // 清除加载超时定时器
  if (camContext.imageLoadingTimer) {
    clearTimeout(camContext.imageLoadingTimer);
    camContext.imageLoadingTimer = null;
  }
  
  // 重置刷新状态
  camContext.isRefreshing = false;
}

/**
 * 处理图片加载错误事件
 */
function handleImageLoadError() {
  if (!camContext) {
    return;
  }
  
  console.error('CAM图片加载错误');
  
  // 增加连续错误计数
  camContext.consecutiveErrors++;
  camContext.totalErrors++;
  
  // 重置连续成功计数
  camContext.consecutiveSuccess = 0;
  
  // 清除加载超时定时器
  if (camContext.imageLoadingTimer) {
    clearTimeout(camContext.imageLoadingTimer);
    camContext.imageLoadingTimer = null;
  }
  
  // 重置刷新状态
  camContext.isRefreshing = false;
  
  console.log('CAM图片连续失败次数:', camContext.consecutiveErrors, '总成功/失败:', camContext.totalSuccess + '/' + camContext.totalErrors);
  
  // 使用新的错误阈值，并考虑成功率
  const errorRate = camContext.totalErrors / (camContext.totalSuccess + camContext.totalErrors);
  const shouldShowError = camContext.consecutiveErrors >= CAM_CONFIG.ERROR_THRESHOLD || 
                         (camContext.consecutiveErrors >= 10 && errorRate > 0.5);
  
  // 如果连续失败次数过多，或错误率过高，才进入错误状态
  if (shouldShowError) {
    console.error('CAM连续失败次数过多或错误率过高，进入错误状态');
    _handleCamError('图片流连接不稳定');
  }
}

/**
 * 处理图片加载超时
 */
function _handleImageLoadTimeout() {
  if (!camContext) {
    return;
  }
  
  console.warn('图片加载超时');
  
  // 增加连续超时计数
  camContext.consecutiveTimeouts++;
  camContext.totalErrors++;
  
  // 重置连续成功计数
  camContext.consecutiveSuccess = 0;
  
  console.log('CAM连续超时次数:', camContext.consecutiveTimeouts, '总成功/失败:', camContext.totalSuccess + '/' + camContext.totalErrors);
  
  // 使用新的超时阈值，并考虑整体表现
  const timeoutRate = camContext.consecutiveTimeouts / (camContext.consecutiveTimeouts + camContext.consecutiveSuccess);
  const shouldShowError = camContext.consecutiveTimeouts >= CAM_CONFIG.TIMEOUT_THRESHOLD || 
                         (camContext.consecutiveTimeouts >= 8 && timeoutRate > 0.7);
  
  // 如果连续超时次数过多，才显示错误
  if (shouldShowError) {
    console.error('CAM连续超时次数过多，进入错误状态');
    _handleCamError('网络连接超时，请检查网络连接或硬件状态');
  }
}

/**
 * 使用双缓冲机制更新图片 (已弃用，使用新的_updateNextImage方法)
 * @param {string} imageUrl - 新图片URL
 */
function _updateImageWithDoubleBuffer(imageUrl) {
  // 此方法已被_updateNextImage替代，保留以防兼容性问题
  console.warn('_updateImageWithDoubleBuffer方法已弃用，请使用_updateNextImage');
}

/**
 * 处理CAM错误
 * @param {string} errorMsg - 错误信息
 */
function _handleCamError(errorMsg) {
  if (!camContext) {
    return;
  }
  
  console.error('CAM错误:', errorMsg);
  
  // 停止刷新
  _stopImageRefresh();
  
  // 清除加载超时定时器
  if (camContext.imageLoadingTimer) {
    clearTimeout(camContext.imageLoadingTimer);
    camContext.imageLoadingTimer = null;
  }
  
  // 更新状态
  camContext.isConnected = false;
  camContext.isRefreshing = false;
  camContext.pageInstance.setData({
    camStatus: 'error',
    camErrorMsg: errorMsg,
    camIsLoading: false
  });
  
  // 如果有错误回调，调用它
  if (camContext.errorCallback) {
    camContext.errorCallback(errorMsg);
  }
}

/**
 * 设置错误回调
 * @param {Function} callback - 错误回调函数
 */
function setCamErrorCallback(callback) {
  if (camContext) {
    camContext.errorCallback = callback;
  }
}

/**
 * 获取CAM状态
 * @returns {Object} CAM状态信息
 */
function getCamStatus() {
  if (!camContext) {
    return { connected: false, status: 'not_initialized' };
  }
  
  return {
    connected: camContext.isConnected,
    status: camContext.pageInstance.data.camStatus,
    retryCount: camContext.retryCount,
    consecutiveErrors: camContext.consecutiveErrors,
    consecutiveTimeouts: camContext.consecutiveTimeouts,
    consecutiveSuccess: camContext.consecutiveSuccess,
    totalErrors: camContext.totalErrors,
    totalSuccess: camContext.totalSuccess,
    isRefreshing: camContext.isRefreshing
  };
}

/**
 * 释放CAM资源
 */
function releaseCamResources() {
  if (camContext) {
    console.log('开始释放CAM资源');
    
    // 停止图片流刷新
    _stopImageRefresh();
    
    // 清除加载超时定时器
    if (camContext.imageLoadingTimer) {
      clearTimeout(camContext.imageLoadingTimer);
      camContext.imageLoadingTimer = null;
    }
    
    // 清理图片URL缓存
    const imageUrls = [
      camContext.pageInstance?.data?.camImageUrl,
      camContext.pageInstance?.data?.camNextImageUrl
    ];
    
    imageUrls.forEach(url => {
      if (url && typeof url === 'string') {
        // 如果是blob URL，撤销它以释放内存
        if (url.startsWith('blob:')) {
          try {
            URL.revokeObjectURL(url);
            console.log('已撤销CAM图片URL');
          } catch (e) {
            // 忽略撤销错误
          }
        }
      }
    });
    
    // 清除上下文引用
    camContext = null;
    
    // 尝试触发垃圾回收
    if (typeof wx.triggerGC === 'function') {
      wx.triggerGC();
      console.log('CAM资源释放后触发垃圾回收');
    }
    
    console.log('CAM资源已完全释放');
  }
}

// 导出模块接口
module.exports = {
  initCamManager,
  connectCam,
  disconnectCam,
  setCamErrorCallback,
  getCamStatus,
  releaseCamResources,
  handleImageLoadSuccess,   // 新增：处理图片加载成功
  handleImageLoadError,     // 新增：处理图片加载错误
  CAM_CONFIG
}; 