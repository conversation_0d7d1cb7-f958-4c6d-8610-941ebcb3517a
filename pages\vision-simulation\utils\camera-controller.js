/**
 * 相机控制管理器
 * 负责处理相机的初始化、切换、错误处理等功能
 */

const cameraManager = require('./camera-manager');
const camManager = require('./cam-manager');
const utils = require('./utils');
const visionManager = require('./vision-manager');

class CameraController {
  constructor() {
    this.pageInstance = null;
    this.cameraCtx = null;
    this.retryTimer = null;
    this.cameraLoadingTimer = null;
    this.safetyTimers = [];
    this.retryCount = 0;
    this.lastFrameCheckTime = 0;
  }

  /**
   * 初始化相机控制器
   * @param {Object} pageInstance - 页面实例
   */
  init(pageInstance) {
    this.pageInstance = pageInstance;
    this.cameraCtx = null;
    this.retryCount = 0;
  }

  /**
   * 初始化相机类型状态
   */
  initializeCameraType() {
    // 根据当前的camMode和cameraPosition设置currentCameraType
    let currentCameraType;
    
    if (this.pageInstance.data.camMode) {
      currentCameraType = 'cam';
    } else {
      currentCameraType = this.pageInstance.data.cameraPosition; // 'back' 或 'front'
    }
    
    console.log('初始化相机类型:', currentCameraType);
    
    this.pageInstance.setData({
      currentCameraType: currentCameraType
    });
  }

  /**
   * 初始化相机
   */
  initCamera() {
    // CAM模式下不初始化手机相机
    if (this.pageInstance.data.camMode) {
      console.log('CAM模式下跳过手机相机初始化');
      return;
    }
    
    console.log('开始初始化相机，当前页面状态:', JSON.stringify({
      cameraLoading: this.pageInstance.data.cameraLoading,
      cameraError: this.pageInstance.data.cameraError,
      cameraInitializing: this.pageInstance.data.cameraInitializing
    }));
    
    // 先停止现有相机实例
    if (this.cameraCtx) {
      try {
        console.log('停止现有相机实例');
        cameraManager.stopCamera(this.cameraCtx);
        this.cameraCtx = null;
      } catch (err) {
        console.error('停止现有相机失败:', err);
      }
    }
    
    // 设置相机加载状态
    this.pageInstance.setData({
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: '',
      showCameraSettingBtn: false
    });
    
    console.log('调用相机管理器初始化相机');
    
    // 使用异步初始化相机，先检查权限再初始化
    cameraManager.initCamera(this.pageInstance)
      .then(cameraContext => {
        if (cameraContext) {
          this.cameraCtx = cameraContext;
          
          // 记录相机分辨率信息
          const currentResolution = this.pageInstance.data.resolutionOptions[this.pageInstance.data.currentResolutionIndex];
          if (!this.pageInstance.data.frame.width || !this.pageInstance.data.frame.height) {
            this.pageInstance.setData({
              'frame.width': currentResolution.width,
              'frame.height': currentResolution.height
            });
          }
          
          // 设置相机初始化成功标记
          this.pageInstance.lifecycleManager.cameraInitialized = true;
          
          console.log('相机初始化成功，分辨率:', currentResolution.width, 'x', currentResolution.height);
        } else {
          console.error('相机初始化失败，返回空的相机上下文');
          this.handleCameraError({
            errCode: 'camera_context_null',
            errMsg: '初始化失败，返回空的相机上下文'
          });
        }
      })
      .catch(error => {
        console.error('相机初始化异常:', error);
        this.handleCameraError(error);
      });
  }

  /**
   * 相机错误处理
   * @param {Object} error - 错误信息
   */
  handleCameraError(error) {
    console.log('处理相机错误:', error);
    utils.handleCameraError(error, this.pageInstance);
  }

  /**
   * 打开相机设置
   */
  openCameraSetting() {
    console.log('用户点击打开设置按钮');
    utils.openCameraSetting(this.pageInstance);
  }

  /**
   * 重试初始化相机
   */
  retryCameraInit() {
    console.log('重新初始化相机');
    // 清除任何现有的定时器
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }
    
    if (this.cameraLoadingTimer) {
      clearTimeout(this.cameraLoadingTimer);
      this.cameraLoadingTimer = null;
    }
    
    // 尝试先关闭任何可能存在的相机实例
    if (this.cameraCtx) {
      try {
        cameraManager.stopCamera(this.cameraCtx);
      } catch (err) {
        console.warn('停止旧相机实例时出错:', err);
      }
      this.cameraCtx = null;
    }
    
    // 重置相机状态
    this.pageInstance.setData({
      cameraError: false,
      cameraLoading: true,
      cameraInitializing: true,
      showCameraSettingBtn: false
    });
    
    // 增加小延迟再初始化，给系统时间释放资源
    this.retryTimer = setTimeout(() => {
      // 在初始化前再次检查页面是否隐藏
      if (this.pageInstance.lifecycleManager.pageHidden) {
        console.log('页面已隐藏，取消重新初始化相机');
        return;
      }
      
      console.log('执行相机重新初始化');
      this.initCamera();
      
      // 添加额外保障：如果相机初始化后5秒仍然处于加载状态，再次尝试
      const safetyTimer = setTimeout(() => {
        if (this.pageInstance.data && this.pageInstance.data.cameraLoading && !this.pageInstance.lifecycleManager.pageHidden) {
          console.log('安全检查：相机初始化5秒后仍在加载，再次尝试');
          // 递归调用，但最多尝试3次
          if (!this.retryCount) this.retryCount = 0;
          if (this.retryCount < 2) {
            this.retryCount++;
            this.retryCameraInit();
          } else {
            // 多次尝试失败，显示一个提示
            wx.showToast({
              title: '相机加载失败，请退出重试',
              icon: 'none',
              duration: 2000
            });
            // 自动重置重试计数
            setTimeout(() => {
              this.retryCount = 0;
            }, 5000);
          }
        } else {
          // 相机已正常加载或页面已隐藏，重置重试计数
          this.retryCount = 0;
        }
      }, 5000);
      
      // 当页面卸载时清除安全计时器
      this.safetyTimers.push(safetyTimer);
    }, 800);
  }

  /**
   * 重置相机状态
   */
  resetCameraState() {
    console.log('重置相机状态');
    if (this.cameraCtx) {
      cameraManager.stopCamera(this.cameraCtx);
      this.cameraCtx = null;
    }
    
    // 重置相机状态
    this.pageInstance.setData({
      cameraError: false,
      cameraLoading: false,
      cameraInitializing: false,
      showCameraSettingBtn: false
    });
  }

  /**
   * 切换到指定相机
   * @param {string} cameraType - 相机类型
   */
  switchToCamera(cameraType) {
    switch(cameraType) {
      case 'back':
      case 'front':
        // 切换到手机相机
        this.switchToPhoneCamera(cameraType);
        break;
      case 'cam':
        // 切换到硬件相机
        this.switchToHardwareCamera();
        break;
      default:
        console.error('未知的相机类型:', cameraType);
    }
  }

  /**
   * 切换到手机相机
   * @param {string} position - 相机位置 ('back' 或 'front')
   */
  switchToPhoneCamera(position) {
    // 如果当前是CAM模式，先关闭CAM
    if (this.pageInstance.data.camMode) {
      this.disableCamMode();
    }
    
    // 先停止现有相机，避免资源冲突
    if (this.cameraCtx) {
      cameraManager.stopCamera(this.cameraCtx);
      this.cameraCtx = null;
    }
    
    // 更新相机位置
    this.pageInstance.setData({
      cameraPosition: position,
      cameraLoading: true,
      // 重置低光状态，避免切换时的状态混乱
      isLowLight: false,
      averageBrightness: 0
    });
    
    // 等待数据更新完成后再初始化相机
    setTimeout(() => {
      // 重新初始化相机
      this.initCamera();
      
      // 显示切换提示
      const positionText = position === 'back' ? '后置相机' : '前置相机';
      wx.showToast({
        title: `已切换到${positionText}`,
        icon: 'none',
        duration: 1500
      });
    }, 100);
  }

  /**
   * 切换到硬件相机
   */
  switchToHardwareCamera() {
    console.log('切换到硬件相机');
    this.enableCamMode();
  }

  /**
   * 启用CAM模式
   */
  enableCamMode() {
    console.log('启用CAM模式，开始释放手机相机资源');

    // 先停止手机相机
    if (this.cameraCtx) {
      try {
        console.log('停止手机相机');
        cameraManager.stopCamera(this.cameraCtx);
        this.cameraCtx = null;
        this.pageInstance.lifecycleManager.cameraInitialized = false;
      } catch (err) {
        console.error('停止手机相机失败:', err);
      }
    }

    // 设置CAM模式状态
    this.pageInstance.setData({
      camMode: true,
      camStatus: 'connecting',
      cameraLoading: false,
      cameraError: false,
      // 重置相机相关状态
      isLowLight: false,
      isBrightLight: false,
      averageBrightness: 0,
      brightPixelRatio: 0,
      brightPixelRatioFormatted: '0.0'
    });

    // 开始连接CAM设备
    this.connectCamDevice();

    // 显示切换提示
    wx.showToast({
      title: '正在连接硬件相机...',
      icon: 'loading',
      duration: 2000
    });
  }

  /**
   * 禁用CAM模式
   */
  disableCamMode() {
    console.log('禁用CAM模式，准备恢复手机相机');

    // 停止CAM连接
    try {
      camManager.stopCamStream();
      console.log('CAM流已停止');
    } catch (err) {
      console.error('停止CAM流失败:', err);
    }

    // 清理CAM相关状态
    this.pageInstance.setData({
      camMode: false,
      camStatus: 'disconnected',
      camImageUrl: '',
      camNextImageUrl: '',
      camIsLoading: false,
      camErrorMsg: '',
      // 重置到默认相机位置
      cameraPosition: 'back',
      cameraLoading: true
    });

    // 清理CAM图片缓存
    this.pageInstance.lifecycleManager._forceCamImageCleanup();

    // 延迟初始化手机相机，给系统时间释放CAM资源
    setTimeout(() => {
      if (!this.pageInstance.data.camMode) {
        console.log('恢复手机相机');
        this.initCamera();
      }
    }, 500);

    // 显示切换提示
    wx.showToast({
      title: '已切换到手机相机',
      icon: 'none',
      duration: 1500
    });
  }

  /**
   * 连接CAM设备
   */
  connectCamDevice() {
    console.log('开始连接CAM设备');

    // 使用CAM管理器连接设备
    camManager.connectCamDevice(this.pageInstance)
      .then(() => {
        console.log('CAM设备连接成功');
        this.pageInstance.setData({
          camStatus: 'connected'
        });

        // 开始CAM图片流
        this.startCamStream();

        wx.showToast({
          title: '硬件相机连接成功',
          icon: 'success',
          duration: 1500
        });
      })
      .catch(error => {
        console.error('CAM设备连接失败:', error);
        this.handleCamError(error);
      });
  }

  /**
   * 开始CAM图片流
   */
  startCamStream() {
    console.log('开始CAM图片流');

    try {
      camManager.startCamStream(this.pageInstance);
    } catch (error) {
      console.error('启动CAM图片流失败:', error);
      this.handleCamError(error);
    }
  }

  /**
   * 处理CAM错误
   * @param {Object} error - 错误信息
   */
  handleCamError(error) {
    console.error('CAM错误:', error);

    this.pageInstance.setData({
      camStatus: 'error',
      camErrorMsg: error.message || '硬件相机连接失败'
    });

    // 显示错误提示
    wx.showToast({
      title: '硬件相机连接失败',
      icon: 'none',
      duration: 2000
    });

    // 自动切换回手机相机
    setTimeout(() => {
      this.disableCamMode();
    }, 3000);
  }

  /**
   * 重试CAM连接
   */
  retryCamConnection() {
    console.log('重试CAM连接');

    // 重置CAM状态
    this.pageInstance.setData({
      camStatus: 'connecting',
      camErrorMsg: ''
    });

    // 重新连接
    this.connectCamDevice();
  }

  /**
   * 处理CAM图片加载错误
   * @param {Object} e - 错误事件
   */
  handleCamImageError(e) {
    console.error('CAM图片加载错误:', e);

    // 增加错误计数
    this.pageInstance.lifecycleManager.camErrorCount++;

    // 如果错误次数过多，自动切换回手机相机
    if (this.pageInstance.lifecycleManager.camErrorCount > 5) {
      console.log('CAM图片加载错误次数过多，切换回手机相机');
      this.disableCamMode();
      return;
    }

    // 尝试重新加载
    setTimeout(() => {
      if (this.pageInstance.data.camMode) {
        this.startCamStream();
      }
    }, 1000);
  }

  /**
   * 处理CAM图片加载完成
   * @param {Object} e - 加载事件
   */
  handleCamImageLoad(e) {
    console.log('CAM图片加载成功:', e.detail);

    // 重置错误计数
    this.pageInstance.lifecycleManager.camErrorCount = 0;

    // 更新加载状态
    this.pageInstance.setData({
      camIsLoading: false
    });
  }

  /**
   * 重新初始化相机
   */
  reinitializeCamera() {
    // 先停止现有相机
    if (this.cameraCtx) {
      cameraManager.stopCamera(this.cameraCtx);
      this.cameraCtx = null;
    }

    // 重新初始化
    this.initCamera();
  }

  /**
   * 清理相机控制器资源
   */
  cleanup() {
    // 清除定时器
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }

    if (this.cameraLoadingTimer) {
      clearTimeout(this.cameraLoadingTimer);
      this.cameraLoadingTimer = null;
    }

    // 清除安全定时器
    this.safetyTimers.forEach(timer => {
      clearTimeout(timer);
    });
    this.safetyTimers = [];

    // 停止相机
    if (this.cameraCtx) {
      try {
        cameraManager.stopCamera(this.cameraCtx);
      } catch (err) {
        console.error('清理相机资源失败:', err);
      }
      this.cameraCtx = null;
    }

    // 重置状态
    this.retryCount = 0;
    this.lastFrameCheckTime = 0;
  }

  /**
   * 获取相机状态
   */
  getCameraState() {
    return {
      cameraCtx: this.cameraCtx,
      retryCount: this.retryCount,
      lastFrameCheckTime: this.lastFrameCheckTime,
      isInitialized: !!this.cameraCtx
    };
  }
}

module.exports = CameraController;
