/**
 * 相机管理模块
 * 负责相机的初始化和帧处理
 */

// 导入权限管理模块
const permissionManager = require('./permission-manager');

// 全局变量，用于跟踪帧监听器状态
let _frameListenerActive = false;
let _frameListenerTimeout = null;
let _initRetryCount = 0;  // 初始化重试计数
let _authRetryCount = 0;  // 权限重试计数
const MAX_RETRY_COUNT = 3; // 最大重试次数

/**
 * 相机权限检查函数
 * 使用权限管理模块申请相机权限
 */
function checkCameraAuth() {
  console.log('调用相机权限检查函数');
  return permissionManager.requestCameraPermission();
}

/**
 * 初始化相机
 * 直接初始化相机，依赖相机组件自身的权限申请机制
 * @param {Object} page 页面实例
 * @returns {Promise} 初始化结果
 */
function initCamera(page) {
  // 重置重试计数（如果是新的初始化请求）
  if (!page.data.cameraInitializing) {
    _initRetryCount = 0;
  }
  
  // 设置初始化状态
  page.setData({
    cameraInitializing: true
  });
  
  console.log(`开始初始化相机，当前重试次数: ${_initRetryCount}`);
  
  return new Promise((resolve, reject) => {
    // 先清理现有资源
    if (_frameListenerTimeout) {
      clearTimeout(_frameListenerTimeout);
      _frameListenerTimeout = null;
    }
    
    // 设置相机加载状态
    page.setData({
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: ''
    });
    
    // 获取系统信息
    wx.getSystemInfo({
      success: (sysInfo) => {
        const isWindowsEnv = sysInfo.platform === 'windows' || sysInfo.platform === 'devtools';
        console.log('当前系统环境:', sysInfo.platform, isWindowsEnv ? '(Windows开发环境)' : '');
        
        // 在Windows环境中增加额外的延迟
        const initDelay = isWindowsEnv ? 1000 : 500;
        
        // 延迟执行初始化，给页面元素更多时间渲染
        setTimeout(() => {
          try {
            console.log('开始初始化相机，当前位置:', page.data.cameraPosition);
            
            // 创建相机上下文
            try {
              // 创建相机上下文
              const cameraContext = wx.createCameraContext();
              
              if (!cameraContext) {
                console.error('创建相机上下文失败，返回空值');
                throw new Error('创建相机上下文失败');
              }
              
              console.log('相机上下文创建成功，准备创建帧监听器');
              
              // 添加错误处理
              try {
                // 创建帧监听器
                const listener = cameraContext.onCameraFrame((frame) => {
                  // 设置帧监听器活动状态
                  _frameListenerActive = true;
                  
                  // 如果页面已卸载或相机出错，则不处理帧
                  if (!page || page.data.cameraError) return;
                  
                  // 更新加载状态
                  if (page.data.cameraLoading) {
                    console.log('收到第一帧数据，相机初始化成功');
                    page.setData({
                      cameraLoading: false,
                      cameraInitializing: false
                    });
                  }
                  
                  // 处理相机帧
                  page.processFrameWebGL(frame);
                });
                
                if (!listener) {
                  console.error('创建帧监听器失败，返回空值');
                  throw new Error('创建帧监听器失败');
                }
                
                console.log('帧监听器创建成功，准备启动');
                
                // 开始监听帧数据
                listener.start();
                
                // 将监听器添加到相机上下文中，便于后续停止
                cameraContext.listener = listener;
                
                console.log('帧监听器启动成功，设置检查定时器');
                
                // 设置定时器检查帧监听器状态
                _frameListenerTimeout = setTimeout(() => {
                  checkFrameListener(page, cameraContext);
                }, 3000);
                
                // 初始化成功
                resolve(cameraContext);
              } catch (listenerError) {
                console.error('创建帧监听器异常:', listenerError);
                handleInitError(page, {
                  errCode: 'frame_listener_error',
                  errMsg: '创建帧监听器失败',
                  detail: listenerError
                });
                reject(listenerError);
              }
            } catch (contextError) {
              console.error('创建相机上下文异常:', contextError);
              handleInitError(page, {
                errCode: 'camera_context_error',
                errMsg: '创建相机上下文失败',
                detail: contextError
              });
              reject(contextError);
            }
          } catch (error) {
            console.error('初始化相机过程中发生异常:', error);
            handleInitError(page, {
              errCode: 'init_process_error',
              errMsg: '初始化相机过程中发生异常',
              detail: error
            });
            reject(error);
          }
        }, initDelay);
      },
      fail: (error) => {
        console.error('获取系统信息失败:', error);
        handleInitError(page, error);
        reject(error);
      }
    });
  });
}

// 处理初始化错误
function handleInitError(page, error) {
  // 增加重试计数
  _initRetryCount++;
  
  // 构建错误消息
  let errorMsg = '相机启动失败，请检查相机权限或重启应用';
  let errorCode = 'unknown';
  let showSettingBtn = false;
  
  if (error) {
    console.error('相机错误详情:', JSON.stringify(error));
    
    if (error.errCode) {
      errorCode = error.errCode;
      
      // 根据错误码设置错误消息
      switch(error.errCode) {
        case 'auth_denied':
          errorMsg = '相机权限被拒绝，请点击下方按钮前往设置开启权限';
          showSettingBtn = true;
          break;
        case 'auth_failed':
          errorMsg = '相机权限申请失败，请点击下方按钮重试或前往设置开启权限';
          showSettingBtn = true;
          break;
        case 'camera_occupied':
          errorMsg = '相机被其他应用占用，请关闭其他使用相机的应用后重试';
          break;
        case 'get_setting_failed':
          errorMsg = '获取相机权限设置失败，请重启小程序后重试';
          break;
        case 'camera_context_error':
          errorMsg = '创建相机上下文失败，请检查相机权限或重启应用';
          showSettingBtn = true;
          break;
        case 'frame_listener_error':
          errorMsg = '创建相机帧监听器失败，请重启应用后重试';
          break;
        case 'auth_check_in_progress':
        case 'auth_check_too_frequent':
          // 这些是临时错误，不需要显示给用户，直接重试
          errorMsg = '权限检查中，请稍候...';
          _initRetryCount--; // 不计入重试次数
          break;
        default:
          errorMsg = '相机启动失败，请检查相机权限或重启应用';
      }
    }
    
    // 如果没有错误码但有错误消息，尝试从错误消息中提取信息
    if (!error.errCode && error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMsg = '相机权限被拒绝，请点击下方按钮前往设置开启权限';
        errorCode = 'auth_denied';
        showSettingBtn = true;
      } else if (error.errMsg.includes('camera occupied')) {
        errorMsg = '相机被其他应用占用，请关闭其他使用相机的应用后重试';
        errorCode = 'camera_occupied';
      } else if (error.errMsg.includes('fail')) {
        // 其他失败情况
        errorMsg = `相机启动失败: ${error.errMsg}`;
        errorCode = 'camera_init_failed';
      }
    }
  }
  
  // 设置错误状态
  page.setData({
    cameraError: true,
    cameraLoading: false,
    cameraErrorMsg: errorMsg,
    cameraErrorCode: errorCode,
    showCameraSettingBtn: showSettingBtn,
    cameraInitializing: _initRetryCount < MAX_RETRY_COUNT // 如果还有重试机会，保持初始化状态
  });
  
  // 如果是权限检查中或请求过于频繁，使用短延迟重试
  if (errorCode === 'auth_check_in_progress' || errorCode === 'auth_check_too_frequent') {
    setTimeout(() => {
      if (page) {
        console.log('权限检查临时错误，短延迟后重试');
        page.setData({
          cameraError: false,
          cameraLoading: true
        });
        initCamera(page);
      }
    }, 800);
    return;
  }
  
  // 如果未达到最大重试次数，则延迟重试
  if (_initRetryCount < MAX_RETRY_COUNT) {
    const delay = 1000 * Math.pow(2, _initRetryCount - 1); // 指数退避延迟：1秒、2秒、4秒...
    console.log(`相机初始化失败，${delay}ms后第${_initRetryCount}次重试...`);
    
    setTimeout(() => {
      if (page) {
        // 重置错误状态，准备重试
        page.setData({
          cameraError: false,
          cameraLoading: true
        });
        initCamera(page);
      }
    }, delay);
  } else {
    console.error(`相机初始化失败，已达到最大重试次数(${MAX_RETRY_COUNT})，不再重试`);
    
    // 显示最终错误提示
    wx.showToast({
      title: '相机启动失败',
      icon: 'none',
      duration: 2000
    });
  }
}

// 帧活动计数器，用于跟踪帧监听器健康状态
let _frameActivityCounter = 0;
const FRAME_ACTIVITY_THRESHOLD = 2; // 允许连续多少次检查不活动

// 检查帧监听器状态
function checkFrameListener(page, cameraContext) {
  // 清除定时器
  _frameListenerTimeout = null;
  
  console.log('检查帧监听器状态:', _frameListenerActive ? '活动中' : '未活动', '连续不活动计数:', _frameActivityCounter);
  
  // 如果帧监听器不活动，增加计数器
  if (!_frameListenerActive) {
    _frameActivityCounter++;
  } else {
    // 如果活动，重置计数器
    _frameActivityCounter = 0;
  }
  
  // 如果连续多次不活动，重新初始化相机
  if (_frameActivityCounter >= FRAME_ACTIVITY_THRESHOLD && page && !page.data.cameraError) {
    console.log(`帧监听器连续${_frameActivityCounter}次不活动，重新初始化相机`);
    
    // 重置计数器
    _frameActivityCounter = 0;
    
    // 停止当前相机
    stopCamera(cameraContext);
    
    // 重置状态
    _frameListenerActive = false;
    
    // 设置相机状态
    page.setData({
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: ''
    });
    
    // 重新初始化相机
    setTimeout(() => {
      if (page) {
        // 重置重试计数，这是一个新的初始化请求
        _initRetryCount = 0;
        _authRetryCount = 0; // 同时重置权限重试计数
        
        console.log('延迟后重新初始化相机');
        page.initCamera();
      }
    }, 1000); // 增加延迟，给系统更多时间释放相机资源
    
    return;
  }
  
  // 重置帧监听器活动状态，准备下一次检查
  _frameListenerActive = false;
  
  // 设置下一次检查
  if (page && !page.data.cameraError) {
    _frameListenerTimeout = setTimeout(() => {
      checkFrameListener(page, cameraContext);
    }, 3000);
  }
}

// 停止相机
function stopCamera(cameraContext) {
  if (cameraContext) {
    try {
      console.log('停止相机');
      
      // 清除定时器
      if (_frameListenerTimeout) {
        clearTimeout(_frameListenerTimeout);
        _frameListenerTimeout = null;
      }
      
      // 停止帧监听
      if (cameraContext.listener) {
        cameraContext.listener.stop();
      }
      
      // 重置状态
      _frameListenerActive = false;
    } catch (error) {
      console.error('停止相机错误:', error);
    }
  }
}

// 打开相机设置页面
function openCameraSetting() {
  return new Promise((resolve, reject) => {
    wx.openSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          resolve(true); // 用户已授权
        } else {
          resolve(false); // 用户未授权
        }
      },
      fail: (err) => {
        console.error('打开设置页面失败:', err);
        reject(err);
      }
    });
  });
}

module.exports = {
  initCamera,
  stopCamera,
  checkCameraAuth,
  openCameraSetting
};
