/**
 * 设备性能检测模块
 * 负责检测设备性能并提供适当的配置参数
 */

// 性能级别定义
const PERFORMANCE_LEVELS = {
  LOW: 'low',       // 低端设备
  MEDIUM: 'medium', // 中端设备
  HIGH: 'high'      // 高端设备
};

// 分辨率配置
const RESOLUTION_CONFIG = {
  [PERFORMANCE_LEVELS.LOW]: {
    width: 640,
    height: 480,
    frameSize: 'small',
    resolution: 'low'
  },
  [PERFORMANCE_LEVELS.MEDIUM]: {
    width: 1280,
    height: 720,
    frameSize: 'medium',
    resolution: 'medium'
  },
  [PERFORMANCE_LEVELS.HIGH]: {
    width: 1920,
    height: 1080,
    frameSize: 'large',
    resolution: 'high'
  }
};

// 采样策略配置
const SAMPLING_CONFIG = {
  [PERFORMANCE_LEVELS.LOW]: {
    sampleStep: 50,     // 采样步长
    sampleRadius: 0.2,  // 采样半径（相对于图像尺寸）
    useAdaptiveSampling: false // 是否使用自适应采样
  },
  [PERFORMANCE_LEVELS.MEDIUM]: {
    sampleStep: 30,
    sampleRadius: 0.3,
    useAdaptiveSampling: true
  },
  [PERFORMANCE_LEVELS.HIGH]: {
    sampleStep: 15,
    sampleRadius: 0.4,
    useAdaptiveSampling: true
  }
};

// 渲染质量配置
const RENDER_QUALITY_CONFIG = {
  [PERFORMANCE_LEVELS.LOW]: {
    useSimpleShaders: true,  // 使用简化着色器
    enableNoise: false,      // 禁用噪点效果
    enableAdvancedEffects: false // 禁用高级效果
  },
  [PERFORMANCE_LEVELS.MEDIUM]: {
    useSimpleShaders: false,
    enableNoise: false,
    enableAdvancedEffects: true
  },
  [PERFORMANCE_LEVELS.HIGH]: {
    useSimpleShaders: false,
    enableNoise: true,
    enableAdvancedEffects: true
  }
};

// 当前性能级别
let currentPerformanceLevel = PERFORMANCE_LEVELS.MEDIUM; // 默认为中等性能

/**
 * 检测设备性能
 * @returns {string} 性能级别
 */
function detectDevicePerformance() {
  try {
    // 获取设备信息
    const systemInfo = wx.getSystemInfoSync();
    const { platform, brand, model, benchmarkLevel } = systemInfo;
    
    console.log('设备信息:', platform, brand, model, benchmarkLevel);
    
    // 根据微信提供的性能等级判断
    // benchmarkLevel: 设备性能等级，取值为：-2, -1, 0, 1, 2, 3, 4, 5
    // 其中 -2 为未知级别，-1 为极低，0 为低，1 为中等，2 为高，3 为极高，4 为超高，5 为最高
    if (benchmarkLevel >= 3) {
      return PERFORMANCE_LEVELS.HIGH;
    } else if (benchmarkLevel >= 1) {
      return PERFORMANCE_LEVELS.MEDIUM;
    } else {
      return PERFORMANCE_LEVELS.LOW;
    }
  } catch (e) {
    console.error('设备性能检测失败:', e);
    return PERFORMANCE_LEVELS.MEDIUM; // 默认为中等性能
  }
}

/**
 * 初始化设备性能检测
 */
function initDevicePerformance() {
  currentPerformanceLevel = detectDevicePerformance();
  console.log('当前设备性能级别:', currentPerformanceLevel);
  return currentPerformanceLevel;
}

/**
 * 获取当前性能级别
 * @returns {string} 性能级别
 */
function getCurrentPerformanceLevel() {
  return currentPerformanceLevel;
}

/**
 * 获取当前性能级别的分辨率配置
 * @returns {Object} 分辨率配置
 */
function getResolutionConfig() {
  return RESOLUTION_CONFIG[currentPerformanceLevel];
}

/**
 * 获取当前性能级别的采样配置
 * @returns {Object} 采样配置
 */
function getSamplingConfig() {
  return SAMPLING_CONFIG[currentPerformanceLevel];
}

/**
 * 获取当前性能级别的渲染质量配置
 * @returns {Object} 渲染质量配置
 */
function getRenderQualityConfig() {
  return RENDER_QUALITY_CONFIG[currentPerformanceLevel];
}

/**
 * 手动设置性能级别（用于测试）
 * @param {string} level 性能级别
 */
function setPerformanceLevel(level) {
  if (PERFORMANCE_LEVELS[level.toUpperCase()]) {
    currentPerformanceLevel = PERFORMANCE_LEVELS[level.toUpperCase()];
    console.log('手动设置性能级别为:', currentPerformanceLevel);
  } else {
    console.error('无效的性能级别:', level);
  }
}

// 导出模块
module.exports = {
  PERFORMANCE_LEVELS,
  initDevicePerformance,
  getCurrentPerformanceLevel,
  getResolutionConfig,
  getSamplingConfig,
  getRenderQualityConfig,
  setPerformanceLevel
};
