/**
 * 增强版视觉管理器
 * 协调所有视觉处理模块，提供统一的接口
 * 保持与原有业务流程的完全兼容性
 */

// 导入相关模块
const visionProcessor = require('./vision-processor');
const webglRenderer = require('./webgl-renderer');
const nightVisionEnhancer = require('./night-vision-enhancer');
const visionConfig = require('./vision-config');
const visionManager = require('./vision-manager'); // 导入原有的视觉管理器

/**
 * 增强版帧处理函数
 * @param {Object} webglContext - WebGL上下文
 * @param {Object} frame - 相机帧数据
 * @param {Object} options - 处理选项
 * @param {Object} features - 功能开关
 * @returns {Object} 处理结果
 */
function processEnhancedFrame(webglContext, frame, options = {}, features = {}) {
  try {
    const startTime = Date.now();
    
    // 获取动物类型
    const animalType = options.animalType || 'dog';
    
    // 检查是否启用增强夜视
    const enableEnhancedNightVision = features.enhancedNightVision !== false; // 默认启用
    
    // 初始化渲染配置
    let renderConfig = {
      animalType: animalType,
      dichromaticVision: options.dichromaticVision || visionConfig.DICHROMATIC_VISION_CONFIG,
      visualAcuity: options.visualAcuity || visionConfig.VISUAL_ACUITY_CONFIG,
      nightVision: options.nightVision || visionConfig.NIGHT_VISION_CONFIG,
      brightLightAdaptation: options.brightLightAdaptation || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG,
      dayVision: options.dayVision,
      isLowLight: options.isLowLight || false,
      isBrightLight: options.isBrightLight || false
    };
    
    // 如果启用增强夜视且处于低光环境，应用增强处理
    if (enableEnhancedNightVision && renderConfig.isLowLight) {
      // 使用增强版视觉处理器
      const enhancedResult = visionProcessor.applyEnhancedNightVision(
        frame.data, 
        frame.width, 
        frame.height, 
        {
          animalType: animalType,
          currentLowLightState: renderConfig.isLowLight
        }
      );
      
      // 如果获得了增强参数，添加到渲染配置中
      if (enhancedResult.enhancementParams) {
        renderConfig.enhancementParams = enhancedResult.enhancementParams;
        renderConfig.isLowLight = enhancedResult.lightInfo.isLowLight;
        
        // 计算处理时间用于性能优化
        const processingTime = Date.now() - startTime;
        renderConfig.enhancementParams.processingTime = processingTime;
      }
      
      // 使用增强后的帧数据
      frame = {
        ...frame,
        data: enhancedResult.enhancedData
      };
    }
    
    // 使用WebGL渲染器进行最终渲染
    webglRenderer.renderFrame(webglContext, frame, renderConfig, features);
    
    // 返回处理结果
    return {
      success: true,
      processingTime: Date.now() - startTime,
      enhancementApplied: renderConfig.enhancementParams ? true : false,
      lightLevel: renderConfig.enhancementParams ? renderConfig.enhancementParams.lightLevel : 'NORMAL'
    };
    
  } catch (error) {
    console.error('增强视觉处理错误:', error);
    
    // 发生错误时回退到原有处理方式
    try {
      webglRenderer.renderFrame(webglContext, frame, options, features);
      return {
        success: true,
        processingTime: Date.now() - startTime,
        enhancementApplied: false,
        fallbackUsed: true,
        error: error.message
      };
    } catch (fallbackError) {
      console.error('回退处理也失败:', fallbackError);
      return {
        success: false,
        error: fallbackError.message
      };
    }
  }
}

/**
 * 兼容性包装器 - 提供与原有vision-manager相同的接口
 * @param {Object} webglContext - WebGL上下文
 * @param {Object} frame - 相机帧数据  
 * @param {Object} config - 配置参数
 * @param {Object} features - 功能开关
 */
function renderFrame(webglContext, frame, config = {}, features = {}) {
  // 检查是否应该使用增强功能
  const useEnhancedProcessing = features.enhancedNightVision !== false && 
                               (config.isLowLight || features.nightVision);
  
  if (useEnhancedProcessing) {
    // 使用增强处理
    return processEnhancedFrame(webglContext, frame, config, features);
  } else {
    // 使用原有处理方式
    webglRenderer.renderFrame(webglContext, frame, config, features);
    return {
      success: true,
      enhancementApplied: false,
      usingOriginalProcessor: true
    };
  }
}

/**
 * 获取增强配置
 */
function getEnhancementConfig() {
  return nightVisionEnhancer.getEnhancementConfig();
}

/**
 * 重置增强状态
 */
function resetEnhancementState() {
  nightVisionEnhancer.resetAdaptiveState();
}

/**
 * 性能监控和优化建议
 * @param {number} processingTime - 处理时间(ms)
 * @returns {Object} 性能信息和建议
 */
function getPerformanceInfo(processingTime) {
  const targetFrameTime = 16.67; // 60fps
  const performanceRatio = processingTime / targetFrameTime;
  
  let recommendation = 'optimal';
  if (performanceRatio > 1.5) {
    recommendation = 'reduce_quality';
  } else if (performanceRatio > 1.2) {
    recommendation = 'monitor';
  } else if (performanceRatio < 0.8) {
    recommendation = 'can_increase_quality';
  }
  
  return {
    processingTime,
    targetFrameTime,
    performanceRatio,
    recommendation,
    isDroppedFrame: performanceRatio > 1.0
  };
}

/**
 * 获取调试信息
 * @param {Object} options - 调试选项
 */
function getDebugInfo(options = {}) {
  return {
    enhancementConfig: nightVisionEnhancer.getEnhancementConfig(),
    visionConfig: visionConfig.getFullConfig(),
    moduleStatus: {
      visionProcessor: 'loaded',
      webglRenderer: 'loaded', 
      nightVisionEnhancer: 'loaded',
      originalVisionManager: 'loaded'
    },
    version: '1.0.0',
    compatibilityMode: options.legacyMode || false
  };
}

// 导出所有功能，保持向后兼容
module.exports = {
  // 新增的增强功能
  processEnhancedFrame,
  getEnhancementConfig,
  resetEnhancementState,
  getPerformanceInfo,
  getDebugInfo,
  
  // 兼容性接口 - 与原有vision-manager保持一致
  renderFrame,
  
  // 重新导出原有模块的功能以保持兼容性
  initWebGL: webglRenderer.initWebGL,
  releaseWebGLResources: webglRenderer.releaseWebGLResources,
  
  // 重新导出视觉处理器功能
  detectLightLevel: visionProcessor.detectLightLevel,
  detectBrightLight: visionProcessor.detectBrightLight,
  applyNightVisionEnhancement: visionProcessor.applyNightVisionEnhancement,
  applyBrightLightAdaptation: visionProcessor.applyBrightLightAdaptation,
  convertToDichromaticVision: visionProcessor.convertToDichromaticVision,
  processFrame: visionProcessor.processFrame,
  
  // 新增的增强功能
  applyEnhancedNightVision: visionProcessor.applyEnhancedNightVision,
  getNightVisionEnhancementConfig: visionProcessor.getNightVisionEnhancementConfig,
  resetNightVisionEnhancement: visionProcessor.resetNightVisionEnhancement
}; 