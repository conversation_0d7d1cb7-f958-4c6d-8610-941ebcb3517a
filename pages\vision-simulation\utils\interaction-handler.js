/**
 * 用户交互处理器
 * 负责处理触摸事件、手势识别、UI交互等功能
 */

const uiController = require('./ui-controller');
const visionManager = require('./vision-manager');

class InteractionHandler {
  constructor() {
    this.pageInstance = null;
    this.lastTapTime = 0; // 上次点击时间
    this.touchStartX = 0; // 触摸开始位置
    this.touchEndX = 0; // 触摸结束位置
    this.touchStartTime = 0; // 触摸开始时间
  }

  /**
   * 初始化交互处理器
   * @param {Object} pageInstance - 页面实例
   */
  init(pageInstance) {
    this.pageInstance = pageInstance;
    this.lastTapTime = 0;
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.touchStartTime = 0;
  }

  /**
   * 处理触摸开始事件
   * @param {Object} e - 触摸事件
   */
  handleTouchStart(e) {
    this.touchStartX = e.touches[0].clientX;
    this.touchStartTime = Date.now();
  }

  /**
   * 处理触摸结束事件 - 检测滑动手势
   * @param {Object} e - 触摸事件
   */
  handleTouchEnd(e) {
    this.touchEndX = e.changedTouches[0].clientX;
    
    // 计算滑动距离
    const swipeDistance = this.touchEndX - this.touchStartX;
    
    // 判断是否为有效滑动（距离超过50像素）
    if (Math.abs(swipeDistance) > 50) {
      if (swipeDistance > 0) {
        // 向右滑动，切换到爱宠视角
        if (this.pageInstance.data.currentView === 'human') {
          this.toggleView();
          // 显示切换提示 - 区分猫科和犬科动物
          let animalIcon = this.pageInstance.data.features.isCat ? '😺 ' : '🐶 ';
          wx.showToast({
            title: '切换到' + animalIcon + this.pageInstance.data.breedName + '视角',
            icon: 'none',
            duration: 1000
          });
        }
      } else {
        // 向左滑动，切换到人类视角
        if (this.pageInstance.data.currentView === 'dog') {
          this.toggleView();
          // 显示切换提示
          wx.showToast({
            title: '切换到人类视角',
            icon: 'none',
            duration: 1000
          });
        }
      }
    } else {
      // 如果是短按，也可以切换视角
      if (Date.now() - this.touchStartTime < 300) {
        this.toggleView();
      }
    }
  }

  /**
   * 处理相机区域点击事件 - 双击控制显示/隐藏
   */
  handleViewAreaTap() {
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - this.lastTapTime;
    
    // 判断是否为双击 (500毫秒内的两次点击)
    if (timeDiff < 500) {
      // 双击事件，切换控制面板显示/隐藏
      this.toggleControlPanel();
      this.lastTapTime = 0; // 重置点击时间，避免连续触发
    } else {
      // 记录单击时间
      this.lastTapTime = currentTime;
    }
  }

  /**
   * 切换视角
   */
  toggleView() {
    uiController.toggleView(this.pageInstance);
  }

  /**
   * 切换控制面板显示/隐藏状态
   */
  toggleControlPanel() {
    const newState = !this.pageInstance.data.showControls;
    this.pageInstance.setData({
      showControls: newState
    });
    
    // 记录日志
    console.log('切换控制面板状态:', newState ? '显示' : '隐藏');
  }

  /**
   * 切换分析区域展开/收起状态
   */
  toggleAnalysisExpand() {
    const newState = !this.pageInstance.data.isAnalysisExpanded;
    this.pageInstance.setData({
      isAnalysisExpanded: newState
    });
    
    // 记录日志
    console.log('切换分析区域状态:', newState ? '展开' : '收起');
  }

  /**
   * 显示/隐藏相机选择器
   */
  toggleCameraSelector() {
    this.pageInstance.setData({
      showCameraSelector: !this.pageInstance.data.showCameraSelector
    });
  }

  /**
   * 选择相机类型
   * @param {Object} e - 事件对象
   */
  selectCameraType(e) {
    const cameraType = e.currentTarget.dataset.type;
    
    // 如果选择的是当前相机类型，仅关闭选择器
    if (cameraType === this.pageInstance.data.currentCameraType) {
      this.toggleCameraSelector();
      return;
    }
    
    console.log('切换相机类型:', this.pageInstance.data.currentCameraType, '->', cameraType);
    
    // 更新当前相机类型
    this.pageInstance.setData({
      currentCameraType: cameraType,
      showCameraSelector: false
    });
    
    // 根据选择的相机类型执行相应操作
    this.pageInstance.cameraController.switchToCamera(cameraType);
    
    // 添加触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  }

  /**
   * 防止触摸事件穿透
   * @param {Object} e - 触摸事件
   */
  preventTouchMove(e) {
    // 阻止触摸事件传播
    return false;
  }

  /**
   * 防止冒泡
   * @param {Object} e - 事件对象
   */
  preventBubble(e) {
    return false;
  }

  /**
   * 标签页切换 - 点击切换
   * @param {Object} e - 事件对象
   */
  changeTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    uiController.changeTab(index, this.pageInstance);
  }

  /**
   * 标签页切换 - 滑动切换
   * @param {Object} e - 事件对象
   */
  swiperChange(e) {
    uiController.swiperChange(e, this.pageInstance);
  }

  /**
   * 视觉参数滑块变化处理 - 清晰度因子
   * @param {Object} e - 事件对象
   */
  onResolutionChange(e) {
    const resolutionFactor = e.detail.value;
    
    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor
    });
    
    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: resolutionFactor
      });
      
      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 视觉参数滑块变化处理 - 视野因子
   * @param {Object} e - 事件对象
   */
  onAntiAliasChange(e) {
    const viewFieldFactor = e.detail.value;
    
    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.antiAliasFactor': viewFieldFactor
    });
    
    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateVisualAcuityConfig({
        VIEW_FIELD_FACTOR: viewFieldFactor
      });
      
      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 视觉参数滑块变化处理 - 亮度增强
   * @param {Object} e - 事件对象
   */
  onBrightnessChange(e) {
    const value = e.detail.value;
    const brightness = 1 + value / 50;
    
    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.brightness': brightness
    });
    
    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness
      });
      
      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 视觉参数滑块变化处理 - 对比度
   * @param {Object} e - 事件对象
   */
  onContrastChange(e) {
    const value = e.detail.value;
    const contrast = 1 + value / 50;

    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.contrast': contrast
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        CONTRAST: contrast
      });

      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 运动敏感度滑块变化处理
   * @param {Object} e - 事件对象
   */
  onMotionSensitivityChange(e) {
    const value = e.detail.value;

    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.motionSensitivity': value
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateMotionVisionConfig({
        MOTION_SENSITIVITY: value
      });

      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 运动阈值滑块变化处理
   * @param {Object} e - 事件对象
   */
  onMotionThresholdChange(e) {
    const value = e.detail.value;

    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.motionThreshold': value
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateMotionVisionConfig({
        MOTION_THRESHOLD: value
      });

      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 运动物体大小阈值滑块变化处理
   * @param {Object} e - 事件对象
   */
  onMotionSizeThresholdChange(e) {
    const value = e.detail.value;

    // 更新UI显示的参数值
    this.pageInstance.setData({
      'dogVisionParams.motionSizeThreshold': value
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateMotionVisionConfig({
        MOTION_SIZE_THRESHOLD: value
      });

      // 如果有最后一帧，重新渲染
      if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
  }

  /**
   * 保存爱宠视角图片
   */
  savePetVisionImage() {
    const that = this.pageInstance;

    // 检查是否有可用的帧数据
    if (!that._lastFrame || !that.webglContext) {
      wx.showToast({
        title: '暂无图像数据，请稍后再试',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示保存中提示
    wx.showLoading({
      title: '正在保存图片...',
      mask: true
    });

    try {
      // 使用WebGL上下文保存图片
      that._saveCanvasToAlbum();
    } catch (error) {
      console.error('保存图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 运动预设循环切换函数 - 简化版本，仅保留接口
   */
  nextMotionPreset() {
    wx.showToast({
      title: '已切换运动预设',
      icon: 'none',
      duration: 1500
    });
  }

  /**
   * 运动视觉风格切换函数 - 简化版本，仅保留接口
   */
  toggleMotionStyle() {
    wx.showToast({
      title: '已切换运动视觉风格',
      icon: 'none',
      duration: 1500
    });
  }

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  }

  /**
   * 检查是否需要显示视觉功能层级引导
   */
  checkShowVisionGuide() {
    // 从本地存储中读取用户设置
    const hideVisionGuide = wx.getStorageSync('hideVisionGuide');

    if (!hideVisionGuide) {
      // 显示引导
      this.pageInstance.setData({
        showVisionGuide: true
      });
    }
  }

  /**
   * 关闭视觉功能层级引导
   */
  closeVisionGuide() {
    this.pageInstance.setData({
      showVisionGuide: false
    });

    // 如果用户选择了"不再显示"，保存到本地存储
    if (this.pageInstance.data.rememberVisionGuide) {
      wx.setStorageSync('hideVisionGuide', true);
    }
  }

  /**
   * 切换"不再显示"选项
   */
  toggleRememberGuide() {
    this.pageInstance.setData({
      rememberVisionGuide: !this.pageInstance.data.rememberVisionGuide
    });
  }

  /**
   * 显示运动视觉引导提示
   */
  showMotionVisionGuideTip() {
    this.pageInstance.setData({
      showMotionGuideTip: true
    });

    // 3秒后自动隐藏
    setTimeout(() => {
      this.pageInstance.setData({
        showMotionGuideTip: false
      });
    }, 3000);
  }

  /**
   * 获取交互状态
   * @returns {Object} 交互状态信息
   */
  getInteractionState() {
    return {
      lastTapTime: this.lastTapTime,
      touchStartX: this.touchStartX,
      touchEndX: this.touchEndX,
      touchStartTime: this.touchStartTime
    };
  }

  /**
   * 重置交互状态
   */
  resetInteractionState() {
    this.lastTapTime = 0;
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.touchStartTime = 0;
  }
}

module.exports = InteractionHandler;
