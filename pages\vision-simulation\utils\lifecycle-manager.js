/**
 * 页面生命周期管理器
 * 负责处理页面的生命周期事件和相关逻辑
 */

const utils = require('./utils');
const visionProcessor = require('./vision-processor');
const cameraManager = require('./camera-manager');
const camManager = require('./cam-manager');
const tipsManager = require('./tips-manager');
const uiController = require('./ui-controller');
const webglRenderer = require('./webgl-renderer');

class LifecycleManager {
  constructor() {
    this.pageInstance = null;
    this.isPageLoaded = false;
    this.cameraInitialized = false;
    this.webglInitialized = false;
    this.pageHidden = false;
    this.authRetryCount = 0;
    this.retryTimer = null;
    this.cameraLoadingTimer = null;
    this.safetyTimers = [];
    this.fpsUpdateInterval = null;
    this.camErrorCount = 0;
  }

  /**
   * 初始化生命周期管理器
   * @param {Object} pageInstance - 页面实例
   */
  init(pageInstance) {
    this.pageInstance = pageInstance;
    this.isPageLoaded = true;
    this.cameraInitialized = false;
    this.webglInitialized = false;
    this.pageHidden = false;
  }

  /**
   * 页面加载处理
   * @param {Object} options - 页面参数
   */
  async onLoad(options) {
    console.log('页面加载，开始初始化');
    
    // 初始化状态标记
    this.isPageLoaded = true;
    this.cameraInitialized = false;
    this.webglInitialized = false;
    this.pageHidden = false;
    
    // 初始化内存监控
    this._monitorMemoryUsage();
    
    // 获取选中的品种信息
    const app = getApp();
    const selectedBreed = app.globalData.selectedBreed;
    
    if (selectedBreed) {
      this.pageInstance.setData({
        breedName: selectedBreed.name,
        breedDetails: selectedBreed.details
      });
      
      // 根据犬种设置视力清晰度因子
      this.pageInstance.setBreedVisualAcuity(selectedBreed.name);
      
      // 更新视觉标签页数据
      uiController.updateVisionTabsData(selectedBreed, this.pageInstance);
    }
    
    // 更新当前页面主题
    this.pageInstance.updateTheme();
    
    // 注册主题变化回调
    this.pageInstance.registerThemeCallback();
    
    // 为了避免和页面卸载时机冲突，使用setTimeout
    setTimeout(() => {
      if (this.isPageLoaded) {
        // 注册应用显示/隐藏事件监听
        wx.onAppShow(this._handleAppShow.bind(this));
        wx.onAppHide(this._handleAppHide.bind(this));
      }
    }, 500);
    
    // 性能优化：根据设备性能调整参数
    const result = await utils.adjustPerformanceSettings();
    const { devicePerformance, skipFrames, deviceInfo } = result;
    
    console.log('设备性能级别:', devicePerformance, '设备信息:', deviceInfo);
    
    // 设置设备性能级别
    this.pageInstance.setData({ devicePerformance });
    
    // 设置视觉处理模块的设备性能级别
    visionProcessor.setDevicePerformance(devicePerformance);
    
    // 初始化WebGL - 相机初始化在WebGL初始化成功后进行
    await this.pageInstance.initWebGL();
    
    // 启动FPS计数器
    this.startFPSCounter();
    
    // 初始化CAM管理器
    this.pageInstance.initCamManager();
    
    // 初始化相机类型状态，确保界面显示与实际状态一致
    this.pageInstance.initializeCameraType();
    
    console.log('页面初始化完成');
  }

  /**
   * 页面就绪处理
   */
  onReady() {
    console.log('页面就绪，准备完成');
    
    // 确保在页面完全加载后再次应用犬种特定的视力清晰度因子
    if (this.pageInstance.data.breedName) {
      console.log('onReady: 再次应用犬种特定的视力清晰度因子');
      this.pageInstance.setBreedVisualAcuity(this.pageInstance.data.breedName);
      
      // 如果视觉管理器已初始化，强制重新渲染
      if (this.pageInstance.visionContext && this.pageInstance._lastFrame) {
        this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
      }
    }
    
    // 添加额外的安全机制，确保相机初始化
    setTimeout(() => {
      if (!this.cameraInitialized && !this.pageInstance.data.cameraLoading && !this.pageHidden) {
        console.log('安全检查：相机未初始化，尝试手动初始化');
        this.pageInstance.initCamera();
      }
    }, 2000);
    
    console.log('视觉模拟页面准备就绪');
  }

  /**
   * 页面显示处理
   */
  onShow() {
    // 重新注册主题回调
    this.pageInstance.registerThemeCallback();
    
    // 更新主题
    this.pageInstance.updateTheme();
    
    // 如果页面之前被隐藏，重新检查相机状态
    if (this.pageHidden) {
      this.pageHidden = false;
      
      // 检查相机状态
      if (this.pageInstance.data.cameraError || !this.cameraInitialized) {
        setTimeout(() => {
          if (!this.pageHidden) {
            this.pageInstance.retryCameraInit();
          }
        }, 1000);
      }
    }
  }

  /**
   * 页面隐藏处理
   */
  onHide() {
    console.log('视觉模拟页面隐藏');

    // 清除相机加载计时器
    if (this.cameraLoadingTimer) {
      clearTimeout(this.cameraLoadingTimer);
      this.cameraLoadingTimer = null;
      console.log('清除相机加载计时器');
    }

    // 标记页面已隐藏
    this.pageHidden = true;

    // 页面隐藏时执行内存清理
    this._forceMemoryCleanup();
  }

  /**
   * 页面卸载处理
   */
  onUnload() {
    console.log('视觉模拟页面卸载');

    // 重置授权重试计数器
    this.authRetryCount = 0;

    // 移除页面显示/隐藏事件监听
    wx.offAppShow(this._handleAppShow);
    wx.offAppHide(this._handleAppHide);

    // 移除主题变化回调
    this.pageInstance.unregisterThemeCallback();

    // 停止FPS计算
    if (this.fpsUpdateInterval) {
      clearInterval(this.fpsUpdateInterval);
      this.fpsUpdateInterval = null;
      console.log('清除FPS计数器');
    }

    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
      console.log('清除重试定时器');
    }

    // 清除相机加载计时器
    if (this.cameraLoadingTimer) {
      clearTimeout(this.cameraLoadingTimer);
      this.cameraLoadingTimer = null;
      console.log('清除相机加载计时器');
    }

    // 清除所有安全计时器
    if (this.safetyTimers && this.safetyTimers.length > 0) {
      this.safetyTimers.forEach(timer => {
        clearTimeout(timer);
      });
      this.safetyTimers = [];
      console.log('清除所有安全计时器');
    }

    // 释放WebGL资源
    if (this.pageInstance.webglContext) {
      try {
        console.log('释放WebGL资源');
        webglRenderer.releaseWebGLResources(this.pageInstance.webglContext);
        this.pageInstance.webglContext = null;
      } catch (err) {
        console.error('释放WebGL资源失败:', err);
      }
    }

    // 重置状态标记
    this.isPageLoaded = false;
    this.cameraInitialized = false;
    this.webglInitialized = false;
    this.pageHidden = true;

    // 释放CAM资源
    try {
      camManager.releaseCamResources();
      console.log('CAM资源已释放');
    } catch (err) {
      console.error('释放CAM资源失败:', err);
    }

    // 重置CAM错误计数
    this.camErrorCount = 0;

    console.log('生命周期管理器清理完成');
  }

  /**
   * 处理小程序显示事件
   */
  _handleAppShow() {
    console.log('小程序返回前台显示');
    this.pageHidden = false;

    // 如果页面已加载且WebGL已初始化，检查相机状态
    if (this.isPageLoaded && this.webglInitialized) {
      // 如果相机处于错误状态或未初始化，尝试重新初始化
      if (this.pageInstance.data.cameraError || !this.cameraInitialized) {
        console.log('检测到相机错误或未初始化，尝试重新初始化');
        setTimeout(() => {
          if (!this.pageHidden) {
            this.pageInstance.retryCameraInit();
          }
        }, 1000);
      }
      // 处理相机加载状态卡住的情况
      else if (this.pageInstance.data.cameraLoading) {
        console.log('检测到相机处于加载状态，可能是息屏后没有正确恢复，尝试重新初始化');
        setTimeout(() => {
          if (!this.pageHidden && this.pageInstance.data.cameraLoading) {
            this.pageInstance.retryCameraInit();
          }
        }, 1500);
      }
      // 即使相机状态正常，也做一次帧检查
      else {
        setTimeout(() => {
          if (!this.pageHidden && this.pageInstance._lastFrameCheckTime) {
            const now = Date.now();
            if (now - this.pageInstance._lastFrameCheckTime > 3000) {
              console.log('检测到相机可能已停止工作（长时间未收到新帧），尝试重新初始化');
              this.pageInstance.retryCameraInit();
            }
          }
        }, 3000);
      }
    }
  }

  /**
   * 处理小程序隐藏事件
   */
  _handleAppHide() {
    console.log('小程序进入后台');
    this.pageHidden = true;
  }

  /**
   * 启动FPS计数器
   */
  startFPSCounter() {
    let lastFrameTime = Date.now();
    let frameCount = 0;

    // 每秒更新一次FPS
    this.fpsUpdateInterval = setInterval(() => {
      const now = Date.now();
      const elapsed = now - lastFrameTime;

      if (elapsed > 0) {
        const fps = Math.round((frameCount * 1000) / elapsed);
        this.pageInstance.setData({ currentFPS: fps });

        // 重置计数器
        lastFrameTime = now;
        frameCount = 0;
      }
    }, 1000);
  }

  /**
   * 强制内存清理
   */
  _forceMemoryCleanup() {
    try {
      console.log('执行强制内存清理');

      // 清理CAM图片缓存
      this._forceCamImageCleanup();

      // 清理WebGL相关资源
      if (this.pageInstance.webglContext && this.pageInstance.webglContext.gl) {
        const gl = this.pageInstance.webglContext.gl;

        // 清理纹理缓存
        if (this.pageInstance.webglContext.textures) {
          this.pageInstance.webglContext.textures.forEach(texture => {
            if (texture) {
              gl.deleteTexture(texture);
            }
          });
          this.pageInstance.webglContext.textures = [];
        }

        // 强制垃圾回收
        gl.flush();
        gl.finish();
      }

      // 清理帧数据缓存
      if (this.pageInstance._lastFrame) {
        this.pageInstance._lastFrame = null;
      }

      // 触发系统垃圾回收（如果支持）
      if (typeof wx.triggerGC === 'function') {
        wx.triggerGC();
      }

      console.log('内存清理完成');
    } catch (error) {
      console.error('内存清理失败:', error);
    }
  }

  /**
   * 强制清理CAM图片缓存
   */
  _forceCamImageCleanup() {
    try {
      console.log('清理CAM图片缓存');

      // 清理CAM相关的图片URL
      if (this.pageInstance.data.camImageUrl) {
        this.pageInstance.setData({
          camImageUrl: '',
          camNextImageUrl: ''
        });
      }

      // 清理可能的图片缓存
      const imageUrls = [
        this.pageInstance.data.camImageUrl,
        this.pageInstance.data.camNextImageUrl
      ].filter(url => url && url.startsWith('blob:'));

      imageUrls.forEach(url => {
        try {
          if (typeof URL !== 'undefined' && URL.revokeObjectURL) {
            URL.revokeObjectURL(url);
          }
        } catch (err) {
          console.warn('清理blob URL失败:', err);
        }
      });

      console.log('CAM图片缓存清理完成');
    } catch (error) {
      console.error('CAM图片缓存清理失败:', error);
    }
  }

  /**
   * 监控内存使用情况
   */
  _monitorMemoryUsage() {
    try {
      if (typeof wx.getSystemInfo === 'function') {
        wx.getSystemInfo({
          success: (res) => {
            console.log('系统信息:', {
              platform: res.platform,
              system: res.system,
              version: res.version,
              SDKVersion: res.SDKVersion
            });

            // 根据系统信息调整内存管理策略
            if (res.platform === 'android') {
              // Android设备可能需要更频繁的内存清理
              this._setupMemoryMonitoring(5000); // 5秒检查一次
            } else {
              // iOS设备内存管理相对较好
              this._setupMemoryMonitoring(10000); // 10秒检查一次
            }
          },
          fail: (err) => {
            console.warn('获取系统信息失败:', err);
            // 使用默认的内存监控策略
            this._setupMemoryMonitoring(8000);
          }
        });
      }
    } catch (error) {
      console.error('内存监控初始化失败:', error);
    }
  }

  /**
   * 设置内存监控
   */
  _setupMemoryMonitoring(interval) {
    // 定期检查内存使用情况
    const memoryCheckTimer = setInterval(() => {
      if (!this.isPageLoaded || this.pageHidden) {
        return;
      }

      // 检查是否需要清理内存
      if (this._shouldCleanMemory()) {
        this._forceMemoryCleanup();
      }
    }, interval);

    // 将定时器添加到安全定时器列表中，确保页面卸载时能被清理
    this.safetyTimers.push(memoryCheckTimer);
  }

  /**
   * 判断是否需要清理内存
   */
  _shouldCleanMemory() {
    // 简单的内存清理策略
    if (this.pageInstance.data.camMode) {
      const hasMultipleUrls = this.pageInstance.data.camImageUrl &&
                             this.pageInstance.data.camNextImageUrl;
      if (hasMultipleUrls) {
        return true;
      }
    }

    // 如果FPS过低，可能是内存不足导致的
    if (this.pageInstance.data.currentFPS < 10 && this.pageInstance.data.currentFPS > 0) {
      return true;
    }

    return false;
  }
}

module.exports = LifecycleManager;
