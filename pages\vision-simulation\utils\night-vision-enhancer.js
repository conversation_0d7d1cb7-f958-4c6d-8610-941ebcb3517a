/**
 * 夜视增强模块
 * 负责实现高级夜视增强功能，包括：
 * 1. 多种算法结合：对比度调整 + 噪点抑制 + 边缘锐化
 * 2. 自适应处理：根据环境光线强度动态调整参数
 * 3. 性能平衡：确保实时处理不影响帧率
 */

// 导入相关模块
const visionConfig = require('./vision-config');
const devicePerformance = require('./device-performance');

// 夜视增强配置
const NIGHT_VISION_ENHANCEMENT_CONFIG = {
  // 对比度调整参数
  CONTRAST: {
    ADAPTIVE_RANGE: [1.0, 2.5],  // 自适应对比度范围
    BASE_CONTRAST: 1.3,          // 基础对比度
    DARK_BOOST: 0.4              // 暗部增强系数
  },
  
  // 噪点抑制参数
  NOISE_REDUCTION: {
    BILATERAL_SIGMA_COLOR: 50.0,   // 颜色相似性阈值
    BILATERAL_SIGMA_SPACE: 5.0,    // 空间距离阈值
    GAUSSIAN_KERNEL_SIZE: 3,       // 高斯核大小
    ENABLED: true                  // 启用噪点抑制
  },
  
  // 边缘锐化参数
  EDGE_SHARPENING: {
    UNSHARP_AMOUNT: 0.8,          // 反锐化蒙版强度
    UNSHARP_RADIUS: 1.5,          // 反锐化蒙版半径
    EDGE_THRESHOLD: 0.1,          // 边缘检测阈值
    ENABLED: true                 // 启用边缘锐化
  },
  
  // 自适应处理参数
  ADAPTIVE: {
    LIGHT_LEVELS: {
      VERY_DARK: { threshold: 30, weight: 1.0 },
      DARK: { threshold: 60, weight: 0.8 },
      DIM: { threshold: 90, weight: 0.6 },
      NORMAL: { threshold: 120, weight: 0.4 }
    },
    SMOOTHING_FACTOR: 0.1,        // 参数平滑因子
    UPDATE_INTERVAL: 100          // 参数更新间隔(ms)
  },
  
  // 性能优化参数
  PERFORMANCE: {
    MAX_PROCESSING_TIME: 16,      // 最大处理时间(ms) 60fps
    QUALITY_LEVELS: {
      HIGH: { samples: 9, iterations: 2 },
      MEDIUM: { samples: 5, iterations: 1 },
      LOW: { samples: 3, iterations: 1 }
    }
  }
};

// 自适应参数状态
let adaptiveState = {
  currentBrightness: 0,
  targetContrast: NIGHT_VISION_ENHANCEMENT_CONFIG.CONTRAST.BASE_CONTRAST,
  targetNoiseReduction: 0.5,
  targetSharpening: 0.5,
  lastUpdateTime: 0,
  performanceLevel: 'MEDIUM'
};

/**
 * 根据环境光线计算自适应增强参数
 * @param {number} brightness - 当前图像亮度 (0-255)
 * @param {string} animalType - 动物类型 ('dog' 或 'cat')
 * @returns {Object} 增强参数对象
 */
function calculateAdaptiveParams(brightness, animalType = 'dog') {
  const config = NIGHT_VISION_ENHANCEMENT_CONFIG;
  const lightLevels = config.ADAPTIVE.LIGHT_LEVELS;
  
  // 确定当前光线等级
  let lightLevel = 'NORMAL';
  let adaptiveWeight = 0.4;
  
  if (brightness <= lightLevels.VERY_DARK.threshold) {
    lightLevel = 'VERY_DARK';
    adaptiveWeight = lightLevels.VERY_DARK.weight;
  } else if (brightness <= lightLevels.DARK.threshold) {
    lightLevel = 'DARK';
    adaptiveWeight = lightLevels.DARK.weight;
  } else if (brightness <= lightLevels.DIM.threshold) {
    lightLevel = 'DIM';
    adaptiveWeight = lightLevels.DIM.weight;
  }
  
  // 根据动物类型调整基础参数
  const baseConfig = animalType === 'cat' 
    ? visionConfig.NIGHT_VISION_CONFIG.CAT 
    : visionConfig.NIGHT_VISION_CONFIG.DOG;
  
  // 计算自适应对比度
  const contrastRange = config.CONTRAST.ADAPTIVE_RANGE;
  const targetContrast = contrastRange[0] + 
    (contrastRange[1] - contrastRange[0]) * adaptiveWeight;
  
  // 计算噪点抑制强度（暗环境下更强）
  const noiseReductionStrength = Math.min(1.0, adaptiveWeight * 1.2);
  
  // 计算边缘锐化强度（中等光线下最强）
  const sharpeningStrength = Math.min(1.0, 
    adaptiveWeight * (lightLevel === 'DIM' ? 1.2 : 0.8));
  
  // 平滑过渡参数，避免闪烁
  const smoothingFactor = config.ADAPTIVE.SMOOTHING_FACTOR;
  adaptiveState.targetContrast = lerp(
    adaptiveState.targetContrast, 
    targetContrast, 
    smoothingFactor
  );
  adaptiveState.targetNoiseReduction = lerp(
    adaptiveState.targetNoiseReduction, 
    noiseReductionStrength, 
    smoothingFactor
  );
  adaptiveState.targetSharpening = lerp(
    adaptiveState.targetSharpening, 
    sharpeningStrength, 
    smoothingFactor
  );
  
  return {
    // 基础夜视参数（保持不变）
    brightness: baseConfig.BRIGHTNESS,
    contrast: baseConfig.CONTRAST,
    
    // 增强参数
    adaptiveContrast: adaptiveState.targetContrast,
    noiseReduction: adaptiveState.targetNoiseReduction,
    edgeSharpening: adaptiveState.targetSharpening,
    lightLevel: lightLevel,
    adaptiveWeight: adaptiveWeight
  };
}

/**
 * 根据设备性能动态调整处理质量
 * @param {number} processingTime - 上一帧处理时间(ms)
 * @returns {Object} 性能配置
 */
function adjustPerformanceLevel(processingTime) {
  const config = NIGHT_VISION_ENHANCEMENT_CONFIG.PERFORMANCE;
  const maxTime = config.MAX_PROCESSING_TIME;
  
  let newLevel = adaptiveState.performanceLevel;
  
  // 根据处理时间调整性能等级
  if (processingTime > maxTime * 1.5) {
    newLevel = 'LOW';
  } else if (processingTime > maxTime * 1.2) {
    newLevel = 'MEDIUM';
  } else if (processingTime < maxTime * 0.8) {
    newLevel = 'HIGH';
  }
  
  // 平滑切换，避免频繁变化
  if (newLevel !== adaptiveState.performanceLevel) {
    const currentTime = Date.now();
    if (currentTime - adaptiveState.lastUpdateTime > 1000) { // 1秒内最多切换一次
      adaptiveState.performanceLevel = newLevel;
      adaptiveState.lastUpdateTime = currentTime;
    }
  }
  
  return config.QUALITY_LEVELS[adaptiveState.performanceLevel];
}

/**
 * 生成夜视增强的WebGL着色器代码
 * @param {Object} params - 增强参数
 * @returns {string} 着色器代码片段
 */
function generateEnhancementShader(params) {
  const performanceConfig = adjustPerformanceLevel(params.processingTime || 16);
  
  return `
    // 夜视增强函数
    vec4 applyNightVisionEnhancement(vec4 originalColor, vec2 uv) {
      vec4 enhancedColor = originalColor;
      
      // 1. 基础亮度和对比度调整（保持原有逻辑）
      enhancedColor.rgb *= uBrightness;
      enhancedColor.rgb = ((enhancedColor.rgb - 0.5) * uContrast) + 0.5;
      
      // 2. 自适应对比度增强
      float adaptiveContrast = ${params.adaptiveContrast.toFixed(2)};
      enhancedColor.rgb = ((enhancedColor.rgb - 0.5) * adaptiveContrast) + 0.5;
      
      // 3. 噪点抑制 - 双边滤波近似
      if (${params.noiseReduction} > 0.1) {
        vec4 filteredColor = vec4(0.0);
        float totalWeight = 0.0;
        float noiseStrength = ${params.noiseReduction.toFixed(2)};
        
        // 使用性能优化的采样点数量
        int samples = ${performanceConfig.samples};
        float sampleStep = 1.0 / float(samples);
        
        for (int i = 0; i < ${performanceConfig.samples}; i++) {
          for (int j = 0; j < ${performanceConfig.samples}; j++) {
            vec2 offset = vec2(float(i), float(j)) * sampleStep * 2.0 - 1.0;
            offset *= noiseStrength * 0.003; // 采样半径
            
            vec4 sampleColor = texture2D(uSampler, uv + offset);
            
            // 颜色相似性权重
            float colorDiff = length(sampleColor.rgb - originalColor.rgb);
            float colorWeight = exp(-colorDiff * colorDiff * 20.0);
            
            // 空间距离权重
            float spatialWeight = exp(-dot(offset, offset) * 100.0);
            
            float weight = colorWeight * spatialWeight;
            filteredColor += sampleColor * weight;
            totalWeight += weight;
          }
        }
        
        if (totalWeight > 0.0) {
          enhancedColor = mix(enhancedColor, filteredColor / totalWeight, noiseStrength);
        }
      }
      
      // 4. 边缘锐化 - 反锐化蒙版
      if (${params.edgeSharpening} > 0.1) {
        float sharpenStrength = ${params.edgeSharpening.toFixed(2)};
        
        // 计算拉普拉斯边缘检测
        vec4 center = enhancedColor;
        vec4 blur = vec4(0.0);
        
        // 简化的高斯模糊
        float weights[9];
        weights[0] = 0.0625; weights[1] = 0.125; weights[2] = 0.0625;
        weights[3] = 0.125;  weights[4] = 0.25;  weights[5] = 0.125;
        weights[6] = 0.0625; weights[7] = 0.125; weights[8] = 0.0625;
        
        vec2 offsets[9];
        offsets[0] = vec2(-1.0, -1.0); offsets[1] = vec2(0.0, -1.0); offsets[2] = vec2(1.0, -1.0);
        offsets[3] = vec2(-1.0,  0.0); offsets[4] = vec2(0.0,  0.0); offsets[5] = vec2(1.0,  0.0);
        offsets[6] = vec2(-1.0,  1.0); offsets[7] = vec2(0.0,  1.0); offsets[8] = vec2(1.0,  1.0);
        
        for (int i = 0; i < 9; i++) {
          vec2 sampleUV = uv + offsets[i] / uTextureSize * 1.5;
          blur += texture2D(uSampler, sampleUV) * weights[i];
        }
        
        // 应用反锐化蒙版
        vec4 sharpened = center + (center - blur) * sharpenStrength;
        enhancedColor = mix(enhancedColor, sharpened, sharpenStrength);
      }
      
      // 确保颜色值在有效范围内
      enhancedColor = clamp(enhancedColor, 0.0, 1.0);
      
      return enhancedColor;
    }
  `;
}

/**
 * 线性插值函数
 */
function lerp(a, b, factor) {
  return a + (b - a) * factor;
}

/**
 * 获取增强配置
 */
function getEnhancementConfig() {
  return { ...NIGHT_VISION_ENHANCEMENT_CONFIG };
}

/**
 * 重置自适应状态
 */
function resetAdaptiveState() {
  adaptiveState = {
    currentBrightness: 0,
    targetContrast: NIGHT_VISION_ENHANCEMENT_CONFIG.CONTRAST.BASE_CONTRAST,
    targetNoiseReduction: 0.5,
    targetSharpening: 0.5,
    lastUpdateTime: 0,
    performanceLevel: 'MEDIUM'
  };
}

module.exports = {
  calculateAdaptiveParams,
  adjustPerformanceLevel,
  generateEnhancementShader,
  getEnhancementConfig,
  resetAdaptiveState,
  NIGHT_VISION_ENHANCEMENT_CONFIG
}; 