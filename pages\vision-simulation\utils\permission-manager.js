/**
 * 权限管理模块
 * 负责统一处理小程序所需的各种权限申请
 */

// 全局变量，用于跟踪权限检查状态
let _permissionCheckInProgress = {
  camera: false,
  album: false
};

let _lastPermissionCheckTime = {
  camera: 0,
  album: 0
};

// 添加权限缓存，避免重复申请
let _permissionCache = {
  camera: null, // null表示未知，true表示有权限，false表示无权限
  album: null
};

// 权限缓存过期时间（毫秒）
const PERMISSION_CACHE_EXPIRE = 60 * 60 * 1000; // 1小时

/**
 * 主动申请相机权限
 * @returns {Promise} 权限申请结果
 */
function requestCameraPermission() {
  return new Promise((resolve, reject) => {
    console.log('主动申请相机权限');
    
    // 检查权限缓存
    const now = Date.now();
    if (_permissionCache.camera === true && now - _lastPermissionCheckTime.camera < PERMISSION_CACHE_EXPIRE) {
      console.log('使用缓存的相机权限状态：已授权');
      resolve({code: 0, msg: '已有相机权限(缓存)'});
      return;
    }
    
    // 防止频繁检查
    if (_permissionCheckInProgress.camera) {
      console.log('相机权限检查正在进行中，跳过重复请求');
      reject({
        errCode: 'auth_check_in_progress',
        errMsg: '权限检查正在进行中'
      });
      return;
    }
    
    // 防止短时间内重复检查
    if (now - _lastPermissionCheckTime.camera < 1000 && _lastPermissionCheckTime.camera > 0) {
      console.log('相机权限检查请求过于频繁，跳过');
      reject({
        errCode: 'auth_check_too_frequent',
        errMsg: '权限检查请求过于频繁'
      });
      return;
    }
    
    // 设置检查状态
    _permissionCheckInProgress.camera = true;
    _lastPermissionCheckTime.camera = now;
    
    // 先检查是否已有权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          // 已有相机权限
          console.log('已有相机权限');
          _permissionCheckInProgress.camera = false;
          
          // 更新权限缓存
          _permissionCache.camera = true;
          _lastPermissionCheckTime.camera = now;
          
          resolve({code: 0, msg: '已有相机权限'});
        } else {
          // 没有相机权限，申请权限
          console.log('没有相机权限，申请权限');
          wx.authorize({
            scope: 'scope.camera',
            success: () => {
              console.log('相机权限申请成功');
              _permissionCheckInProgress.camera = false;
              
              // 更新权限缓存
              _permissionCache.camera = true;
              _lastPermissionCheckTime.camera = now;
              
              resolve({code: 0, msg: '相机权限申请成功'});
            },
            fail: (err) => {
              console.log('相机权限申请失败', err);
              _permissionCheckInProgress.camera = false;
              
              // 更新权限缓存
              _permissionCache.camera = false;
              _lastPermissionCheckTime.camera = now;
              
              reject({
                errCode: 'auth_rejected',
                errMsg: '相机权限申请失败'
              });
            }
          });
        }
      },
      fail: (err) => {
        console.error('获取设置失败:', err);
        _permissionCheckInProgress.camera = false;
        reject({
          errCode: 'get_setting_failed',
          errMsg: '获取设置失败',
          detail: err
        });
      }
    });
  });
}

/**
 * 主动申请保存图片到相册的权限
 * @returns {Promise} 权限申请结果
 */
function requestAlbumPermission() {
  return new Promise((resolve, reject) => {
    console.log('主动申请保存图片到相册权限');
    
    // 防止频繁检查
    const now = Date.now();
    if (_permissionCheckInProgress.album) {
      console.log('相册权限检查正在进行中，跳过重复请求');
      reject({
        errCode: 'auth_check_in_progress',
        errMsg: '权限检查正在进行中'
      });
      return;
    }
    
    // 防止短时间内重复检查
    if (now - _lastPermissionCheckTime.album < 1000 && _lastPermissionCheckTime.album > 0) {
      console.log('相册权限检查请求过于频繁，跳过');
      reject({
        errCode: 'auth_check_too_frequent',
        errMsg: '权限检查请求过于频繁'
      });
      return;
    }
    
    // 设置检查状态
    _permissionCheckInProgress.album = true;
    _lastPermissionCheckTime.album = now;
    
    // 先检查是否已有权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === true) {
          // 用户已授权
          console.log('用户已授权相册权限');
          _permissionCheckInProgress.album = false;
          resolve(true);
        } else {
          // 使用 wx.authorize 主动申请权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              console.log('相册权限申请成功');
              _permissionCheckInProgress.album = false;
              resolve(true);
            },
            fail: (err) => {
              console.error('相册权限申请失败:', err);
              _permissionCheckInProgress.album = false;
              
              // 如果是用户拒绝，则引导用户打开设置
              if (err.errMsg && err.errMsg.indexOf('auth deny') > -1) {
                reject({
                  errCode: 'auth_denied',
                  errMsg: '相册权限被拒绝',
                  detail: err
                });
              } else {
                reject({
                  errCode: 'auth_failed',
                  errMsg: '相册权限申请失败',
                  detail: err
                });
              }
            }
          });
        }
      },
      fail: (err) => {
        console.error('获取设置失败:', err);
        _permissionCheckInProgress.album = false;
        reject({
          errCode: 'get_setting_failed',
          errMsg: '获取设置失败',
          detail: err
        });
      }
    });
  });
}

/**
 * 打开设置页面
 * @param {string} permissionType 权限类型，'camera'或'album'
 * @returns {Promise} 打开设置页面的结果
 */
function openPermissionSetting(permissionType) {
  return new Promise((resolve, reject) => {
    wx.openSetting({
      success: (res) => {
        if (permissionType === 'camera' && res.authSetting['scope.camera']) {
          resolve(true); // 用户已授权相机
        } else if (permissionType === 'album' && res.authSetting['scope.writePhotosAlbum']) {
          resolve(true); // 用户已授权相册
        } else {
          resolve(false); // 用户未授权
        }
      },
      fail: (err) => {
        console.error('打开设置页面失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 显示权限申请对话框
 * @param {string} permissionType 权限类型，'camera'或'album'
 * @param {Object} page 页面实例
 * @returns {Promise} 用户操作结果
 */
function showPermissionDialog(permissionType, page) {
  return new Promise((resolve, reject) => {
    let title, content, confirmText;
    
    if (permissionType === 'camera') {
      title = '相机权限申请';
      content = '需要使用您的相机来模拟宠物视觉效果，是否前往设置开启权限？';
      confirmText = '前往设置';
    } else if (permissionType === 'album') {
      title = '相册权限申请';
      content = '需要保存图片到您的相册，是否前往设置开启权限？';
      confirmText = '前往设置';
    } else {
      reject(new Error('未知的权限类型'));
      return;
    }
    
    wx.showModal({
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户确认前往设置
          openPermissionSetting(permissionType)
            .then((granted) => {
              if (granted) {
                // 用户已开启权限
                resolve(true);
              } else {
                // 用户未开启权限
                reject({
                  errCode: 'auth_denied',
                  errMsg: permissionType === 'camera' ? '相机权限被拒绝' : '相册权限被拒绝'
                });
              }
            })
            .catch((err) => {
              console.error('打开设置页面失败:', err);
              reject(err);
            });
        } else {
          // 用户取消
          reject({
            errCode: 'auth_canceled',
            errMsg: '用户取消授权'
          });
        }
      }
    });
  });
}

module.exports = {
  requestCameraPermission,
  requestAlbumPermission,
  openPermissionSetting,
  showPermissionDialog
};
