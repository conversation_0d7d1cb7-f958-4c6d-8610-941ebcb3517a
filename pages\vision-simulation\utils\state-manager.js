/**
 * 数据状态管理器
 * 负责管理页面的复杂数据状态和配置
 */

class StateManager {
  constructor() {
    this.pageInstance = null;
    this.initialData = null;
  }

  /**
   * 初始化状态管理器
   * @param {Object} pageInstance - 页面实例
   */
  init(pageInstance) {
    this.pageInstance = pageInstance;
    this.initialData = this.getInitialData();
  }

  /**
   * 获取初始数据配置
   * @returns {Object} 初始数据对象
   */
  getInitialData() {
    return {
      breedName: "",
      breedDetails: {},
      currentView: 'dog',
      cameraPosition: 'back',
      averageBrightness: 0,
      isLowLight: false,
      isBrightLight: false,
      brightPixelRatio: 0,
      brightPixelRatioFormatted: '0.0',
      LOW_LIGHT_THRESHOLD: 70, // 低光阈值，亮度值小于70时启用夜视
      HIGH_LIGHT_THRESHOLD: 210, // 强光阈值，亮度值大于180时启用缩璞模式
      devicePerformance: 'medium', // 默认中等性能
      
      // 光照模式提示相关
      showBrightLightTip: false,
      showNightVisionTip: false,
      showMotionTip: false, // 运动视觉提示
      
      // 性能相关
      currentFPS: 0,
      frame: { width: 0, height: 0 },
      cameraLoading: true,
      cameraError: false,
      cameraErrorMsg: '',
      
      // 相机布局相关
      isAnalysisExpanded: false, // 分析区域是否展开
      showControls: true, // 控制面板是否显示，默认显示
      
      // 相机选择器相关
      showCameraSelector: false, // 是否显示相机选择器
      currentCameraType: 'back', // 当前相机类型: 'back', 'front', 'cam'
      cameraOptions: [
        { 
          type: 'back', 
          name: '后置相机', 
          icon: '📷', 
          desc: '手机后置摄像头',
          available: true 
        },
        { 
          type: 'front', 
          name: '前置相机', 
          icon: '🤳', 
          desc: '手机前置摄像头',
          available: true 
        },
        { 
          type: 'cam', 
          name: '硬件相机', 
          icon: '📡', 
          desc: 'CAM硬件摄像设备',
          available: true 
        }
      ],
      
      // 分辨率设置
      resolutionOptions: [
        { name: '高清(1920x1080)', value: 'high', frameSize: 'large', width: 1920, height: 1080 },
        { name: '标清(1280x720)', value: 'medium', frameSize: 'medium', width: 1280, height: 720 },
        { name: '流畅(640x480)', value: 'low', frameSize: 'small', width: 640, height: 480 }
      ],
      currentResolutionIndex: 1, // 默认使用标清分辨率
      showResolutionSelector: false, // 控制分辨率选择器显示
      
      // 视觉模式选择器
      showVisionModeSelector: false, // 控制视觉模式选择器显示
      currentVisionMode: 'nightVision', // 默认夜视能力模式，包含二色视觉和视力辨析度
      showVisionModeDetail: true, // 控制视觉模式详情显示
      
      // 功能开关
      features: {
        motion: false,
        night: true,  // 默认启用夜视功能
        color: true,  // 默认启用二色视觉
        isCat: false  // 默认为犬科动物
      },
      
      // 犬科视觉特性参数
      dogVisionParams: {
        // 增强夜视能力参数
        brightness: 1.5,       // 亮度增强 - 提高夜视能力 (默认参数，哈士奇会单独设置为1.8)
        contrast: 1.3,         // 对比度 - 提高夜视能力
        // 辨析度参数
        resolutionFactor: 0.5, // 提高辨析度
        antiAliasFactor: 0.3,   // 视野因子 - 控制视野范围内外的清晰度差异
        // 运动视觉参数
        motionSensitivity: 3.0,  // 运动敏感度 - 控制运动检测的敏感程度
        motionThreshold: 10.0,   // 运动阈值 - 仅超过阈值的运动才会被检测到
        motionSizeThreshold: 10.0 // 运动物体大小阈值 - 控制可被检测到的运动物体大小
      },
      
      // 标签页控制
      currentTab: 0,
      tabs: ['视觉特征', '感知能力', '爱宠养护'],
      
      // 按标签页分类的视觉特点
      visionTabs: {
        basicFeatures: {
          eyeColor: '琥珀色',
          eyeShape: '圆形',
          colorSystem: '双色视觉',
          visualAngle: '水平240°'
        },
        coreFeatures: {
          visualAcuity: '人类的1/4-1/5',
          nightVision: '人类的3-5倍',
          motionPerception: '优于人类2倍'
        },
        otherFeatures: {
          tearStains: '轻微'
        }
      },
      
      showVisionParams: false,  // 控制视觉参数面板显示
      
      // 保存原始参数值，用于重置
      originalDogVisionParams: {
        resolutionFactor: 0.5,
        antiAliasFactor: 0.3, // 视野因子的默认值为0.3
        brightness: 1.5,
        contrast: 1.3,
        motionSensitivity: 3.0, // 犬科运动敏感度默认值
        motionThreshold: 10.0, // 犬科运动阈值默认值
        motionSizeThreshold: 10.0 // 犬科物体大小阈值默认值
      },
      
      // 视觉模式详情
      visionModeDetails: {
        dichromatic: {
          title: "二色视觉（基础层）",
          desc: "模拟犬科动物的二色视觉系统，主要感知蓝色和黄色光谱，对红绿色调区分能力较弱。犬科动物无法区分红色与绿色，但在蓝黄色调上有较好辨识能力。",
          includes: "仅二色视觉"
        },
        acuity: {
          title: "视力辨析（第2层）",
          desc: "模拟犬科动物视力辨析能力，约为人类的1/4-1/5，远处物体变得模糊。犬科动物近距离视力较好，但无法看清远处细节，无法阅读文字，但对移动物体敏感。",
          includes: "二色视觉 + 视力辨析"
        },
        nightVision: {
          title: "光感模式（第3层）",
          desc: "模拟犬科动物在低光环境中的出色视觉，包括亮度增强和对比度调整。犬科动物具有出色的夜间视觉能力，视网膜上的感光细胞比人类多2-3倍，能在几乎全黑环境中辨识物体。",
          includes: "二色视觉 + 视力辨析 + 光感模式"
        },
        motionVision: {
          title: "运动视觉（全功能）",
          desc: "模拟犬科动物对运动目标的敏锐感知能力，能迅速捕捉微小的动态变化。犬科动物视网膜上的感光细胞对移动物体极为敏感，即使在低光和远距离条件下，也能迅速察觉到细微的运动目标。",
          includes: "二色视觉 + 视力辨析 + 光感模式 + 运动视觉"
        }
      },
      
      showVisionGuide: false,
      rememberVisionGuide: false,
      
      // CAM硬件相机相关
      camMode: false,                    // 是否启用CAM模式
      camStatus: 'disconnected',         // CAM连接状态: disconnected, connecting, connected, error
      camImageUrl: '',                   // CAM图片流URL
      camNextImageUrl: '',               // CAM下一帧URL
      camCurrentBuffer: 'buffer1',       // CAM当前显示缓冲区
      camIsLoading: false,               // CAM是否正在加载
      camErrorMsg: '',                   // CAM错误信息
    };
  }

  /**
   * 更新主题相关状态
   * @param {string} currentTheme - 当前主题
   * @param {Object} themeConfig - 主题配置
   */
  updateThemeState(currentTheme, themeConfig) {
    // 检查是否为浅色系主题
    const isLightTheme = currentTheme === 'theme6' || currentTheme === 'theme7' || currentTheme === 'theme8';
    
    this.pageInstance.setData({
      currentTheme: currentTheme,
      isLightTheme: isLightTheme,
      themeStyle: {
        background: themeConfig.gradient || 'linear-gradient(145deg, #3D6B96, #5284B4)',
        backgroundDark: themeConfig.darkBg || '#3D6B96',
        cardBg: themeConfig.cardBg || 'rgba(70, 115, 160, 0.85)',
        textColor: themeConfig.textColor || '#ffffff'
      }
    });
  }

  /**
   * 重置视觉参数到默认值
   * @param {string} breedName - 品种名称
   */
  resetVisionParams(breedName) {
    // 获取当前品种名称
    if (!breedName) {
      breedName = this.pageInstance.data.breedName;
    }
    
    console.log('重置视觉参数，品种:', breedName);
    
    // 根据品种重新设置默认参数
    let defaultParams = { ...this.initialData.originalDogVisionParams };
    
    // 根据品种调整默认参数
    if (this.pageInstance.data.features.isCat) {
      // 猫科动物参数
      if (breedName === '暹罗') {
        defaultParams.resolutionFactor = 0.15;
        defaultParams.brightness = 1.5;
        defaultParams.contrast = 1.3;
      } else {
        defaultParams.resolutionFactor = 0.2;
        defaultParams.brightness = 2.0;
        defaultParams.contrast = 1.5;
      }
    } else {
      // 犬科动物参数
      if (breedName && breedName.includes('拉布拉多')) {
        defaultParams.resolutionFactor = 1.0;
      } else if (breedName && breedName.includes('金毛')) {
        defaultParams.resolutionFactor = 0.33;
      } else if (breedName && (breedName.includes('边境') || breedName.includes('牧羊'))) {
        defaultParams.resolutionFactor = 0.4;
      } else if (breedName && (breedName.includes('贵宾') || breedName.includes('柯基') || 
                breedName.includes('比熊') || breedName.includes('哈士奇'))) {
        defaultParams.resolutionFactor = 0.27;
        if (breedName.includes('哈士奇')) {
          defaultParams.brightness = 2.0;
          defaultParams.contrast = 1.5;
        }
      } else if (breedName && (breedName.includes('吉娃娃') || breedName.includes('法斗') || 
                breedName.includes('法国'))) {
        defaultParams.resolutionFactor = 0.25;
      }
    }
    
    // 更新UI显示的参数和原始参数
    this.pageInstance.setData({
      dogVisionParams: { ...defaultParams },
      originalDogVisionParams: { ...defaultParams }
    });
    
    console.log('视觉参数已重置为默认值:', defaultParams);
  }
}

module.exports = StateManager;
