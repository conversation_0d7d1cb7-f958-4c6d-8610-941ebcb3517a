/**
 * 提示管理器模块
 * 负责管理各种提示的显示，避免重复显示
 */

// 冷却时间记录
const cooldowns = {
  nightVision: false,  // 夜视模式提示冷却状态
  brightLight: false,  // 强光模式提示冷却状态
  motionVision: false  // 运动视觉提示冷却状态
};

// 冷却时间(毫秒)
const COOLDOWN_DURATION = 5000;  // 5秒冷却期

/**
 * 显示夜视模式提示
 * @param {Object} page - 页面实例
 * @returns {Boolean} - 是否成功显示提示(true:已显示, false:处于冷却期)
 */
function showNightVisionTip(page) {
  // 如果在冷却期内，不重复显示
  if (cooldowns.nightVision) {
    return false;
  }
  
  // 设置冷却状态
  cooldowns.nightVision = true;
  
  // 注意: UI元素已移除，不再设置Data
  // page.setData({
  //   showNightVisionTip: true
  // });
  
  // 设置冷却期
  setTimeout(() => {
    cooldowns.nightVision = false;
  }, COOLDOWN_DURATION);
  
  return true;
}

/**
 * 显示强光适应提示
 * @param {Object} page - 页面实例
 * @returns {Boolean} - 是否成功显示提示
 */
function showBrightLightTip(page) {
  // 如果在冷却期内，不重复显示
  if (cooldowns.brightLight) {
    return false;
  }
  
  // 设置冷却状态
  cooldowns.brightLight = true;
  
  // 注意: UI元素已移除，不再设置Data
  // page.setData({
  //   showBrightLightTip: true
  // });
  
  // 设置冷却期
  setTimeout(() => {
    cooldowns.brightLight = false;
  }, COOLDOWN_DURATION);
  
  return true;
}

/**
 * 显示运动视觉提示
 * @param {Object} page - 页面实例
 * @returns {Boolean} - 是否成功显示提示
 */
function showMotionVisionTip(page) {
  // 如果在冷却期内，不重复显示
  if (cooldowns.motionVision) {
    return false;
  }
  
  // 设置冷却状态
  cooldowns.motionVision = true;
  
  // 注意: UI元素已移除，不再设置Data
  // page.setData({
  //   showMotionTip: true
  // });
  
  // 设置冷却期
  setTimeout(() => {
    cooldowns.motionVision = false;
  }, COOLDOWN_DURATION);
  
  return true;
}

/**
 * 关闭运动视觉提示
 * @param {Object} page - 页面实例
 */
function closeMotionVisionTip(page) {
  // 注意: UI元素已移除，不再设置Data
  // page.setData({
  //   showMotionTip: false
  // });
}

/**
 * 关闭夜视模式提示
 * @param {Object} page - 页面实例
 */
function closeNightVisionTip(page) {
  // 注意: UI元素已移除，不再设置Data
  // page.setData({
  //   showNightVisionTip: false
  // });
}

/**
 * 关闭强光适应提示
 * @param {Object} page - 页面实例
 */
function closeBrightLightTip(page) {
  // 注意: UI元素已移除，不再设置Data
  // page.setData({
  //   showBrightLightTip: false
  // });
}

module.exports = {
  showNightVisionTip,
  closeNightVisionTip,
  showBrightLightTip,
  closeBrightLightTip,
  showMotionVisionTip,
  closeMotionVisionTip
}; 