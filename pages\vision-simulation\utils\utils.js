/**
 * 工具函数模块
 * 存放通用工具函数
 */

// 性能优化：根据设备性能调整参数
function adjustPerformanceSettings() {
  return new Promise((resolve) => {
    wx.getSystemInfo({
      success: (res) => {
        // 获取设备信息
        const { platform, brand, model, benchmarkLevel } = res;
        console.log('设备信息:', platform, brand, model, benchmarkLevel);
        
        // 根据基准性能等级判断设备性能
        // benchmarkLevel: 设备性能等级, -2 及以下 极低, -1 低, 0 中等, 1 高, 2 及以上 极高
        let devicePerformance = 'medium'; // 默认中等性能
        let skipFrames = 1; // 默认跳过1帧
        
        if (benchmarkLevel >= 1) {
          devicePerformance = 'high';
          skipFrames = 0; // 高性能设备不跳帧
        } else if (benchmarkLevel <= -1) {
          devicePerformance = 'low';
          skipFrames = 2; // 低性能设备跳过2帧
        }
        
        // 特定设备优化
        if (brand === 'HUAWEI' && model.includes('P30')) {
          devicePerformance = 'high'; // 华为P30系列强制设为高性能
          skipFrames = 0;
        } else if (brand === 'HONOR' && model.includes('8X')) {
          devicePerformance = 'low'; // 荣耀8X强制设为低性能
          skipFrames = 2;
        }
        
        resolve({
          devicePerformance,
          skipFrames,
          deviceInfo: {
            platform,
            brand,
            model,
            benchmarkLevel
          }
        });
      },
      fail: () => {
        // 获取失败时使用默认值
        resolve({
          devicePerformance: 'medium',
          skipFrames: 1,
          deviceInfo: null
        });
      }
    });
  });
}

// 性能优化：预分配内存缓冲区
function initMemoryBuffers(width, height) {
  try {
    // 创建帧数据缓冲区
    const frameSize = width * height * 4; // RGBA每像素4字节
    const frameBuffer = new ArrayBuffer(frameSize);
    const frameData = new Uint8Array(frameBuffer);
    
    // 创建处理结果缓冲区
    const resultBuffer = new ArrayBuffer(frameSize);
    const resultData = new Uint8Array(resultBuffer);
    
    return {
      frameBuffer,
      frameData,
      resultBuffer,
      resultData
    };
  } catch (error) {
    console.error('初始化内存缓冲区错误:', error);
    return null;
  }
}

// 保存Canvas到相册
function saveCanvasToAlbum(canvasId) {
  return new Promise((resolve, reject) => {
    // 获取Canvas对象
    const query = wx.createSelectorQuery();
    query.select(canvasId)
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0] || !res[0].node) {
          console.error('获取Canvas节点失败', res);
          reject(new Error('获取Canvas节点失败'));
          return;
        }
        
        const canvas = res[0].node;
        const width = res[0].width;
        const height = res[0].height;
        
        console.log('获取到Canvas节点', { width, height });
        
        // 确保Canvas尺寸正确
        if (width === 0 || height === 0) {
          console.error('Canvas尺寸为0', { width, height });
          reject(new Error('Canvas尺寸异常'));
          return;
        }
        
        // 将Canvas内容保存为临时文件
        wx.canvasToTempFilePath({
          canvas,
          x: 0,
          y: 0,
          width: width,
          height: height,
          destWidth: width,
          destHeight: height,
          fileType: 'jpg',
          quality: 0.9,
          success: (res) => {
            const tempFilePath = res.tempFilePath;
            console.log('生成临时文件成功', tempFilePath);
            
            // 保存图片到相册
            wx.saveImageToPhotosAlbum({
              filePath: tempFilePath,
              success: () => {
                console.log('保存到相册成功');
                resolve();
              },
              fail: (error) => {
                console.error('保存到相册失败', error);
                if (error.errMsg && error.errMsg.includes('auth deny')) {
                  // 用户拒绝授权
                  reject(new Error('需要授权才能保存图片'));
                } else {
                  reject(new Error('保存图片失败: ' + error.errMsg));
                }
              }
            });
          },
          fail: (error) => {
            console.error('Canvas转换为图片失败', error);
            reject(new Error('Canvas转换为图片失败: ' + error.errMsg));
          }
        });
      });
  });
}

// 保存爱宠视角图片
function savePetVisionImage(page) {
  // 检查当前是否为爱宠视角
  if (page.data.currentView !== 'dog') {
    wx.showToast({
      title: '请切换到爱宠视角',
      icon: 'none',
      duration: 1500
    });
    return;
  }
  
  try {
    // 显示加载提示
    wx.showLoading({
      title: '正在保存...',
      mask: true
    });
    
    // 先检查相册权限
    checkPhotoAlbumAuth()
      .then(() => {
        // 权限获取成功，继续保存图片
        savePetVisionCanvasToAlbum(page);
      })
      .catch((error) => {
        // 权限获取失败
        console.error('相册权限获取失败', error);
        handleSaveError(error);
      });
  } catch (error) {
    console.error('保存过程发生异常', error);
    handleSaveError(error);
  }
}

// 导入权限管理模块
const permissionManager = require('./permission-manager');

// 检查相册权限
function checkPhotoAlbumAuth() {
  console.log('使用权限管理模块检查相册权限');
  return permissionManager.requestAlbumPermission();
}

// 保存宠物视觉画布到相册
function savePetVisionCanvasToAlbum(page) {
  // 获取画布实例
  const query = wx.createSelectorQuery();
  query.select('#petVisionCanvas')
    .fields({ node: true, size: true })
    .exec((res) => {
      if (!res[0] || !res[0].node) {
        console.error('获取画布失败');
        handleSaveError(new Error('获取画布失败'));
        return;
      }
      
      const canvas = res[0].node;
      const width = res[0].width;
      const height = res[0].height;
      
      wx.canvasToTempFilePath({
        canvas: canvas,
        x: 0,
        y: 0,
        width: width,
        height: height,
        destWidth: width,
        destHeight: height,
        fileType: 'jpg',
        quality: 0.9,
        success: (res) => {
          console.log('生成临时文件成功', res.tempFilePath);
          
          // 保存图片到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 1500
              });
            },
            fail: (error) => {
              console.error('保存到相册失败', error);
              handleSaveError(error);
            }
          });
        },
        fail: (error) => {
          console.error('Canvas转换为图片失败', error);
          handleSaveError(error);
        }
      });
    });
  
  // 处理保存错误
  function handleSaveError(error) {
    wx.hideLoading();
    
    // 检查是否权限被拒绝
    let isAuthDenied = false;
    
    if (error.errCode && error.errCode === 'auth_denied') {
      isAuthDenied = true;
    } else if (error.errMsg && (error.errMsg.includes('auth deny') || error.errMsg.includes('authorize no response'))) {
      isAuthDenied = true;
    }
    
    if (isAuthDenied) {
      // 权限被拒绝，引导用户授权
      wx.showModal({
        title: '相册权限申请',
        content: '保存图片需要您授权访问相册，是否前往设置开启？',
        confirmText: '前往设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认前往设置
            wx.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.writePhotosAlbum']) {
                  // 用户已开启权限，重新尝试保存
                  wx.showToast({
                    title: '权限已开启',
                    icon: 'success',
                    duration: 1500
                  });
                  
                  // 延时后重新尝试保存
                  setTimeout(() => {
                    savePetVisionImage(page);
                  }, 1000);
                } else {
                  // 用户未开启权限
                  wx.showToast({
                    title: '权限未开启',
                    icon: 'none',
                    duration: 1500
                  });
                }
              },
              fail: (err) => {
                console.error('打开设置页面失败:', err);
                wx.showToast({
                  title: '打开设置失败',
                  icon: 'none',
                  duration: 1500
                });
              }
            });
          }
        }
      });
    } else {
      // 其他错误
      wx.showToast({
        title: '保存失败',
        icon: 'none',
        duration: 1500
      });
    }
  }
}

// 处理相机错误
function handleCameraError(error, page) {
  console.error('相机错误:', error);
  
  // 构建错误消息
  let errorMsg = '相机启动失败，请检查相机权限或重启应用';
  let errorCode = 'unknown';
  let showSettingBtn = false;
  
  if (error) {
    // 如果错误对象中包含错误码
    if (error.errCode) {
      errorCode = error.errCode;
      
      // 根据错误码设置错误消息
      if (error.errCode === 'auth_denied') {
        errorMsg = '相机权限被拒绝，请点击下方按钮前往设置开启权限';
        showSettingBtn = true;
      } else if (error.errCode === 'camera_occupied') {
        errorMsg = '相机被其他应用占用，请关闭其他使用相机的应用后重试';
      } else if (error.errCode === 'get_setting_failed') {
        errorMsg = '获取相机权限失败，请重启小程序后重试';
      }
    }
    
    // 如果错误对象中包含错误消息
    if (error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMsg = '相机权限被拒绝，请点击下方按钮前往设置开启权限';
        errorCode = 'auth_denied';
        showSettingBtn = true;
      } else if (error.errMsg.includes('camera occupied')) {
        errorMsg = '相机被其他应用占用，请关闭其他使用相机的应用后重试';
        errorCode = 'camera_occupied';
      }
    }
  }
  
  // 设置错误状态
  page.setData({
    cameraError: true,
    cameraErrorMsg: errorMsg,
    cameraErrorCode: errorCode,
    cameraLoading: false,
    showCameraSettingBtn: showSettingBtn // 显示设置按钮标志
  });
  
  // 显示错误提示
  wx.showToast({
    title: '相机启动失败',
    icon: 'none',
    duration: 2000
  });
}

// 打开相机权限设置
function openCameraSetting(page) {
  const cameraManager = require('./camera-manager');
  
  wx.showModal({
    title: '相机权限请求',
    content: '需要开启相机权限才能使用爱宠视角功能，是否前往设置开启？',
    confirmText: '前往设置',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 用户确认前往设置
        cameraManager.openCameraSetting()
          .then((granted) => {
            if (granted) {
              // 用户已开启权限，重新初始化相机
              page.setData({
                cameraError: false,
                cameraLoading: true,
                showCameraSettingBtn: false
              });
              
              // 重新初始化相机
              setTimeout(() => {
                page.initCamera();
              }, 500);
            } else {
              // 用户未开启权限
              wx.showToast({
                title: '相机权限未开启',
                icon: 'none',
                duration: 2000
              });
            }
          })
          .catch((err) => {
            console.error('打开设置页面失败:', err);
            wx.showToast({
              title: '打开设置页面失败',
              icon: 'none',
              duration: 2000
            });
          });
      }
    }
  });
}

// 重试初始化相机
function retryCameraInit(page) {
  // 重置相机状态
  page.setData({
    cameraError: false,
    cameraLoading: true,
    showCameraSettingBtn: false
  });
  
  // 延迟重新初始化相机
  setTimeout(() => {
    page.initCamera();
  }, 500);
}

module.exports = {
  adjustPerformanceSettings,
  initMemoryBuffers,
  saveCanvasToAlbum,
  savePetVisionCanvasToAlbum,
  savePetVisionImage,
  handleCameraError,
  openCameraSetting,
  retryCameraInit
};
