/**
 * 视觉配置模块
 * 集中管理所有视觉处理参数
 */

// 相机数据配置
const CAMERA_CONFIG = {
  RESOLUTION: {
    HIGH: { width: 1920, height: 1080 },
    MEDIUM: { width: 1280, height: 720 },
    LOW: { width: 640, height: 480 }
  },
  FRAME_RATE: 15, // 默认帧率
  DEVICE_POSITION: 'back' // 默认使用后置相机
};

// 二色视觉配置
const DICHROMATIC_VISION_CONFIG = {
  // 犬科动物参数 (红绿色盲，只能看见蓝黄)
  DOG: {
    RED_FACTOR: 0.5,  // 红色因子
    GREEN_FACTOR: 0.5, // 绿色因子
    BLUE_PRESERVE: true // 保持蓝色不变
  },
  // 猫科动物参数 (二色视觉，能看见蓝绿)
  CAT: {
    RED_FACTOR: { r: 0.1, g: 0.2, b: 0.1 },
    GREEN_FACTOR: { r: 0.2, g: 0.6, b: 0.2 },
    BLUE_FACTOR: { r: 0.1, g: 0.2, b: 0.7 }
  },
  ENABLED: true // 默认启用二色视觉
};

// 视力辨析度配置
const VISUAL_ACUITY_CONFIG = {
  // 犬科动物视力辨析度参数
  DOG: {
    RESOLUTION_FACTOR: 0.5, // 犬科视力清晰度因子 (0.1-1.0，值越大越清晰)
    VIEW_FIELD_FACTOR: 0.3  // 视野因子 (0.2-3.0，值越大边缘越模糊)
  },
  // 猫科动物视力辨析度参数
  CAT: {
    RESOLUTION_FACTOR: 0.2,  // 猫科视力清晰度因子，相当于20/100
    SIAMESE_RESOLUTION_FACTOR: 0.15, // 暹罗猫特殊视力清晰度因子，相当于15/100
    VIEW_FIELD_FACTOR: 0.3   // 视野因子
  },
  // 兼容旧版本代码的默认值
  RESOLUTION_FACTOR: 0.5, 
  VIEW_FIELD_FACTOR: 0.3,
  ENABLED: true // 默认启用视力辨析度处理
};

// 夜视能力配置
const NIGHT_VISION_CONFIG = {
  // 犬科动物夹视能力参数
  DOG: {
    BRIGHTNESS: 1.5, // 亮度增强因子
    CONTRAST: 1.3    // 对比度增强因子
  },
  // 猫科动物夹视能力参数
  CAT: {
    BRIGHTNESS: 2.0, // 亮度增强因子
    CONTRAST: 1.5,   // 对比度增强因子
    // 暹罗猫特殊夹视能力参数，因为眼睛有轻微的视轴对位问题
    SIAMESE_BRIGHTNESS: 1.5, // 暹罗猫亮度增强因子
    SIAMESE_CONTRAST: 1.3    // 暹罗猫对比度增强因子
  },
  LOW_LIGHT_THRESHOLD: 60, // 低光照阈值
  ENABLED: true // 默认启用夹视能力
};

// 强光适应配置
const BRIGHT_LIGHT_ADAPTATION_CONFIG = {
  // 犬科动物强光适应参数
  DOG: {
    BRIGHTNESS_REDUCTION: 0.7,  // 亮度降低因子
    CONTRAST_INCREASE: 1.2,     // 对比度增强因子
    VIGNETTE_STRENGTH: 0.3,     // 暗角强度（模拟视野变窄）
    HIGHLIGHT_THRESHOLD: 200     // 高光阈值（模拟过曝效果）
  },
  // 猫科动物强光适应参数（更敏感）
  CAT: {
    BRIGHTNESS_REDUCTION: 0.6,  // 亮度降低因子
    CONTRAST_INCREASE: 1.3,     // 对比度增强因子
    VIGNETTE_STRENGTH: 0.4,     // 暗角强度（模拟视野变窄）
    HIGHLIGHT_THRESHOLD: 180     // 高光阈值（模拟过曝效果）
  },
  HIGH_LIGHT_THRESHOLD: 225,    // 强光阈值 (从210提高到225，降低敏感度)
  ENABLED: true                 // 默认启用强光适应
};

// 运动视觉配置
const MOTION_VISION_CONFIG = {
  // 犬科动物运动视觉参数
  DOG: {
    MOTION_SENSITIVITY: 3.0,     // 运动敏感度，范围从0-5，设置为3.0
    MOTION_THRESHOLD: 10.0,      // 运动阈值，设置为10.0
    MOTION_SIZE_THRESHOLD: 10.0  // 运动物体大小阈值，范围从1-50，设置为10.0
  },
  // 猫科动物运动视觉参数
  CAT: {
    MOTION_SENSITIVITY: 3.0,     // 运动敏感度，范围从0-5，设置为3.0
    MOTION_THRESHOLD: 8.0,       // 猫科运动阈值，设置为8.0（比犬科略低）
    MOTION_SIZE_THRESHOLD: 10.0  // 运动物体大小阈值，范围从1-50，设置为10.0
  },
  ENABLED: false                 // 默认不启用运动视觉
};

// 导出所有配置
module.exports = {
  CAMERA_CONFIG,
  DICHROMATIC_VISION_CONFIG,
  VISUAL_ACUITY_CONFIG,
  NIGHT_VISION_CONFIG,
  BRIGHT_LIGHT_ADAPTATION_CONFIG,
  MOTION_VISION_CONFIG,
  
  // 获取完整配置的方法
  getFullConfig() {
    return {
      camera: { ...CAMERA_CONFIG },
      dichromaticVision: { ...DICHROMATIC_VISION_CONFIG },
      visualAcuity: { ...VISUAL_ACUITY_CONFIG },
      nightVision: { ...NIGHT_VISION_CONFIG },
      brightLightAdaptation: { ...BRIGHT_LIGHT_ADAPTATION_CONFIG }
    };
  }
};
