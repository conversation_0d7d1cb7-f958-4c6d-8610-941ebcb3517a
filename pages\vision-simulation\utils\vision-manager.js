/**
 * 视觉管理器模块
 * 负责协调各个视觉处理模块的工作
 */

// 导入相关模块
const visionConfig = require('./vision-config');
const visionProcessor = require('./vision-processor');
const webglRenderer = require('./webgl-renderer');
const devicePerformance = require('./device-performance');

// 当前配置
let currentConfig = visionConfig.getFullConfig();

// 光照环境持续时间检测相关变量
// 低光相关
let lowLightStartTime = 0; // 低光环境开始时间
let isInLowLightCondition = false; // 当前是否处于低光条件(但不一定触发夜视)
const LOW_LIGHT_DURATION_THRESHOLD = 1500; // 低光持续时间阈值(毫秒)

// 强光相关
let brightLightStartTime = 0; // 强光环境开始时间
let isInBrightLightCondition = false; // 当前是否处于强光条件(但不一定触发强光模式)
const BRIGHT_LIGHT_DURATION_THRESHOLD = 800; // 增加强光持续时间阈值(从500毫秒增加到800毫秒)

// 初始化视觉管理器
async function initVisionManager(options = {}) {
  // 初始化设备性能检测
  const performanceLevel = devicePerformance.initDevicePerformance();
  console.log('设备性能级别:', performanceLevel);
  
  // 根据设备性能调整视觉效果参数
  const adjustedConfig = adjustConfigByPerformance(visionConfig.getFullConfig(), performanceLevel);
  
  // 合并默认配置和传入的选项
  currentConfig = {
    ...adjustedConfig,
    ...options,
    isLowLight: false, // 初始化低光照状态
    isBrightLight: false, // 初始化强光状态
    performanceLevel: performanceLevel // 保存性能级别
  };
  
  try {
    // 初始化WebGL渲染器 - 使用正确的canvas ID并等待Promise解析
    const webglContext = await webglRenderer.initWebGL('#processCanvas');
    
    // 设置视觉处理器的性能级别
    visionProcessor.setDevicePerformance(performanceLevel);
    
    return {
      config: currentConfig,
      webglContext: webglContext
    };
  } catch (error) {
    console.error('初始化WebGL渲染器失败:', error);
    return {
      config: currentConfig,
      webglContext: null
    };
  }
}

// 根据设备性能调整配置
function adjustConfigByPerformance(config, performanceLevel) {
  // 创建配置的深拷贝，避免修改原始配置
  const adjustedConfig = JSON.parse(JSON.stringify(config));
  
  // 获取渲染质量配置
  const renderQuality = devicePerformance.getRenderQualityConfig();
  
  // 根据设备性能调整视觉效果参数
  switch(performanceLevel) {
    case devicePerformance.PERFORMANCE_LEVELS.LOW:
      // 低性能设备：简化视觉效果
      // 调整视力辨析度参数 - 低性能设备使用更低的辨析度
      adjustedConfig.visualAcuity.RESOLUTION_FACTOR = Math.min(adjustedConfig.visualAcuity.RESOLUTION_FACTOR, 0.6);
      adjustedConfig.visualAcuity.ANTI_ALIAS_FACTOR = 0.5; // 降低抗锈齿强度
      
      // 调整夜视能力参数 - 简化夜视效果
      adjustedConfig.nightVision.DOG.BRIGHTNESS = Math.min(adjustedConfig.nightVision.DOG.BRIGHTNESS, 1.8);
      adjustedConfig.nightVision.CAT.BRIGHTNESS = Math.min(adjustedConfig.nightVision.CAT.BRIGHTNESS, 1.8);
      
      // 调整强光适应参数 - 简化强光效果
      adjustedConfig.brightLightAdaptation.DOG.VIGNETTE_STRENGTH = 0.25; // 降低暗角强度
      adjustedConfig.brightLightAdaptation.CAT.VIGNETTE_STRENGTH = 0.35;
      break;
      
    case devicePerformance.PERFORMANCE_LEVELS.MEDIUM:
      // 中性能设备：平衡效果和性能
      // 保持原有参数不变，这是默认配置
      break;
      
    case devicePerformance.PERFORMANCE_LEVELS.HIGH:
      // 高性能设备：增强视觉效果
      // 增强视力辨析度效果
      adjustedConfig.visualAcuity.ANTI_ALIAS_FACTOR = 1.0; // 完全抗锈齿
      
      // 增强夜视效果
      adjustedConfig.nightVision.DOG.CONTRAST = 1.2; // 增强对比度
      adjustedConfig.nightVision.CAT.CONTRAST = 1.3;
      
      // 增强强光适应效果
      adjustedConfig.brightLightAdaptation.DOG.VIGNETTE_STRENGTH = 0.35; // 增强暗角效果
      adjustedConfig.brightLightAdaptation.CAT.VIGNETTE_STRENGTH = 0.45;
      break;
  }
  
  // 添加正常白天暗化因子
  // 正常白天情况下，犬猫眼中的画面亮度都比人类要暗，因为没那么多锥细胞
  if (!adjustedConfig.dayVision) {
    adjustedConfig.dayVision = {
      ENABLED: true,
      DARKENING_FACTOR: 0.85 // 暗化因子，可以根据性能级别调整
    };
  }
  
  return adjustedConfig;
}

// 更新二色视觉配置
function updateDichromaticVisionConfig(newConfig) {
  // 仅更新二色视觉相关的配置
  currentConfig.dichromaticVision = {
    ...currentConfig.dichromaticVision,
    ...newConfig
  };
  
  return currentConfig.dichromaticVision;
}

// 更新视力辨析度配置
function updateVisualAcuityConfig(newConfig) {
  // 仅更新视力辨析度相关的配置
  currentConfig.visualAcuity = {
    ...currentConfig.visualAcuity,
    ...newConfig
  };
  
  // 兼容处理：如果提供了旧的ANTI_ALIAS_FACTOR参数，则将其转换为VIEW_FIELD_FACTOR
  if (newConfig.ANTI_ALIAS_FACTOR !== undefined && newConfig.VIEW_FIELD_FACTOR === undefined) {
    currentConfig.visualAcuity.VIEW_FIELD_FACTOR = newConfig.ANTI_ALIAS_FACTOR * 3.0; // 转换比例
    console.log('将旧的抗锈齿因子转换为视野因子:', currentConfig.visualAcuity.VIEW_FIELD_FACTOR);
  }
  
  return currentConfig.visualAcuity;
}

// 更新夜视能力配置
function updateNightVisionConfig(newConfig) {
  // 如果提供了猫科动物的参数
  if (newConfig.CAT) {
    currentConfig.nightVision.CAT = {
      ...currentConfig.nightVision.CAT,
      ...newConfig.CAT
    };
  }
  
  // 如果提供了犬科动物的参数
  if (newConfig.DOG) {
    currentConfig.nightVision.DOG = {
      ...currentConfig.nightVision.DOG,
      ...newConfig.DOG
    };
  }
  
  // 其他通用参数（例如LOW_LIGHT_THRESHOLD和ENABLED）
  const { CAT, DOG, ...otherParams } = newConfig;
  if (Object.keys(otherParams).length > 0) {
    currentConfig.nightVision = {
      ...currentConfig.nightVision,
      ...otherParams
    };
  }
  
  return currentConfig.nightVision;
}

// 更新强光适应配置
function updateBrightLightAdaptationConfig(newConfig) {
  if (!newConfig) return;
  
  // 更新犬科动物强光适应参数
  if (newConfig.DOG) {
    if (newConfig.DOG.BRIGHTNESS_REDUCTION !== undefined) {
      currentConfig.brightLightAdaptation.DOG.BRIGHTNESS_REDUCTION = newConfig.DOG.BRIGHTNESS_REDUCTION;
    }
    if (newConfig.DOG.CONTRAST_INCREASE !== undefined) {
      currentConfig.brightLightAdaptation.DOG.CONTRAST_INCREASE = newConfig.DOG.CONTRAST_INCREASE;
    }
    if (newConfig.DOG.VIGNETTE_STRENGTH !== undefined) {
      currentConfig.brightLightAdaptation.DOG.VIGNETTE_STRENGTH = newConfig.DOG.VIGNETTE_STRENGTH;
    }
    if (newConfig.DOG.HIGHLIGHT_THRESHOLD !== undefined) {
      currentConfig.brightLightAdaptation.DOG.HIGHLIGHT_THRESHOLD = newConfig.DOG.HIGHLIGHT_THRESHOLD;
    }
  }
  
  // 更新猫科动物强光适应参数
  if (newConfig.CAT) {
    if (newConfig.CAT.BRIGHTNESS_REDUCTION !== undefined) {
      currentConfig.brightLightAdaptation.CAT.BRIGHTNESS_REDUCTION = newConfig.CAT.BRIGHTNESS_REDUCTION;
    }
    if (newConfig.CAT.CONTRAST_INCREASE !== undefined) {
      currentConfig.brightLightAdaptation.CAT.CONTRAST_INCREASE = newConfig.CAT.CONTRAST_INCREASE;
    }
    if (newConfig.CAT.VIGNETTE_STRENGTH !== undefined) {
      currentConfig.brightLightAdaptation.CAT.VIGNETTE_STRENGTH = newConfig.CAT.VIGNETTE_STRENGTH;
    }
    if (newConfig.CAT.HIGHLIGHT_THRESHOLD !== undefined) {
      currentConfig.brightLightAdaptation.CAT.HIGHLIGHT_THRESHOLD = newConfig.CAT.HIGHLIGHT_THRESHOLD;
    }
  }
}

// 更新运动视觉配置
function updateMotionVisionConfig(newConfig) {
  if (!newConfig) return;
  
  // 确保运动视觉配置存在
  if (!currentConfig.motionVision) {
    currentConfig.motionVision = {
      DOG: {
        MOTION_SENSITIVITY: visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY,
        MOTION_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD,
        MOTION_SIZE_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD
      },
      CAT: {
        MOTION_SENSITIVITY: visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SENSITIVITY,
        MOTION_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_THRESHOLD,
        MOTION_SIZE_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SIZE_THRESHOLD
      },
      ENABLED: false
    };
  }
  
  // 更新犬科动物运动视觉参数
  if (newConfig.DOG) {
    if (newConfig.DOG.MOTION_SENSITIVITY !== undefined) {
      currentConfig.motionVision.DOG.MOTION_SENSITIVITY = newConfig.DOG.MOTION_SENSITIVITY;
    }
    if (newConfig.DOG.MOTION_THRESHOLD !== undefined) {
      currentConfig.motionVision.DOG.MOTION_THRESHOLD = newConfig.DOG.MOTION_THRESHOLD;
    }
    if (newConfig.DOG.MOTION_SIZE_THRESHOLD !== undefined) {
      currentConfig.motionVision.DOG.MOTION_SIZE_THRESHOLD = newConfig.DOG.MOTION_SIZE_THRESHOLD;
    }
  }
  
  // 更新猫科动物运动视觉参数
  if (newConfig.CAT) {
    if (newConfig.CAT.MOTION_SENSITIVITY !== undefined) {
      currentConfig.motionVision.CAT.MOTION_SENSITIVITY = newConfig.CAT.MOTION_SENSITIVITY;
    }
    if (newConfig.CAT.MOTION_THRESHOLD !== undefined) {
      currentConfig.motionVision.CAT.MOTION_THRESHOLD = newConfig.CAT.MOTION_THRESHOLD;
    }
    if (newConfig.CAT.MOTION_SIZE_THRESHOLD !== undefined) {
      currentConfig.motionVision.CAT.MOTION_SIZE_THRESHOLD = newConfig.CAT.MOTION_SIZE_THRESHOLD;
    }
  }
  
  // 更新运动视觉启用状态
  if (newConfig.ENABLED !== undefined) {
    currentConfig.motionVision.ENABLED = newConfig.ENABLED;
  }
  
  console.log('运动视觉参数已更新:', currentConfig.motionVision);
}

// 处理相机帧
function processFrame(frame, webglContext, animalType = 'dog', motionParams = null, forceDisableNightVision = false, forceDisableBrightLight = false) {
  try {
    // 检测光照级别
    const lightResult = visionProcessor.detectLightLevel(
      frame, 
      currentConfig.nightVision.LOW_LIGHT_THRESHOLD, 
      currentConfig.isLowLight
    );
    
    // 检测强光环境
    const brightLightResult = visionProcessor.detectBrightLight(
      frame,
      currentConfig.brightLightAdaptation.HIGH_LIGHT_THRESHOLD,
      animalType
    );
    
    // 处理光照环境持续时间检测
    const currentTime = Date.now();
    
    // 1. 处理低光持续时间检测
    // 检查是否处于低光条件(原始亮度检测，不考虑持续时间)
    if (lightResult.brightness < currentConfig.nightVision.LOW_LIGHT_THRESHOLD) {
      // 如果之前不是低光条件，记录开始时间
      if (!isInLowLightCondition) {
        lowLightStartTime = currentTime;
        isInLowLightCondition = true;
      }
      
      // 检查低光持续时间是否超过阈值
      if (currentTime - lowLightStartTime >= LOW_LIGHT_DURATION_THRESHOLD) {
        // 持续时间超过阈值，触发夜视模式
        lightResult.isLowLight = true;
      } else {
        // 持续时间未超过阈值，不触发夜视模式
        lightResult.isLowLight = false;
      }
    } else {
      // 不是低光条件，重置状态
      isInLowLightCondition = false;
      lightResult.isLowLight = false;
    }
    
    // 2. 处理强光持续时间检测
    // 检查是否处于强光条件(原始检测，不考虑持续时间)
    if ((brightLightResult.brightPixelRatio > 0.25) || brightLightResult.isBrightLight) {
      // 如果之前不是强光条件，记录开始时间
      if (!isInBrightLightCondition) {
        brightLightStartTime = currentTime;
        isInBrightLightCondition = true;
      }
      
      // 检查强光持续时间是否超过阈值
      if (currentTime - brightLightStartTime >= BRIGHT_LIGHT_DURATION_THRESHOLD) {
        // 持续时间超过阈值，触发强光模式
        brightLightResult.isBrightLight = true;
      } else {
        // 持续时间未超过阈值，不触发强光模式
        brightLightResult.isBrightLight = false;
      }
    } else {
      // 不是强光条件，重置状态
      isInBrightLightCondition = false;
      brightLightResult.isBrightLight = false;
    }
    
    // 更新当前配置中的光照状态
    // 如果强制禁用夜视或强光效果，则忽略环境光照状态
    currentConfig.isLowLight = forceDisableNightVision ? false : lightResult.isLowLight;
    currentConfig.isBrightLight = forceDisableBrightLight ? false : brightLightResult.isBrightLight;
    
    // 根据性能级别动态调整渲染参数
    const renderConfig = {
      visualAcuity: currentConfig.visualAcuity,
      nightVision: currentConfig.nightVision,
      dichromaticVision: currentConfig.dichromaticVision,
      brightLightAdaptation: currentConfig.brightLightAdaptation,
      isLowLight: currentConfig.isLowLight,
      isBrightLight: currentConfig.isBrightLight,
      animalType: animalType,
      // 添加正常白天暗化因子
      dayVision: currentConfig.dayVision,
      // 添加运动视觉参数 - 确保在夜视能力基础上进行叠加处理
      motionVision: {
        // 使用从 UI 传入的运动视觉参数，如果有的话
        enabled: motionParams ? motionParams.enabled : false,
        // 新增的运动视觉参数
        DOG: {
          // 优先使用从 UI 传入的参数，如果没有则使用默认值
          MOTION_SENSITIVITY: motionParams && motionParams.sensitivity ? motionParams.sensitivity : 5.0,
          MOTION_THRESHOLD: motionParams && motionParams.threshold ? motionParams.threshold : 25.0,
          MOTION_SIZE_THRESHOLD: motionParams && motionParams.sizeThreshold ? motionParams.sizeThreshold : 4.0
        },
        CAT: {
          // 优先使用从 UI 传入的参数，如果没有则使用默认值
          MOTION_SENSITIVITY: motionParams && motionParams.sensitivity ? motionParams.sensitivity * 1.2 : 6.0, // 猫科敏感度更高
          MOTION_THRESHOLD: motionParams && motionParams.threshold ? motionParams.threshold * 0.8 : 20.0, // 猫科阈值更低
          MOTION_SIZE_THRESHOLD: motionParams && motionParams.sizeThreshold ? motionParams.sizeThreshold * 0.75 : 3.0 // 猫科大小阈值更低
        }
      }
    };
    
    // 只在调试模式下输出日志
    if (false) { // 设置为false关闭调试日志
      console.log('应用到渲染器的运动视觉参数:', {
        motionParams: motionParams,
        appliedParams: animalType === 'cat' ? renderConfig.motionVision.CAT : renderConfig.motionVision.DOG,
        enabled: renderConfig.motionVision.enabled
      });
    }
    
    // 渲染帧
    webglRenderer.renderFrame(
      webglContext,
      frame,
      renderConfig,
      {
        // 如果强制禁用夜视或强光效果，则忽略环境光照状态
        isLowLight: forceDisableNightVision ? false : currentConfig.isLowLight,
        isBrightLight: forceDisableBrightLight ? false : currentConfig.isBrightLight,
        performanceLevel: currentConfig.performanceLevel,
        // 传递运动视觉开关
        motion: renderConfig.motionVision.enabled,
        // 直接传递完整的运动视觉参数对象
        motionSensitivity: animalType === 'cat' ? renderConfig.motionVision.CAT.MOTION_SENSITIVITY : renderConfig.motionVision.DOG.MOTION_SENSITIVITY,
        motionThreshold: animalType === 'cat' ? renderConfig.motionVision.CAT.MOTION_THRESHOLD : renderConfig.motionVision.DOG.MOTION_THRESHOLD,
        motionSizeThreshold: animalType === 'cat' ? renderConfig.motionVision.CAT.MOTION_SIZE_THRESHOLD : renderConfig.motionVision.DOG.MOTION_SIZE_THRESHOLD
      }
    );
    
    // 只在调试模式下输出日志
    if (false) { // 设置为false关闭调试日志
      console.log('最终传递给渲染器的参数:', {
        motion: renderConfig.motionVision.enabled,
        motionSensitivity: animalType === 'cat' ? renderConfig.motionVision.CAT.MOTION_SENSITIVITY : renderConfig.motionVision.DOG.MOTION_SENSITIVITY,
        motionThreshold: animalType === 'cat' ? renderConfig.motionVision.CAT.MOTION_THRESHOLD : renderConfig.motionVision.DOG.MOTION_THRESHOLD,
        motionSizeThreshold: animalType === 'cat' ? renderConfig.motionVision.CAT.MOTION_SIZE_THRESHOLD : renderConfig.motionVision.DOG.MOTION_SIZE_THRESHOLD
      });
    }
    
    // 返回处理结果
    return {
      brightness: lightResult.brightness,
      isLowLight: forceDisableNightVision ? false : currentConfig.isLowLight,
      isBrightLight: forceDisableBrightLight ? false : currentConfig.isBrightLight,
      brightPixelRatio: brightLightResult.brightPixelRatio,
      performanceLevel: currentConfig.performanceLevel
    };
  } catch (error) {
    console.error('处理相机帧错误:', error);
    return {
      brightness: 0,
      isLowLight: false,
      isBrightLight: false,
      brightPixelRatio: 0
    };
  }
}

// 修复运动视觉参数
function fixMotionVisionParameters() {
  // 确保运动视觉配置存在
  if (!currentConfig.motionVision) {
    currentConfig.motionVision = {
      DOG: {
        MOTION_SENSITIVITY: visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY,
        MOTION_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD,
        MOTION_SIZE_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD
      },
      CAT: {
        MOTION_SENSITIVITY: visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SENSITIVITY,
        MOTION_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_THRESHOLD,
        MOTION_SIZE_THRESHOLD: visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SIZE_THRESHOLD
      },
      ENABLED: false
    };
  }
  
  // 确保猫科动物参数正确
  currentConfig.motionVision.CAT.MOTION_SENSITIVITY = visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SENSITIVITY;
  currentConfig.motionVision.CAT.MOTION_THRESHOLD = visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_THRESHOLD;
  currentConfig.motionVision.CAT.MOTION_SIZE_THRESHOLD = visionConfig.MOTION_VISION_CONFIG.CAT.MOTION_SIZE_THRESHOLD;
  
  // 确保犬科动物参数正确
  currentConfig.motionVision.DOG.MOTION_SENSITIVITY = visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SENSITIVITY;
  currentConfig.motionVision.DOG.MOTION_THRESHOLD = visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_THRESHOLD;
  currentConfig.motionVision.DOG.MOTION_SIZE_THRESHOLD = visionConfig.MOTION_VISION_CONFIG.DOG.MOTION_SIZE_THRESHOLD;
  
  console.log('运动视觉参数已修复:', currentConfig.motionVision);
  return currentConfig.motionVision;
}

// 导出模块
module.exports = {
  initVisionManager,
  updateDichromaticVisionConfig,
  updateVisualAcuityConfig,
  updateNightVisionConfig,
  updateBrightLightAdaptationConfig,
  updateMotionVisionConfig,
  fixMotionVisionParameters,
  processFrame,
  getCurrentConfig: () => currentConfig
};
