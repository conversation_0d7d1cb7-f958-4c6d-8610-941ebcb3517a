/**
 * 视觉模式管理器
 * 负责处理视觉模式的选择、切换、参数设置等功能
 */

const visionManager = require('./vision-manager');
const tipsManager = require('./tips-manager');

class VisionModeManager {
  constructor() {
    this.pageInstance = null;
    this.currentMode = 'nightVision';
    this.modeDetails = {
      dichromatic: {
        title: "二色视觉（基础层）",
        desc: "模拟犬科动物的二色视觉系统，主要感知蓝色和黄色光谱，对红绿色调区分能力较弱。犬科动物无法区分红色与绿色，但在蓝黄色调上有较好辨识能力。",
        includes: "仅二色视觉"
      },
      acuity: {
        title: "视力辨析（第2层）",
        desc: "模拟犬科动物视力辨析能力，约为人类的1/4-1/5，远处物体变得模糊。犬科动物近距离视力较好，但无法看清远处细节，无法阅读文字，但对移动物体敏感。",
        includes: "二色视觉 + 视力辨析"
      },
      nightVision: {
        title: "光感模式（第3层）",
        desc: "模拟犬科动物在低光环境中的出色视觉，包括亮度增强和对比度调整。犬科动物具有出色的夜间视觉能力，视网膜上的感光细胞比人类多2-3倍，能在几乎全黑环境中辨识物体。",
        includes: "二色视觉 + 视力辨析 + 光感模式"
      },
      motionVision: {
        title: "运动视觉（全功能）",
        desc: "模拟犬科动物对运动目标的敏锐感知能力，能迅速捕捉微小的动态变化。犬科动物视网膜上的感光细胞对移动物体极为敏感，即使在低光和远距离条件下，也能迅速察觉到细微的运动目标。",
        includes: "二色视觉 + 视力辨析 + 光感模式 + 运动视觉"
      }
    };
  }

  /**
   * 初始化视觉模式管理器
   * @param {Object} pageInstance - 页面实例
   */
  init(pageInstance) {
    this.pageInstance = pageInstance;
    this.currentMode = pageInstance.data.currentVisionMode || 'nightVision';
  }

  /**
   * 显示/隐藏视觉模式选择器
   */
  toggleVisionModeSelector() {
    const newState = !this.pageInstance.data.showVisionModeSelector;
    
    this.pageInstance.setData({
      showVisionModeSelector: newState,
      showVisionModeDetail: newState, // 同步显示模式详情
      // 隐藏视觉选择引导提示
      showVisionSelectGuide: false
    });
    
    // 添加触觉反馈
    if (newState && wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  }

  /**
   * 选择视觉模式
   * @param {string} mode - 视觉模式
   */
  selectVisionMode(mode) {
    // 如果点击当前模式，仅关闭选择器
    if (mode === this.currentMode) {
      this.toggleVisionModeSelector();
      return;
    }
    
    // 添加触觉反馈
    if (wx.vibrateShort) {
      // 根据不同模式提供略微不同的触觉反馈
      switch(mode) {
        case 'dichromatic':
          wx.vibrateShort({ type: 'light' });
          break;
        case 'acuity':
          wx.vibrateShort({ type: 'medium' });
          break;
        case 'nightVision':
          wx.vibrateShort({ type: 'medium' });
          break;
        case 'motionVision':
          wx.vibrateShort({ type: 'heavy' });
          break;
        default:
          wx.vibrateShort({ type: 'medium' });
      }
    }
    
    // 根据选择的模式设置相应的特性
    let features = { ...this.pageInstance.data.features };
    
    // 根据视觉模式的层级关系设置特性
    switch(mode) {
      case 'dichromatic':
        // 仅启用二色视觉，禁用视力辨析、夜视和运动视觉
        features.color = true;
        features.night = false;
        features.motion = false;
        
        this._updateVisionManagerForDichromatic();
        break;
        
      case 'acuity':
        // 启用二色视觉和视力辨析度，禁用夜视和运动视觉
        features.color = true;
        features.night = false;
        features.motion = false;
        
        this._updateVisionManagerForAcuity();
        break;
        
      case 'nightVision':
        // 启用二色视觉、视力辨析度和夜视，禁用运动视觉
        features.color = true;
        features.night = true;
        features.motion = false;
        
        this._updateVisionManagerForNightVision();
        break;
        
      case 'motionVision':
        // 启用所有功能
        features.color = true;
        features.night = true;
        features.motion = true;
        
        this._updateVisionManagerForMotionVision();
        break;
    }
    
    // 更新当前模式和特性
    this.currentMode = mode;
    this.pageInstance.setData({
      currentVisionMode: mode,
      features: features,
      showVisionModeSelector: false
    });
    
    // 如果有最后一帧，重新渲染
    if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
      this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
    }
    
    // 显示模式切换提示
    const modeName = this.getModeName(mode);
    wx.showToast({
      title: `已切换到${modeName}`,
      icon: 'none',
      duration: 1500
    });
    
    // 如果切换到运动视觉，显示运动视觉提示
    if (mode === 'motionVision') {
      setTimeout(() => {
        tipsManager.showMotionVisionTip(this.pageInstance);
      }, 1000);
    }
  }

  /**
   * 更新视觉管理器配置 - 二色视觉模式
   */
  _updateVisionManagerForDichromatic() {
    if (visionManager) {
      // 启用二色视觉
      visionManager.updateDichromaticVisionConfig({
        ENABLED: true
      });
      
      // 禁用视力辨析度 - 设置最高清晰度
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: 1.0,  // 最高清晰度，禁用模糊效果
        VIEW_FIELD_FACTOR: 0.1   // 最低视野因子，全视野清晰
      });
      
      // 禁用夜视能力
      visionManager.updateNightVisionConfig({
        ENABLED: false
      });
      
      // 禁用运动视觉
      visionManager.updateMotionVisionConfig({
        ENABLED: false
      });
    }
  }

  /**
   * 更新视觉管理器配置 - 视力辨析模式
   */
  _updateVisionManagerForAcuity() {
    if (visionManager) {
      // 启用二色视觉
      visionManager.updateDichromaticVisionConfig({
        ENABLED: true
      });
      
      // 启用视力辨析度 - 恢复默认设置
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: this.pageInstance.data.originalDogVisionParams.resolutionFactor,
        VIEW_FIELD_FACTOR: this.pageInstance.data.originalDogVisionParams.antiAliasFactor
      });
      
      // 禁用夜视能力
      visionManager.updateNightVisionConfig({
        ENABLED: false
      });
      
      // 禁用运动视觉
      visionManager.updateMotionVisionConfig({
        ENABLED: false
      });
    }
  }

  /**
   * 更新视觉管理器配置 - 夜视模式
   */
  _updateVisionManagerForNightVision() {
    if (visionManager) {
      // 启用二色视觉
      visionManager.updateDichromaticVisionConfig({
        ENABLED: true
      });
      
      // 启用视力辨析度
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: this.pageInstance.data.originalDogVisionParams.resolutionFactor,
        VIEW_FIELD_FACTOR: this.pageInstance.data.originalDogVisionParams.antiAliasFactor
      });
      
      // 启用夜视能力
      visionManager.updateNightVisionConfig({
        ENABLED: true,
        BRIGHTNESS: this.pageInstance.data.originalDogVisionParams.brightness,
        CONTRAST: this.pageInstance.data.originalDogVisionParams.contrast
      });
      
      // 禁用运动视觉
      visionManager.updateMotionVisionConfig({
        ENABLED: false
      });
    }
  }

  /**
   * 更新视觉管理器配置 - 运动视觉模式
   */
  _updateVisionManagerForMotionVision() {
    if (visionManager) {
      // 启用二色视觉
      visionManager.updateDichromaticVisionConfig({
        ENABLED: true
      });
      
      // 启用视力辨析度
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: this.pageInstance.data.originalDogVisionParams.resolutionFactor,
        VIEW_FIELD_FACTOR: this.pageInstance.data.originalDogVisionParams.antiAliasFactor
      });
      
      // 启用夜视能力
      visionManager.updateNightVisionConfig({
        ENABLED: true,
        BRIGHTNESS: this.pageInstance.data.originalDogVisionParams.brightness,
        CONTRAST: this.pageInstance.data.originalDogVisionParams.contrast
      });
      
      // 启用运动视觉
      visionManager.updateMotionVisionConfig({
        ENABLED: true,
        MOTION_SENSITIVITY: this.pageInstance.data.originalDogVisionParams.motionSensitivity,
        MOTION_THRESHOLD: this.pageInstance.data.originalDogVisionParams.motionThreshold,
        MOTION_SIZE_THRESHOLD: this.pageInstance.data.originalDogVisionParams.motionSizeThreshold
      });
    }
  }

  /**
   * 获取模式名称
   * @param {string} mode - 模式标识
   * @returns {string} 模式名称
   */
  getModeName(mode) {
    switch(mode) {
      case 'dichromatic': return '二色视觉';
      case 'acuity': return '视力辨析';
      case 'nightVision': return '光感模式';
      case 'motionVision': return '运动视觉';
      default: return '未知模式';
    }
  }

  /**
   * 切换运动视觉功能
   */
  toggleMotionVision() {
    // 切换运动视觉状态
    const newMotionState = !this.pageInstance.data.features.motion;

    // 更新数据
    this.pageInstance.setData({
      'features.motion': newMotionState,
      // 如果启用运动视觉，则更新当前模式
      currentVisionMode: newMotionState ? 'motionVision' : 'nightVision'
    });

    // 更新当前模式
    this.currentMode = newMotionState ? 'motionVision' : 'nightVision';

    // 如果启用运动视觉，显示提示
    if (newMotionState) {
      tipsManager.showMotionVisionTip(this.pageInstance);

      // 确保运动视觉参数正确设置
      const animalType = this.pageInstance.data.features.isCat ? 'cat' : 'dog';
      console.log('当前动物类型:', animalType);

      // 修复运动视觉参数
      if (visionManager) {
        const fixedParams = visionManager.fixMotionVisionParameters();
        console.log('运动视觉参数已修复:', fixedParams);

        // 更新UI显示的参数和原始参数值
        if (animalType === 'cat') {
          this.pageInstance.setData({
            'dogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
            'dogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
            'dogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD,
            // 同时更新原始参数值，用于重置
            'originalDogVisionParams.motionSensitivity': fixedParams.CAT.MOTION_SENSITIVITY,
            'originalDogVisionParams.motionThreshold': fixedParams.CAT.MOTION_THRESHOLD,
            'originalDogVisionParams.motionSizeThreshold': fixedParams.CAT.MOTION_SIZE_THRESHOLD
          });
        } else {
          this.pageInstance.setData({
            'dogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
            'dogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
            'dogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD,
            // 同时更新原始参数值，用于重置
            'originalDogVisionParams.motionSensitivity': fixedParams.DOG.MOTION_SENSITIVITY,
            'originalDogVisionParams.motionThreshold': fixedParams.DOG.MOTION_THRESHOLD,
            'originalDogVisionParams.motionSizeThreshold': fixedParams.DOG.MOTION_SIZE_THRESHOLD
          });
        }
      }
    }

    // 如果有最后一帧，重新渲染
    if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
      this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
    }

    // 添加触觉反馈，提升用户体验
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'medium'
      });
    }

    // 显示切换状态的反馈提示
    wx.showToast({
      title: newMotionState ? '运动视觉已开启' : '运动视觉已关闭',
      icon: 'none',
      duration: 1500
    });
  }

  /**
   * 显示/隐藏视觉参数调整面板
   */
  toggleVisionParams() {
    // 在显示参数面板前，确保同步最新的品种特定参数
    if (!this.pageInstance.data.showVisionParams) {
      // 即将显示参数面板，同步品种特定参数到UI
      const breedName = this.pageInstance.data.breedName;
      if (breedName) {
        let resolutionFactor = this.pageInstance.data.originalDogVisionParams.resolutionFactor;

        // 根据品种设置视力清晰度
        if (this.pageInstance.data.features.isCat) {
          // 猫科动物处理逻辑
          if (breedName === '暹罗') {
            resolutionFactor = 0.15; // 暹罗猫特殊视力清晰度因子
          } else {
            resolutionFactor = 0.2; // 猫科视力清晰度因子
          }
        } else {
          // 犬科动物处理逻辑
          if (breedName.includes('拉布拉多')) {
            resolutionFactor = 1.0;
          } else if (breedName.includes('金毛')) {
            resolutionFactor = 0.33;
          } else if (breedName.includes('边境') || breedName.includes('牧羊')) {
            resolutionFactor = 0.4;
          } else if (breedName.includes('贵宾') || breedName.includes('柯基') ||
                    breedName.includes('比熊') || breedName.includes('哈士奇')) {
            resolutionFactor = 0.27;
          } else if (breedName.includes('吉娃娃') || breedName.includes('法斗') ||
                    breedName.includes('法国')) {
            resolutionFactor = 0.25;
          }
        }

        // 确保UI显示正确的参数值
        this.pageInstance.setData({
          'dogVisionParams.resolutionFactor': resolutionFactor
        });

        console.log('参数面板显示前同步品种[' + breedName + ']的视力清晰度参数:', resolutionFactor);
      }
    }

    // 切换参数面板的显示状态
    this.pageInstance.setData({
      showVisionParams: !this.pageInstance.data.showVisionParams
    });
  }

  /**
   * 显示/隐藏分辨率选择器
   */
  toggleResolutionSelector() {
    this.pageInstance.setData({
      showResolutionSelector: !this.pageInstance.data.showResolutionSelector
    });
  }

  /**
   * 切换分辨率
   * @param {number} index - 分辨率选项索引
   */
  changeResolution(index) {
    const currentOption = this.pageInstance.data.resolutionOptions[index];

    console.log('切换分辨率到:', currentOption.name);

    // 更新分辨率设置
    this.pageInstance.setData({
      currentResolutionIndex: index,
      showResolutionSelector: false,
      'frame.width': currentOption.width,
      'frame.height': currentOption.height
    });

    // 重新初始化相机以应用新分辨率
    this.pageInstance.cameraController.reinitializeCamera();

    // 显示切换提示
    wx.showToast({
      title: `已切换到${currentOption.name}`,
      icon: 'none',
      duration: 1500
    });
  }

  /**
   * 获取当前视觉模式详情
   * @returns {Object} 当前模式详情
   */
  getCurrentModeDetail() {
    return this.modeDetails[this.currentMode] || {};
  }

  /**
   * 获取所有模式详情
   * @returns {Object} 所有模式详情
   */
  getAllModeDetails() {
    return this.modeDetails;
  }

  /**
   * 重置视觉模式到默认状态
   */
  resetToDefaultMode() {
    this.selectVisionMode('nightVision');
  }

  /**
   * 检查当前模式是否支持某个功能
   * @param {string} feature - 功能名称 ('color', 'night', 'motion')
   * @returns {boolean} 是否支持
   */
  isFeatureSupported(feature) {
    const features = this.pageInstance.data.features;
    return features[feature] || false;
  }

  /**
   * 获取当前模式状态
   * @returns {Object} 模式状态信息
   */
  getModeState() {
    return {
      currentMode: this.currentMode,
      features: this.pageInstance.data.features,
      showVisionModeSelector: this.pageInstance.data.showVisionModeSelector,
      showVisionParams: this.pageInstance.data.showVisionParams,
      showResolutionSelector: this.pageInstance.data.showResolutionSelector
    };
  }
}

module.exports = VisionModeManager;
