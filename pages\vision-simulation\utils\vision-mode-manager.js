/**
 * 视觉模式管理器
 * 负责处理视觉模式的选择、切换、参数设置等功能
 */

const visionManager = require('./vision-manager');
const tipsManager = require('./tips-manager');

function VisionModeManager() {
  this.pageInstance = null;
  this.currentMode = 'nightVision';
  this.modeDetails = {
    dichromatic: {
      title: "二色视觉（基础层）",
      desc: "模拟犬科动物的二色视觉系统，主要感知蓝色和黄色光谱，对红绿色调区分能力较弱。犬科动物无法区分红色与绿色，但在蓝黄色调上有较好辨识能力。",
      includes: "仅二色视觉"
    },
    acuity: {
      title: "视力辨析（第2层）",
      desc: "模拟犬科动物视力辨析能力，约为人类的1/4-1/5，远处物体变得模糊。犬科动物近距离视力较好，但无法看清远处细节，无法阅读文字，但对移动物体敏感。",
      includes: "二色视觉 + 视力辨析"
    },
    nightVision: {
      title: "光感模式（第3层）",
      desc: "模拟犬科动物在低光环境中的出色视觉，包括亮度增强和对比度调整。犬科动物具有出色的夜间视觉能力，视网膜上的感光细胞比人类多2-3倍，能在几乎全黑环境中辨识物体。",
      includes: "二色视觉 + 视力辨析 + 光感模式"
    },
    motionVision: {
      title: "运动视觉（全功能）",
      desc: "模拟犬科动物对运动目标的敏锐感知能力，能迅速捕捉微小的动态变化。犬科动物视网膜上的感光细胞对移动物体极为敏感，即使在低光和远距离条件下，也能迅速察觉到细微的运动目标。",
      includes: "二色视觉 + 视力辨析 + 光感模式 + 运动视觉"
    }
  };
}

/**
 * 初始化视觉模式管理器
 * @param {Object} pageInstance - 页面实例
 */
VisionModeManager.prototype.init = function(pageInstance) {
  this.pageInstance = pageInstance;
  this.currentMode = pageInstance.data.currentVisionMode || 'nightVision';
};

/**
 * 显示/隐藏视觉模式选择器
 */
VisionModeManager.prototype.toggleVisionModeSelector = function() {
  const newState = !this.pageInstance.data.showVisionModeSelector;
  
  this.pageInstance.setData({
    showVisionModeSelector: newState,
    showVisionModeDetail: newState, // 同步显示模式详情
    // 隐藏视觉选择引导提示
    showVisionSelectGuide: false
  });
  
  // 添加触觉反馈
  if (newState && wx.vibrateShort) {
    wx.vibrateShort({
      type: 'light'
    });
  }
};

/**
 * 选择视觉模式
 * @param {string} mode - 视觉模式
 */
VisionModeManager.prototype.selectVisionMode = function(mode) {
  // 如果点击当前模式，仅关闭选择器
  if (mode === this.currentMode) {
    this.toggleVisionModeSelector();
    return;
  }
  
  // 添加触觉反馈
  if (wx.vibrateShort) {
    switch(mode) {
      case 'dichromatic':
        wx.vibrateShort({ type: 'light' });
        break;
      case 'acuity':
        wx.vibrateShort({ type: 'medium' });
        break;
      case 'nightVision':
        wx.vibrateShort({ type: 'medium' });
        break;
      case 'motionVision':
        wx.vibrateShort({ type: 'heavy' });
        break;
      default:
        wx.vibrateShort({ type: 'medium' });
    }
  }
  
  // 根据选择的模式设置相应的特性
  let features = { ...this.pageInstance.data.features };
  
  // 根据视觉模式的层级关系设置特性
  switch(mode) {
    case 'dichromatic':
      features.color = true;
      features.night = false;
      features.motion = false;
      this._updateVisionManagerForDichromatic();
      break;
    case 'acuity':
      features.color = true;
      features.night = false;
      features.motion = false;
      this._updateVisionManagerForAcuity();
      break;
    case 'nightVision':
      features.color = true;
      features.night = true;
      features.motion = false;
      this._updateVisionManagerForNightVision();
      break;
    case 'motionVision':
      features.color = true;
      features.night = true;
      features.motion = true;
      this._updateVisionManagerForMotionVision();
      break;
  }
  
  // 更新当前模式和特性
  this.currentMode = mode;
  this.pageInstance.setData({
    currentVisionMode: mode,
    features: features,
    showVisionModeSelector: false
  });
  
  // 如果有最后一帧，重新渲染
  if (this.pageInstance._lastFrame && this.pageInstance.webglContext) {
    this.pageInstance.processFrameWebGL(this.pageInstance._lastFrame);
  }
  
  // 显示模式切换提示
  const modeName = this.getModeName(mode);
  wx.showToast({
    title: `已切换到${modeName}`,
    icon: 'none',
    duration: 1500
  });
  
  // 如果切换到运动视觉，显示运动视觉提示
  if (mode === 'motionVision') {
    setTimeout(() => {
      tipsManager.showMotionVisionTip(this.pageInstance);
    }, 1000);
  }
};

/**
 * 更新视觉管理器配置 - 二色视觉模式
 */
VisionModeManager.prototype._updateVisionManagerForDichromatic = function() {
  if (visionManager) {
    // 启用二色视觉
    visionManager.updateDichromaticVisionConfig({
      ENABLED: true
    });
    
    // 禁用视力辨析度 - 设置最高清晰度
    visionManager.updateVisualAcuityConfig({
      RESOLUTION_FACTOR: 1.0,  // 最高清晰度，禁用模糊效果
      VIEW_FIELD_FACTOR: 0.1   // 最低视野因子，全视野清晰
    });
    
    // 禁用夜视能力
    visionManager.updateNightVisionConfig({
      ENABLED: false
    });
    
    // 禁用运动视觉
    visionManager.updateMotionVisionConfig({
      ENABLED: false
    });
  }
};

/**
 * 更新视觉管理器配置 - 视力辨析模式
 */
VisionModeManager.prototype._updateVisionManagerForAcuity = function() {
  if (visionManager) {
    // 启用二色视觉
    visionManager.updateDichromaticVisionConfig({
      ENABLED: true
    });
    
    // 启用视力辨析度 - 恢复默认设置
    visionManager.updateVisualAcuityConfig({
      RESOLUTION_FACTOR: this.pageInstance.data.originalDogVisionParams.resolutionFactor,
      VIEW_FIELD_FACTOR: this.pageInstance.data.originalDogVisionParams.antiAliasFactor
    });
    
    // 禁用夜视能力
    visionManager.updateNightVisionConfig({
      ENABLED: false
    });
    
    // 禁用运动视觉
    visionManager.updateMotionVisionConfig({
      ENABLED: false
    });
  }
};

/**
 * 更新视觉管理器配置 - 夜视模式
 */
VisionModeManager.prototype._updateVisionManagerForNightVision = function() {
  if (visionManager) {
    // 启用二色视觉
    visionManager.updateDichromaticVisionConfig({
      ENABLED: true
    });
    
    // 启用视力辨析度
    visionManager.updateVisualAcuityConfig({
      RESOLUTION_FACTOR: this.pageInstance.data.originalDogVisionParams.resolutionFactor,
      VIEW_FIELD_FACTOR: this.pageInstance.data.originalDogVisionParams.antiAliasFactor
    });
    
    // 启用夜视能力
    visionManager.updateNightVisionConfig({
      ENABLED: true,
      BRIGHTNESS: this.pageInstance.data.originalDogVisionParams.brightness,
      CONTRAST: this.pageInstance.data.originalDogVisionParams.contrast
    });
    
    // 禁用运动视觉
    visionManager.updateMotionVisionConfig({
      ENABLED: false
    });
  }
};

/**
 * 更新视觉管理器配置 - 运动视觉模式
 */
VisionModeManager.prototype._updateVisionManagerForMotionVision = function() {
  if (visionManager) {
    // 启用二色视觉
    visionManager.updateDichromaticVisionConfig({
      ENABLED: true
    });
    
    // 启用视力辨析度
    visionManager.updateVisualAcuityConfig({
      RESOLUTION_FACTOR: this.pageInstance.data.originalDogVisionParams.resolutionFactor,
      VIEW_FIELD_FACTOR: this.pageInstance.data.originalDogVisionParams.antiAliasFactor
    });
    
    // 启用夜视能力
    visionManager.updateNightVisionConfig({
      ENABLED: true,
      BRIGHTNESS: this.pageInstance.data.originalDogVisionParams.brightness,
      CONTRAST: this.pageInstance.data.originalDogVisionParams.contrast
    });
    
    // 启用运动视觉
    visionManager.updateMotionVisionConfig({
      ENABLED: true,
      MOTION_SENSITIVITY: this.pageInstance.data.originalDogVisionParams.motionSensitivity,
      MOTION_THRESHOLD: this.pageInstance.data.originalDogVisionParams.motionThreshold,
      MOTION_SIZE_THRESHOLD: this.pageInstance.data.originalDogVisionParams.motionSizeThreshold
    });
  }
};

/**
 * 获取模式名称
 * @param {string} mode - 模式标识
 * @returns {string} 模式名称
 */
VisionModeManager.prototype.getModeName = function(mode) {
  switch(mode) {
    case 'dichromatic': return '二色视觉';
    case 'acuity': return '视力辨析';
    case 'nightVision': return '光感模式';
    case 'motionVision': return '运动视觉';
    default: return '未知模式';
  }
};

module.exports = VisionModeManager;
