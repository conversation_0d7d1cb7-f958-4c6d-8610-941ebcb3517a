/**
 * 视觉处理模块
 * 负责处理各种视觉效果和图像处理算法
 */

// 导入配置模块
const visionConfig = require('./vision-config');
const devicePerformance = require('./device-performance');
const nightVisionEnhancer = require('./night-vision-enhancer');

// 检测图像亮度
function detectLightLevel(frame, threshold, currentState) {
  // 使用配置中的阈值（如果未提供）
  threshold = threshold || visionConfig.NIGHT_VISION_CONFIG.LOW_LIGHT_THRESHOLD;
  
  // 获取设备性能配置
  const samplingConfig = devicePerformance.getSamplingConfig();
  const performanceLevel = devicePerformance.getCurrentPerformanceLevel();
  
  // 使用设备性能配置中的采样步长
  const sampleStep = samplingConfig.sampleStep;
  const sampleRadius = samplingConfig.sampleRadius;
  const useAdaptiveSampling = samplingConfig.useAdaptiveSampling;
  
  // 只采样部分像素点以提高性能
  const data = new Uint8Array(frame.data);
  const width = frame.width;
  const height = frame.height;
  
  let totalBrightness = 0;
  let sampleCount = 0;
  
  // 根据设备性能选择不同的采样策略
  if (useAdaptiveSampling) {
    // 高效能设备：使用同心圆采样策略，更符合视觉感知
    const centerX = Math.floor(width / 2);
    const centerY = Math.floor(height / 2);
    const maxRadius = Math.min(width, height) * sampleRadius;
    
    // 同心圆采样
    for (let r = 0; r < maxRadius; r += sampleStep) {
      // 计算当前半径的圆周上的采样点数量
      const circumference = 2 * Math.PI * r;
      const pointCount = Math.max(8, Math.floor(circumference / sampleStep));
      
      // 在圆周上均匀采样
      for (let i = 0; i < pointCount; i++) {
        const angle = (i / pointCount) * 2 * Math.PI;
        const x = Math.floor(centerX + r * Math.cos(angle));
        const y = Math.floor(centerY + r * Math.sin(angle));
        
        // 确保采样点在图像范围内
        if (x >= 0 && x < width && y >= 0 && y < height) {
          const idx = (y * width + x) * 4;
          // 使用更快的亮度计算方法
          const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
          // 根据距离中心的远近给予不同权重
          const weight = 1.0 - (r / maxRadius) * 0.5; // 中心区域权重更高
          totalBrightness += brightness * weight;
          sampleCount += weight;
        }
      }
    }
  } else {
    // 低性能设备：使用简单的矩形区域采样
    // 只采样中心区域以提高性能和准确性
    const startY = Math.floor(height * (0.5 - sampleRadius));
    const endY = Math.floor(height * (0.5 + sampleRadius));
    const startX = Math.floor(width * (0.5 - sampleRadius));
    const endX = Math.floor(width * (0.5 + sampleRadius));
    
    // 采样计算亮度
    for (let y = startY; y < endY; y += sampleStep) {
      const rowOffset = y * width;
      for (let x = startX; x < endX; x += sampleStep) {
        const idx = (rowOffset + x) * 4;
        // 计算RGB的平均值作为亮度
        const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
        totalBrightness += brightness;
        sampleCount++;
      }
    }
  }
  
  // 计算加权平均亮度
  const avgBrightness = Math.round(totalBrightness / (sampleCount || 1));
  
  // 使用滞后比较，避免频繁切换状态
  // 增加滞后阈值，提高稳定性
  const hysteresis = performanceLevel === devicePerformance.PERFORMANCE_LEVELS.LOW ? 20 : 15;
  
  let isLowLight = currentState;
  if (!isLowLight && avgBrightness < threshold) {
    isLowLight = true;
  } else if (isLowLight && avgBrightness > threshold + hysteresis) {
    isLowLight = false;
  }
  
  // 返回亮度信息
  return {
    brightness: avgBrightness,
    isLowLight: isLowLight
  };
}

// 将原始帧数据转换为二色视觉数据
function convertToDichromaticVision(frameData, width, height, animalType = 'dog') {
  // 使用配置中的参数
  const config = animalType === 'cat' 
    ? visionConfig.DICHROMATIC_VISION_CONFIG.CAT 
    : visionConfig.DICHROMATIC_VISION_CONFIG.DOG;
  try {
    // 创建新的数组来存储转换后的数据
    const convertedData = new Uint8Array(frameData.length);
    
    // 遍历所有像素
    for (let i = 0; i < frameData.length; i += 4) {
      // 获取RGB值
      const r = frameData[i];
      const g = frameData[i + 1];
      const b = frameData[i + 2];
      const a = frameData[i + 3];
      
      // 根据动物类型应用不同的二色视觉转换
      if (animalType === 'cat') {
        // 猫科视觉处理
        convertedData[i] = r * config.RED_FACTOR.r + g * config.RED_FACTOR.g + b * config.RED_FACTOR.b;
        convertedData[i + 1] = r * config.GREEN_FACTOR.r + g * config.GREEN_FACTOR.g + b * config.GREEN_FACTOR.b;
        convertedData[i + 2] = r * config.BLUE_FACTOR.r + g * config.BLUE_FACTOR.g + b * config.BLUE_FACTOR.b;
      } else {
        // 犬科视觉处理
        const dogRed = Math.round((r * config.RED_FACTOR + g * config.GREEN_FACTOR));
        convertedData[i] = dogRed;     // R
        convertedData[i + 1] = dogRed; // G
        convertedData[i + 2] = b;      // B (保持不变)
      }
      convertedData[i + 3] = a;      // Alpha
    }
    
    return convertedData;
  } catch (error) {
    console.error('二色视觉转换错误:', error);
    return frameData; // 出错时返回原始数据
  }
}

// 应用夜视增强
function applyNightVisionEnhancement(frameData, width, height, options = {}) {
  // 获取动物类型
  const animalType = options.animalType || 'dog';
  
  // 根据动物类型获取对应的夜视参数
  let brightness, contrast;
  if (animalType === 'cat') {
    brightness = options.brightness || visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS;
    contrast = options.contrast || visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST;
  } else {
    brightness = options.brightness || visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS;
    contrast = options.contrast || visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST;
  }
  
  try {
    // 创建新的数组来存储增强后的数据
    const enhancedData = new Uint8Array(frameData.length);
    
    // 遍历所有像素
    for (let i = 0; i < frameData.length; i += 4) {
      // 获取RGB值
      let r = frameData[i];
      let g = frameData[i + 1];
      let b = frameData[i + 2];
      const a = frameData[i + 3];
      
      // 应用亮度增强
      r = Math.min(255, Math.round(r * brightness));
      g = Math.min(255, Math.round(g * brightness));
      b = Math.min(255, Math.round(b * brightness));
      
      // 应用对比度增强 - 使用简化的对比度调整公式
      const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
      r = Math.max(0, Math.min(255, Math.round(factor * (r - 128) + 128)));
      g = Math.max(0, Math.min(255, Math.round(factor * (g - 128) + 128)));
      b = Math.max(0, Math.min(255, Math.round(factor * (b - 128) + 128)));
      
      // 设置增强后的值
      enhancedData[i] = r;
      enhancedData[i + 1] = g;
      enhancedData[i + 2] = b;
      enhancedData[i + 3] = a;
    }
    
    return enhancedData;
  } catch (error) {
    console.error('夜视增强错误:', error);
    return frameData; // 出错时返回原始数据
  }
}

// 处理相机帧
function processFrame(frame, options = {}, features = {}) {
  // 合并默认配置和传入的选项
  const visionParams = {
    ...visionConfig.getFullConfig(),
    ...options
  };
  try {
    // 检测光照级别
    const lightResult = detectLightLevel(
      frame, 
      visionParams.nightVision.LOW_LIGHT_THRESHOLD, 
      visionParams.isLowLight
    );
    
    // 创建处理结果对象
    const result = {
      frame: frame,
      brightness: lightResult.brightness,
      isLowLight: lightResult.isLowLight
    };
    
    return result;
  } catch (error) {
    console.error('处理相机帧错误:', error);
    return {
      frame: frame,
      brightness: 0,
      isLowLight: false
    };
  }
}

// 检测强光环境
function detectBrightLight(frame, threshold, animalType = 'dog') {
  // 使用配置中的阈值（如果未提供）
  threshold = threshold || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.HIGH_LIGHT_THRESHOLD;
  
  // 使用与检测低光类似的方法，但判断条件相反
  const data = new Uint8Array(frame.data);
  const width = frame.width;
  const height = frame.height;
  
  // 性能优化：根据设备性能动态调整采样步长
  const performanceLevel = devicePerformance.getCurrentPerformanceLevel();
  const sampleStep = performanceLevel === devicePerformance.PERFORMANCE_LEVELS.LOW ? 40 : 
                    (performanceLevel === devicePerformance.PERFORMANCE_LEVELS.MEDIUM ? 30 : 20);
  
  let totalBrightness = 0;
  let brightPixelCount = 0;
  let sampleCount = 0;
  
  // 性能优化：使用更高效的采样策略，只采样中心区域
  const startY = Math.floor(height * 0.25);
  const endY = Math.floor(height * 0.75);
  const startX = Math.floor(width * 0.25);
  const endX = Math.floor(width * 0.75);
  
  // 采样计算亮度
  for (let y = startY; y < endY; y += sampleStep) {
    const rowOffset = y * width;
    for (let x = startX; x < endX; x += sampleStep) {
      const idx = (rowOffset + x) * 4;
      // 计算RGB的平均值作为亮度
      const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
      totalBrightness += brightness;
      
      // 统计亮度超过阈值的像素数量
      if (brightness > threshold) {
        brightPixelCount++;
      }
      
      sampleCount++;
    }
  }
  
  // 计算平均亮度
  const avgBrightness = Math.round(totalBrightness / sampleCount);
  
  // 计算亮像素比例
  const brightPixelRatio = brightPixelCount / sampleCount;
  
  // 判断是否为强光环境：平均亮度高或者高亮度像素比例足够大
  // 优化判断逻辑，使用动态阈值和权重组合
  const brightThreshold = threshold * 0.92; // 提高阈值以降低灵敏度(从0.85改为0.92)
  
  // 为猫科动物设置更低的强光阈值，体现其对强光更敏感的特性
  const ratioThreshold = animalType === 'cat' ? 0.25 : 0.30; // 提高比例阈值(恢复到原来的0.25和0.3)
  
  // 组合判断条件，增强稳定性
  const isBrightLight = 
    (avgBrightness > brightThreshold && brightPixelRatio > ratioThreshold * 0.5) || // 平均亮度高且有一定比例的亮像素(从0.4提高到0.5)
    (brightPixelRatio > ratioThreshold); // 或者亮像素比例足够高
  
  return {
    brightness: avgBrightness,
    brightPixelRatio: brightPixelRatio,
    isBrightLight: isBrightLight
  };
}

// 应用强光适应效果
function applyBrightLightAdaptation(frameData, width, height, options = {}) {
  // 获取动物类型
  const animalType = options.animalType || 'dog';
  
  // 根据动物类型获取对应的强光适应参数
  let brightnessReduction, contrastIncrease, vignetteStrength, highlightThreshold;
  
  if (animalType === 'cat') {
    brightnessReduction = options.brightnessReduction || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.BRIGHTNESS_REDUCTION;
    contrastIncrease = options.contrastIncrease || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.CONTRAST_INCREASE;
    vignetteStrength = options.vignetteStrength || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.VIGNETTE_STRENGTH;
    highlightThreshold = options.highlightThreshold || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.HIGHLIGHT_THRESHOLD;
  } else {
    brightnessReduction = options.brightnessReduction || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.BRIGHTNESS_REDUCTION;
    contrastIncrease = options.contrastIncrease || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.CONTRAST_INCREASE;
    vignetteStrength = options.vignetteStrength || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.VIGNETTE_STRENGTH;
    highlightThreshold = options.highlightThreshold || visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.HIGHLIGHT_THRESHOLD;
  }
  
  try {
    // 创建新的数组来存储处理后的数据
    const adaptedData = new Uint8Array(frameData.length);
    
    // 计算图像中心点
    const centerX = width / 2;
    const centerY = height / 2;
    const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);
    
    // 遍历所有像素
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;
        
        // 获取RGB值
        let r = frameData[idx];
        let g = frameData[idx + 1];
        let b = frameData[idx + 2];
        const a = frameData[idx + 3];
        
        // 计算当前像素到中心的距离，用于暗角效果
        const distX = x - centerX;
        const distY = y - centerY;
        const distance = Math.sqrt(distX * distX + distY * distY);
        const normalizedDistance = distance / maxDistance;
        
        // 应用暗角效果（模拟视野变窄）
        const vignetteFactor = 1.0 - (normalizedDistance * vignetteStrength);
        
        // 应用亮度降低
        r = Math.round(r * brightnessReduction * vignetteFactor);
        g = Math.round(g * brightnessReduction * vignetteFactor);
        b = Math.round(b * brightnessReduction * vignetteFactor);
        
        // 应用对比度增强
        const factor = (259 * (contrastIncrease + 255)) / (255 * (259 - contrastIncrease));
        r = Math.max(0, Math.min(255, Math.round(factor * (r - 128) + 128)));
        g = Math.max(0, Math.min(255, Math.round(factor * (g - 128) + 128)));
        b = Math.max(0, Math.min(255, Math.round(factor * (b - 128) + 128)));
        
        // 模拟过曝效果
        const pixelBrightness = (r + g + b) / 3;
        if (pixelBrightness > highlightThreshold) {
          // 对超过高光阈值的像素应用过曝效果
          const overExposureFactor = 1.0 + (pixelBrightness - highlightThreshold) / (255 - highlightThreshold) * 0.5;
          r = Math.min(255, Math.round(r * overExposureFactor));
          g = Math.min(255, Math.round(g * overExposureFactor));
          b = Math.min(255, Math.round(b * overExposureFactor));
        }
        
        // 设置处理后的值
        adaptedData[idx] = r;
        adaptedData[idx + 1] = g;
        adaptedData[idx + 2] = b;
        adaptedData[idx + 3] = a;
      }
    }
    
    return adaptedData;
  } catch (error) {
    console.error('强光适应处理错误:', error);
    return frameData; // 出错时返回原始数据
  }
}

// 设置设备性能级别（兼容旧代码）
function setDevicePerformance(level) {
  // 将旧的性能级别映射到新的性能级别
  let newLevel;
  switch(level) {
    case 'low':
      newLevel = devicePerformance.PERFORMANCE_LEVELS.LOW;
      break;
    case 'high':
      newLevel = devicePerformance.PERFORMANCE_LEVELS.HIGH;
      break;
    case 'medium':
    default:
      newLevel = devicePerformance.PERFORMANCE_LEVELS.MEDIUM;
  }
  
  // 使用新的设备性能模块设置性能级别
  devicePerformance.setPerformanceLevel(newLevel);
}

// 应用增强版夜视功能
function applyEnhancedNightVision(frameData, width, height, options = {}) {
  try {
    // 检测图像亮度
    const frame = { data: frameData, width, height };
    const lightInfo = detectLightLevel(frame, undefined, options.currentLowLightState || false);
    
    // 只在低光照环境下应用增强功能
    if (!lightInfo.isLowLight) {
      return {
        enhancedData: frameData,
        lightInfo: lightInfo,
        enhancementParams: null
      };
    }
    
    // 获取动物类型
    const animalType = options.animalType || 'dog';
    
    // 计算自适应增强参数
    const enhancementParams = nightVisionEnhancer.calculateAdaptiveParams(
      lightInfo.brightness, 
      animalType
    );
    
    // 应用基础夜视增强（保持原有逻辑）
    const baseEnhanced = applyNightVisionEnhancement(frameData, width, height, {
      animalType: animalType,
      brightness: enhancementParams.brightness,
      contrast: enhancementParams.contrast
    });
    
    // 返回增强结果和参数，WebGL着色器将处理高级增强
    return {
      enhancedData: baseEnhanced,
      lightInfo: lightInfo,
      enhancementParams: enhancementParams
    };
    
  } catch (error) {
    console.error('增强夜视处理错误:', error);
    return {
      enhancedData: frameData,
      lightInfo: { brightness: 128, isLowLight: false },
      enhancementParams: null
    };
  }
}

// 获取夜视增强配置
function getNightVisionEnhancementConfig() {
  return nightVisionEnhancer.getEnhancementConfig();
}

// 重置夜视增强状态
function resetNightVisionEnhancement() {
  nightVisionEnhancer.resetAdaptiveState();
}

module.exports = {
  detectLightLevel,
  detectBrightLight,
  convertToDichromaticVision,
  applyNightVisionEnhancement,
  applyBrightLightAdaptation,
  processFrame,
  setDevicePerformance,
  // 新增的增强夜视功能
  applyEnhancedNightVision,
  getNightVisionEnhancementConfig,
  resetNightVisionEnhancement
};
