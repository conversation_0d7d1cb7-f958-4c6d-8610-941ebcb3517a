/**
 * WebGL渲染器模块
 * 负责WebGL相关的初始化、着色器管理和图像渲染
 * 包含二色视觉、夜视能力、强光适应和运动视觉等功能
 */

// 导入配置模块
const visionConfig = require('./vision-config');
const nightVisionEnhancer = require('./night-vision-enhancer');

// 创建着色器
function createShader(gl, type, source) {
  const shader = gl.createShader(type);
  gl.shaderSource(shader, source);
  gl.compileShader(shader);
  
  // 检查着色器是否编译成功
  if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
    console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
    gl.deleteShader(shader);
    return null;
  }
  
  return shader;
}

// 初始化着色器
function initShaders(gl) {
  // 顶点着色器源码
  const vsSource = `
    attribute vec4 aVertexPosition;
    attribute vec2 aTextureCoord;
    varying highp vec2 vTextureCoord;
    void main(void) {
      gl_Position = aVertexPosition;
      vTextureCoord = aTextureCoord;
    }
  `;

  // 片段着色器源码 - 包含二色视觉转换、夜视增强、强光适应和运动视觉
  const fsSource = `
    precision mediump float;
    varying highp vec2 vTextureCoord;
    uniform sampler2D uSampler;
    
    // 视觉参数
    uniform float uResolutionFactor; // 辨析度因子 - 现在作为视力清晰度因子
    uniform float uAntiAliasFactor;  // 现在作为视野因子，控制视野范围
    uniform float uBrightness;       // 亮度增强
    uniform float uContrast;         // 对比度
    
    // 运动视觉参数
    uniform float uMotionSensitivity; // 运动敏感度
    uniform float uMotionThreshold;   // 运动阈值
    uniform float uMotionSizeThreshold; // 运动物体大小阈值
    
    // 强光适应参数
    uniform float uBrightnessReduction; // 亮度降低因子
    uniform float uContrastIncrease;    // 对比度增强因子
    uniform float uVignetteStrength;    // 暗角强度
    uniform float uHighlightThreshold;  // 高光阈值
    
    // 夜视增强参数
    uniform float uAdaptiveContrast;    // 自适应对比度
    uniform float uNoiseReduction;      // 噪点抑制强度
    uniform float uEdgeSharpening;      // 边缘锐化强度
    uniform int uEnableEnhancedNight;   // 启用增强夜视
    
    // 纹理尺寸 - WebGL 1.0不支持textureSize函数，所以需要传入
    uniform vec2 uTextureSize;       // 纹理尺寸
    
    // 前一帧的纹理 - 用于运动检测
    uniform sampler2D uPrevFrameSampler;
    
    // 功能开关
    uniform int uEnableColor;        // 启用二色视觉
    uniform int uEnableNight;        // 启用夜视增强
    uniform int uEnableBrightLight;  // 启用强光适应
    uniform int uEnableDayDarkening; // 启用白天暗化效果
    uniform int uEnableMotion;       // 启用运动视觉
    uniform float uIsCat;            // 区分猫科和犬科动物 (0.0 for dog, 1.0 for cat)
    uniform float uDayDarkeningFactor; // 白天暗化因子
    
    // 性能优化：预计算常量
    const float ZERO = 0.0;
    const float HALF = 0.5;
    const float ONE = 1.0;
    
    // 基于视网膜感受野的模糊函数 - 高质量版本，模拟犬猫视力辨析度
    vec4 retinalBlur(sampler2D texture, vec2 uv, float acuityFactor, float viewFieldFactor) {
      // 计算到视野中心的距离
      vec2 center = vec2(0.5, 0.5);
      float dist = length(uv - center) * 2.0; // 归一化距离
      
      // 极端测试模式 - 当视野因子达到极端值时显示更明显的效果
      bool extremeMode = false;
      float visualIndicator = 0.0;
      
      // 判断是否处于极端测试模式
      if (viewFieldFactor <= 0.3) { // 极低值 - 全视野清晰模式
        extremeMode = true;
        visualIndicator = 1.0 - viewFieldFactor / 0.3; // 视觉指示器强度
      } else if (viewFieldFactor >= 2.5) { // 极高值 - 隧视模式
        extremeMode = true;
        visualIndicator = (viewFieldFactor - 2.5) / 0.5; // 视觉指示器强度
      }
      
      // 使用更温和的视野因子，确保平滑过渡
      float amplifiedViewFieldFactor = viewFieldFactor * 1.5; // 进一步减小放大因子
      
      // 使用平滑的非线性函数计算距离因子
      float distFactor;
      
      if (viewFieldFactor >= 2.5) { // 隧视模式 - 中心小区域清晰，其余全部模糊
        distFactor = smoothstep(0.0, 0.2, dist * 2.0); // 非常小的清晰区域
      } else {
        // 使用更平滑的S型曲线，避免突变
        float normalizedDist = dist * amplifiedViewFieldFactor;
        distFactor = smoothstep(0.0, 1.0, normalizedDist * (2.0 - normalizedDist)); // 平滑S曲线
      }
      
      // 增强中心和周边的差异，但使用更平滑的过渡
      float peripheralBlur = max(0.05, acuityFactor * 0.15); // 减小边缘区域的模糊程度
      
      // 将输入的辨析度因子(0.2-1.0)转换为更广的模糊范围
      // 使用非线性映射，使低值时模糊更明显
      float mappedAcuity;
      
      if (acuityFactor >= 0.9) {
        // 接近人类视力时几乎不模糊
        mappedAcuity = 0.95;
      } else if (acuityFactor >= 0.7) {
        // 高辨析度犬种(如边境牧羊犬)
        mappedAcuity = 0.7 + (acuityFactor - 0.7) * 0.8; // 0.7-0.9区间
      } else if (acuityFactor >= 0.5) {
        // 中等辨析度犬种(如拉布拉多)
        mappedAcuity = 0.4 + (acuityFactor - 0.5) * 1.5; // 0.4-0.7区间
      } else if (acuityFactor >= 0.3) {
        // 低辨析度犬种(如金毛寻回犬)
        mappedAcuity = 0.2 + (acuityFactor - 0.3) * 1.0; // 0.2-0.4区间
      } else {
        // 非常低辨析度(如某些短鼻犬种)
        mappedAcuity = acuityFactor * 0.7; // 0.0-0.2区间
      }
      
      // 增强边缘区域的模糊度
      float peripheralBlurIntensity = max(0.05, mappedAcuity * 0.15);
      
      // 结合用户设置的辨析度因子和距离因子
      float effectiveBlur = mix(
        mappedAcuity,            // 中心区域使用映射后的辨析度
        peripheralBlurIntensity, // 边缘区域更模糊
        distFactor
      );
      
      // 采样半径与辨析度成反比，低辨析度时半径更大
      // 使用指数函数增强低辨析度时的模糊效果
      float radius = pow(1.0 - effectiveBlur, 0.8) * 5.0 + 1.0; // 增大最大半径并使用非线性映射
      vec2 pixelSize = 1.0 / uTextureSize;
      
      // 使用多通道高斯模糊，减少虚影
      vec4 color = vec4(0.0);
      float totalWeight = 0.0;
      
      // 中心像素权重 - 增加中心像素权重以保持清晰度
      vec4 centerColor = texture2D(texture, uv);
      float centerWeight = 1.5;
      color += centerColor * centerWeight;
      totalWeight += centerWeight;
      
      // 使用更多的采样圈，但每圈的权重迅速衰减
      // 第一圈 - 最近距离采样点 (4个正交方向)
      float firstRingRadius = radius * 0.25;
      for (int i = 0; i < 4; i++) {
        float angle = float(i) * 3.14159 * 0.5; // 0, 90, 180, 270度
        vec2 offset = vec2(cos(angle), sin(angle)) * firstRingRadius * pixelSize;
        vec4 sampleColor = texture2D(texture, uv + offset);
        
        // 使用更高的权重
        float weight = 0.9;
        color += sampleColor * weight;
        totalWeight += weight;
      }
      
      // 第二圈 - 中距离采样点 (4个对角方向)
      float secondRingRadius = radius * 0.5;
      for (int i = 0; i < 4; i++) {
        float angle = float(i) * 3.14159 * 0.5 + 3.14159 * 0.25; // 45, 135, 225, 315度
        vec2 offset = vec2(cos(angle), sin(angle)) * secondRingRadius * pixelSize;
        vec4 sampleColor = texture2D(texture, uv + offset);
        
        // 使用中等权重
        float weight = 0.6;
        color += sampleColor * weight;
        totalWeight += weight;
      }
      
      // 第三圈 - 远距离采样点 (8个方向，但权重较低)
      if (distFactor > 0.2) { // 只在稍微远离中心的区域使用
        float thirdRingRadius = radius * 0.75;
        for (int i = 0; i < 8; i++) {
          float angle = float(i) * 3.14159 * 0.25;
          vec2 offset = vec2(cos(angle), sin(angle)) * thirdRingRadius * pixelSize;
          vec4 sampleColor = texture2D(texture, uv + offset);
          
          // 使用较低权重
          float weight = 0.3 * (1.0 - distFactor * 0.5);
          color += sampleColor * weight;
          totalWeight += weight;
        }
      }
      
      // 第四圈 - 最远距离采样点 (仅在边缘区域使用，权重非常低)
      if (distFactor > 0.5) { // 只在远离中心的区域使用
        float fourthRingRadius = radius;
        for (int i = 0; i < 8; i++) {
          float angle = float(i) * 3.14159 * 0.25;
          vec2 offset = vec2(cos(angle), sin(angle)) * fourthRingRadius * pixelSize;
          vec4 sampleColor = texture2D(texture, uv + offset);
          
          // 使用非常低的权重，避免产生虚影
          float weight = 0.15 * distFactor;
          color += sampleColor * weight;
          totalWeight += weight;
        }
      }
      
      // 归一化
      vec4 finalColor = color / totalWeight;
      
      // 如果处于极端测试模式，添加视觉指示器
      if (extremeMode) {
        // 根据不同的极端模式添加不同的视觉指示
        if (viewFieldFactor <= 0.3) { // 全视野清晰模式
          // 在视野边缘添加淡蓝色边框指示全视野清晰模式
          if (dist > 0.9) {
            float borderIntensity = smoothstep(0.9, 1.0, dist) * visualIndicator * 0.3;
            finalColor = mix(finalColor, vec4(0.2, 0.4, 0.9, 1.0), borderIntensity);
          }
        } else if (viewFieldFactor >= 2.5) { // 隧视模式
          // 在视野外围添加暗化效果模拟隧视
          float vignette = smoothstep(0.2, 0.5, dist) * visualIndicator;
          finalColor = mix(finalColor, vec4(0.0, 0.0, 0.0, 1.0), vignette * 0.8);
        }
      }
      
      return finalColor;
    }
    
    // 暗角效果函数（模拟爱宠的短视野效果）- 高性能版本
    float vignette(vec2 uv, float strength) {
      // 计算到中心的距离，使用平方距离代替欧几里得距离以提高性能
      vec2 center = vec2(HALF, HALF);
      vec2 delta = uv - center;
      
      // 使用模拟欧几里得距离的快速计算方法
      // 这比直接使用distance()函数更高效
      float dist = length(delta) * 2.0;
      
      // 使用平滑函数代替clamp来实现更自然的暗角过渡
      // 使用平方公式使暗角效果在边缘处更强
      float vignetteEffect = smoothstep(0.0, 1.0, 1.0 - dist * strength);
      
      // 返回暗角因子
      return vignetteEffect;
    }
    
    // 边缘检测函数 - 检测运动区域中的边缘 - 优化版本
    bool isEdgePixel(sampler2D currentTexture, sampler2D prevTexture, vec2 uv, float threshold, float sensitivity) {
      vec2 pixelSize = 1.0 / uTextureSize;
      
      // 当前像素的运动差异 - 使用直接的阈值比较
      vec4 currentColor = texture2D(currentTexture, uv);
      vec4 prevColor = texture2D(prevTexture, uv);
      vec3 centerDiff = abs(currentColor.rgb - prevColor.rgb);
      float centerDiffMag = (centerDiff.r + centerDiff.g + centerDiff.b) / 3.0;
      
      // 使用直接的阈值比较，不再使用sensitivity作为放大因子
      bool centerHasMotion = centerDiffMag > threshold;
      
      // 如果当前像素没有运动，直接返回false
      if (!centerHasMotion) return false;
      
      // 检查周围8个方向
      int motionCount = 0;
      int nonMotionCount = 0;
      
      // 8个方向的偏移量
      vec2 offsets[8];
      offsets[0] = vec2(-1.0, -1.0) * pixelSize;
      offsets[1] = vec2(-1.0,  0.0) * pixelSize;
      offsets[2] = vec2(-1.0,  1.0) * pixelSize;
      offsets[3] = vec2( 0.0,  1.0) * pixelSize;
      offsets[4] = vec2( 1.0,  1.0) * pixelSize;
      offsets[5] = vec2( 1.0,  0.0) * pixelSize;
      offsets[6] = vec2( 1.0, -1.0) * pixelSize;
      offsets[7] = vec2( 0.0, -1.0) * pixelSize;
      
      // 检查周围像素 - 简化逻辑
      for (int i = 0; i < 8; i++) {
        vec2 sampleUV = uv + offsets[i];
        vec4 sampleCurrent = texture2D(currentTexture, sampleUV);
        vec4 samplePrev = texture2D(prevTexture, sampleUV);
        vec3 sampleDiff = abs(sampleCurrent.rgb - samplePrev.rgb);
        float sampleDiffMag = (sampleDiff.r + sampleDiff.g + sampleDiff.b) / 3.0;
        
        // 使用直接的阈值比较
        if (sampleDiffMag > threshold) {
          motionCount++;
        } else {
          nonMotionCount++;
        }
      }
      
      // 边缘像素的定义：当前像素有运动，且周围同时有运动和非运动像素
      return motionCount > 0 && nonMotionCount > 0;
    }
    
    void main(void) {
      // 获取原始纹理颜色
      vec4 originalColor = texture2D(uSampler, vTextureCoord);
      vec4 color = originalColor;
      
      // 1. 应用基于视网膜感受野的视力辨析度处理
      // uResolutionFactor: 视力清晰度因子 (0.1-1.0)
      // uAntiAliasFactor: 视野因子，控制视野范围 (0.5-2.0)
      vec4 blurredColor = retinalBlur(uSampler, vTextureCoord, uResolutionFactor, uAntiAliasFactor);
      
      // 平滑过渡，当辨析度因子接近最大值时，使用原始图像
      float resolutionMix = smoothstep(0.95, 1.0, uResolutionFactor);
      color = mix(blurredColor, originalColor, resolutionMix);
      
      // 2. 应用二色视觉转换 - 预计算猫科和犬科的转换结果
      if (uEnableColor == 1) {
        // 猫科视觉处理（二色视觉）
        vec3 catColor;
        catColor.r = 0.1 * color.r + 0.2 * color.g + 0.1 * color.b;
        catColor.g = 0.2 * color.r + 0.6 * color.g + 0.2 * color.b;
        catColor.b = 0.1 * color.r + 0.2 * color.g + 0.7 * color.b;
        
        // 犬科视觉处理（二色视觉）
        vec3 dogColor;
        float dogRed = (color.r + color.g) * 0.5;
        dogColor.r = dogRed;
        dogColor.g = dogRed;
        dogColor.b = color.b; // 保持蓝色不变
        
        // 使用uIsCat混合猫科和犬科的结果
        color.rgb = mix(dogColor, catColor, uIsCat);
      }
      
      // 3. 应用夜视增强 - 使用float类型转换代替if判断
      float nightVisionFactor = float(uEnableNight);
      if (nightVisionFactor > 0.0) {
        // 应用基础夜视增强
        color.rgb = color.rgb * mix(1.0, uBrightness, nightVisionFactor);
        color.rgb = mix(color.rgb, (color.rgb - 0.5) * uContrast + 0.5, nightVisionFactor);
        
        // 应用增强夜视功能
        float enhancedNightFactor = float(uEnableEnhancedNight);
        if (enhancedNightFactor > 0.0) {
          // 3.1 自适应对比度增强
          color.rgb = mix(color.rgb, (color.rgb - 0.5) * uAdaptiveContrast + 0.5, enhancedNightFactor);
          
          // 3.2 噪点抑制 - 双边滤波近似
          if (uNoiseReduction > 0.1) {
            vec4 filteredColor = vec4(0.0);
            float totalWeight = 0.0;
            
            // 采样步长（性能优化）
            float sampleStep = 1.0;
            vec2 pixelSize = 1.0 / uTextureSize;
            
            // 3x3采样窗口进行噪点抑制
            for (int i = -1; i <= 1; i++) {
              for (int j = -1; j <= 1; j++) {
                vec2 offset = vec2(float(i), float(j)) * pixelSize * uNoiseReduction * 2.0;
                vec4 sampleColor = texture2D(uSampler, vTextureCoord + offset);
                
                // 颜色相似性权重
                float colorDiff = length(sampleColor.rgb - color.rgb);
                float colorWeight = exp(-colorDiff * colorDiff * 30.0);
                
                // 空间距离权重
                float spatialWeight = exp(-dot(offset, offset) * 500.0);
                
                float weight = colorWeight * spatialWeight;
                filteredColor += sampleColor * weight;
                totalWeight += weight;
              }
            }
            
            if (totalWeight > 0.0) {
              vec4 denoised = filteredColor / totalWeight;
              color = mix(color, denoised, uNoiseReduction * enhancedNightFactor);
            }
          }
          
          // 3.3 边缘锐化 - 反锐化蒙版
          if (uEdgeSharpening > 0.1) {
            // 计算高斯模糊
            vec4 blur = vec4(0.0);
            float weights[9];
            weights[0] = 0.0625; weights[1] = 0.125; weights[2] = 0.0625;
            weights[3] = 0.125;  weights[4] = 0.25;  weights[5] = 0.125;
            weights[6] = 0.0625; weights[7] = 0.125; weights[8] = 0.0625;
            
            vec2 offsets[9];
            offsets[0] = vec2(-1.0, -1.0); offsets[1] = vec2(0.0, -1.0); offsets[2] = vec2(1.0, -1.0);
            offsets[3] = vec2(-1.0,  0.0); offsets[4] = vec2(0.0,  0.0); offsets[5] = vec2(1.0,  0.0);
            offsets[6] = vec2(-1.0,  1.0); offsets[7] = vec2(0.0,  1.0); offsets[8] = vec2(1.0,  1.0);
            
            for (int k = 0; k < 9; k++) {
              vec2 sampleUV = vTextureCoord + offsets[k] / uTextureSize * 1.5;
              blur += texture2D(uSampler, sampleUV) * weights[k];
            }
            
            // 应用反锐化蒙版
            vec4 sharpened = color + (color - blur) * uEdgeSharpening;
            color = mix(color, sharpened, uEdgeSharpening * enhancedNightFactor);
          }
        }
        
        // 添加轻微的绿色色调（模拟tapetum lucidum效果）
        vec3 nightTint = vec3(0.9, 1.0, 0.9);
        color.rgb = mix(color.rgb, color.rgb * nightTint, nightVisionFactor * 0.2);
      }
      
      // 4. 应用强光适应效果 - 优化暗角和过曝计算
      float brightLightFactor = float(uEnableBrightLight);
      if (brightLightFactor > 0.0) {
        // 计算暗角因子（模拟视野变窄）
        float vignetteStrength = mix(uVignetteStrength, uVignetteStrength * 1.2, uIsCat);
        float vignetteFactor = vignette(vTextureCoord, vignetteStrength);
        
        // 降低亮度，并应用暗角效果
        color.rgb *= mix(1.0, uBrightnessReduction * vignetteFactor, brightLightFactor);
        
        // 增强对比度
        float contrastFactor = mix(1.0, uContrastIncrease, brightLightFactor);
        color.rgb = (color.rgb - 0.5) * contrastFactor + 0.5;
        
        // 模拟过曝效果（模拟视网膜对强光的适应）
        float pixelBrightness = dot(color.rgb, vec3(0.299, 0.587, 0.114)); // 更准确的亮度计算
        float normalizedThreshold = uHighlightThreshold / 255.0;
        
        // 使用smoothstep实现平滑过渡的过曝效果
        float overExposure = smoothstep(normalizedThreshold, normalizedThreshold + 0.2, pixelBrightness);
        overExposure *= mix(1.0, 1.2, uIsCat); // 猫科动物过曝效果更强
        
        // 应用过曝效果
        color.rgb = mix(color.rgb, vec3(1.0), overExposure * brightLightFactor * 0.5);
      }
      
      // 确保颜色在有效范围内
      color.rgb = clamp(color.rgb, 0.0, 1.0);
      
      // 添加整体暗化效果（模拟犬猫视觉中亮度较低）
      // 正常白天情况下，犬猫眼中的画面亮度都比人类要暗，因为没那么多锥细胞
      float dayDarkeningFactor = uDayDarkeningFactor;
      // 当不在夜视模式且不在强光模式时应用暗化
      float normalLightCondition = (1.0 - float(uEnableNight)) * (1.0 - float(uEnableBrightLight)) * float(uEnableDayDarkening);
      
      // 根据动物类型调整暗化强度（猫科动物锥细胞更少，暗化更强）
      float animalAdjustedDarkening = mix(dayDarkeningFactor, dayDarkeningFactor * 0.95, uIsCat);
      color.rgb *= mix(1.0, animalAdjustedDarkening, normalLightCondition);
      
      // 5. 应用运动视觉效果 - 只对运动物体的外边缘轮廓进行高亮处理
      if (uEnableMotion == 1) {
        // 获取前一帧的颜色
        vec4 prevColor = texture2D(uPrevFrameSampler, vTextureCoord);
        
        // 计算当前帧与前一帧的差异
        vec3 diff = abs(color.rgb - prevColor.rgb);
        float diffMagnitude = (diff.r + diff.g + diff.b) / 3.0;
        
        // 移除猫科动物特殊的1.5倍敏感度放大，直接使用传入的参数
        float animalMotionSensitivity = uMotionSensitivity;
        
        // 直接使用运动阈值，不进行复杂的转换
        float normalizedThreshold = uMotionThreshold;
        
        // 检测是否为边缘像素 - 使用新的边缘检测函数
        bool isEdge = isEdgePixel(uSampler, uPrevFrameSampler, vTextureCoord, normalizedThreshold, animalMotionSensitivity);
        
        // 只有边缘像素才应用高亮效果
        if (isEdge) {
          // 计算边缘强度 - 使用简单线性计算
          float edgeIntensity = diffMagnitude / normalizedThreshold * 0.7;
          edgeIntensity = clamp(edgeIntensity, 0.0, 1.0);
          
          // 边缘高亮颜色 - 使用适度的白色高亮
          vec3 edgeHighlight = vec3(1.5, 1.5, 1.5);
          
          // 简化边缘效果应用
          // 将边缘区域的原始颜色进行调暗
          vec3 edgeColor = color.rgb * 0.7; // 简单的线性暗化
          
          // 应用边缘效果和高亮
          color.rgb = mix(color.rgb, edgeColor, 0.6);
          color.rgb = mix(color.rgb, edgeHighlight, edgeIntensity * 0.6);
        }
      }
      
      gl_FragColor = color;
    }
  `;

  // 创建着色器程序
  const vertexShader = createShader(gl, gl.VERTEX_SHADER, vsSource);
  const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fsSource);
  
  if (!vertexShader || !fragmentShader) {
    return null;
  }
  
  const shaderProgram = gl.createProgram();
  gl.attachShader(shaderProgram, vertexShader);
  gl.attachShader(shaderProgram, fragmentShader);
  gl.linkProgram(shaderProgram);
  
  // 检查着色器程序是否链接成功
  if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
    console.error('着色器程序链接错误:', gl.getProgramInfoLog(shaderProgram));
    return null;
  }
  
  // 创建着色器程序信息对象
  const programInfo = {
    program: shaderProgram,
    attribLocations: {
      vertexPosition: gl.getAttribLocation(shaderProgram, 'aVertexPosition'),
      textureCoord: gl.getAttribLocation(shaderProgram, 'aTextureCoord'),
    },
    uniformLocations: {
      sampler: gl.getUniformLocation(shaderProgram, 'uSampler'),
      prevFrameSampler: gl.getUniformLocation(shaderProgram, 'uPrevFrameSampler'),
      resolutionFactor: gl.getUniformLocation(shaderProgram, 'uResolutionFactor'),
      antiAliasFactor: gl.getUniformLocation(shaderProgram, 'uAntiAliasFactor'),
      brightness: gl.getUniformLocation(shaderProgram, 'uBrightness'),
      contrast: gl.getUniformLocation(shaderProgram, 'uContrast'),
      textureSize: gl.getUniformLocation(shaderProgram, 'uTextureSize'),
      enableColor: gl.getUniformLocation(shaderProgram, 'uEnableColor'),
      enableNight: gl.getUniformLocation(shaderProgram, 'uEnableNight'),
      enableBrightLight: gl.getUniformLocation(shaderProgram, 'uEnableBrightLight'),
      enableMotion: gl.getUniformLocation(shaderProgram, 'uEnableMotion'),
      motionSensitivity: gl.getUniformLocation(shaderProgram, 'uMotionSensitivity'),
      motionThreshold: gl.getUniformLocation(shaderProgram, 'uMotionThreshold'),
      motionSizeThreshold: gl.getUniformLocation(shaderProgram, 'uMotionSizeThreshold'),
      isCat: gl.getUniformLocation(shaderProgram, 'uIsCat'),
      // 强光适应相关的uniform变量位置
      brightnessReduction: gl.getUniformLocation(shaderProgram, 'uBrightnessReduction'),
      contrastIncrease: gl.getUniformLocation(shaderProgram, 'uContrastIncrease'),
      vignetteStrength: gl.getUniformLocation(shaderProgram, 'uVignetteStrength'),
      highlightThreshold: gl.getUniformLocation(shaderProgram, 'uHighlightThreshold'),
      // 添加白天暗化效果相关的uniform变量
      enableDayDarkening: gl.getUniformLocation(shaderProgram, 'uEnableDayDarkening'),
      dayDarkeningFactor: gl.getUniformLocation(shaderProgram, 'uDayDarkeningFactor'),
      // 新增的夜视增强uniform变量
      adaptiveContrast: gl.getUniformLocation(shaderProgram, 'uAdaptiveContrast'),
      noiseReduction: gl.getUniformLocation(shaderProgram, 'uNoiseReduction'),
      edgeSharpening: gl.getUniformLocation(shaderProgram, 'uEdgeSharpening'),
      enableEnhancedNight: gl.getUniformLocation(shaderProgram, 'uEnableEnhancedNight'),
    },
  };
  
  return programInfo;
}

// 初始化缓冲区
function initBuffers(gl) {
  // 创建顶点位置缓冲区
  const positionBuffer = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
  
  // 定义一个覆盖整个画布的矩形
  const positions = [
    -1.0, -1.0,
     1.0, -1.0,
    -1.0,  1.0,
     1.0,  1.0,
  ];
  
  gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);
  
  // 创建纹理坐标缓冲区
  const textureCoordBuffer = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, textureCoordBuffer);
  
  // 定义纹理坐标
  const textureCoordinates = [
    0.0, 1.0,
    1.0, 1.0,
    0.0, 0.0,
    1.0, 0.0,
  ];
  
  gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(textureCoordinates), gl.STATIC_DRAW);
  
  return {
    position: positionBuffer,
    textureCoord: textureCoordBuffer,
  };
}

// 初始化WebGL
function initWebGL(canvasId) {
  return new Promise((resolve, reject) => {
    // 使用适当的延时确保页面已经渲染
    setTimeout(() => {
      try {
        // 处理选择器，去除可能的#前缀
        const selector = canvasId.startsWith('#') ? canvasId.substring(1) : canvasId;
        console.log('尝试获取Canvas节点:', selector);
        
        // 获取WebGL上下文
        const query = wx.createSelectorQuery();
        query.select('#' + selector)
          .fields({ node: true, size: true })
          .exec((res) => {
            if (!res || !res[0] || !res[0].node) {
              console.error('获取Canvas节点失败', res, '选择器:', '#' + selector);
              reject(new Error('获取Canvas节点失败'));
              return;
            }
            
            const canvasNode = res[0].node;
            const gl = canvasNode.getContext('webgl');
            
            if (!gl) {
              console.error('获取WebGL上下文失败');
              reject(new Error('获取WebGL上下文失败'));
              return;
            }
          
            // 设置画布尺寸为实际尺寸
            const width = res[0].width;
            const height = res[0].height;
            canvasNode.width = width;
            canvasNode.height = height;
            gl.viewport(0, 0, width, height);
            
            // 初始化着色器程序
            const programInfo = initShaders(gl);
            if (!programInfo) {
              reject(new Error('初始化着色器失败'));
              return;
            }
          
            // 初始化缓冲区
            const buffers = initBuffers(gl);
            
            // 创建纹理
            const texture = gl.createTexture();
            gl.bindTexture(gl.TEXTURE_2D, texture);
            
            // 设置纹理参数
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
            
            // 使用空白像素初始化纹理
            gl.texImage2D(
              gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE,
              new Uint8Array([0, 0, 0, 255])
            );
            
            // 创建并初始化前一帧纹理
            const prevFrameTexture = gl.createTexture();
            gl.bindTexture(gl.TEXTURE_2D, prevFrameTexture);
            gl.texImage2D(
              gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE,
              new Uint8Array([0, 0, 0, 255])
            );
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
            
            // 创建WebGL上下文对象
            const webglContext = {
              gl,
              programInfo,
              buffers,
              texture,
              prevFrameTexture,
              width,
              height,
              firstFrame: true, // 标记是否为第一帧，用于运动检测的初始化
            };
            
            resolve(webglContext);
          });
      } catch (error) {
        console.error('初始化WebGL时发生错误:', error);
        reject(error);
      }
    }, 100); // 添加100ms延迟确保页面已渲染
  });
}

// 渲染帧
function renderFrame(webglContext, frame, config = {}, features = {}, cameraPosition = 'back') {
  const { gl, programInfo, buffers, texture, prevFrameTexture } = webglContext;
  
  try {
    // 绑定纹理
    gl.bindTexture(gl.TEXTURE_2D, texture);
    
    // 微信小程序中相机帧的处理方式
    if (frame && frame.data && frame.width && frame.height) {
      // 使用正确的参数调用texImage2D
      gl.texImage2D(
        gl.TEXTURE_2D, 0, gl.RGBA, 
        frame.width, frame.height, 0,
        gl.RGBA, gl.UNSIGNED_BYTE, 
        new Uint8Array(frame.data)
      );
    } else {
      console.error('帧数据格式不正确:', frame);
      return; // 如果帧数据格式不正确，直接返回
    }
    
    // 清除画布
    gl.clearColor(0.0, 0.0, 0.0, 1.0);
    gl.clear(gl.COLOR_BUFFER_BIT);
    
    // 使用着色器程序
    gl.useProgram(programInfo.program);
    
    // 设置顶点位置属性
    gl.bindBuffer(gl.ARRAY_BUFFER, buffers.position);
    gl.vertexAttribPointer(
      programInfo.attribLocations.vertexPosition,
      2, gl.FLOAT, false, 0, 0
    );
    gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);
    
    // 设置纹理坐标属性
    gl.bindBuffer(gl.ARRAY_BUFFER, buffers.textureCoord);
    gl.vertexAttribPointer(
      programInfo.attribLocations.textureCoord,
      2, gl.FLOAT, false, 0, 0
    );
    gl.enableVertexAttribArray(programInfo.attribLocations.textureCoord);
    
    // 设置当前帧的纹理单元
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.uniform1i(programInfo.uniformLocations.sampler, 0);
    
    // 设置前一帧的纹理单元
    gl.activeTexture(gl.TEXTURE1);
    gl.bindTexture(gl.TEXTURE_2D, prevFrameTexture);
    gl.uniform1i(programInfo.uniformLocations.prevFrameSampler, 1);
    
    // 设置纹理尺寸 - 使用帧的宽高
    gl.uniform2f(programInfo.uniformLocations.textureSize, frame.width || 1280, frame.height || 720);
    
    // 设置视觉参数 - 使用配置模块中的参数
    gl.uniform1f(
      programInfo.uniformLocations.resolutionFactor, 
      config.visualAcuity ? config.visualAcuity.RESOLUTION_FACTOR : visionConfig.VISUAL_ACUITY_CONFIG.RESOLUTION_FACTOR
    );
    gl.uniform1f(
      programInfo.uniformLocations.antiAliasFactor, 
      config.visualAcuity ? config.visualAcuity.VIEW_FIELD_FACTOR : visionConfig.VISUAL_ACUITY_CONFIG.VIEW_FIELD_FACTOR
    );
    // 根据动物类型设置夜视参数
    const animalType = config.animalType || 'dog';
    let brightness, contrast;
    
    if (animalType === 'cat') {
      // 猫科动物夜视参数
      brightness = config.nightVision && config.nightVision.BRIGHTNESS ? 
        config.nightVision.BRIGHTNESS : 
        visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS;
      
      contrast = config.nightVision && config.nightVision.CONTRAST ? 
        config.nightVision.CONTRAST : 
        visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST;
    } else {
      // 犬科动物夜视参数
      brightness = config.nightVision && config.nightVision.BRIGHTNESS ? 
        config.nightVision.BRIGHTNESS : 
        visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS;
      
      contrast = config.nightVision && config.nightVision.CONTRAST ? 
        config.nightVision.CONTRAST : 
        visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST;
    }
    
    gl.uniform1f(programInfo.uniformLocations.brightness, brightness);
    gl.uniform1f(programInfo.uniformLocations.contrast, contrast);
    
    // 设置功能开关
    gl.uniform1i(
      programInfo.uniformLocations.enableColor,
      config.dichromaticVision ? (config.dichromaticVision.ENABLED ? 1 : 0) : visionConfig.DICHROMATIC_VISION_CONFIG.ENABLED ? 1 : 0
    );
    gl.uniform1f(
      programInfo.uniformLocations.isCat,
      config.animalType === 'cat' ? 1.0 : 0.0
    );
    
    // 夜视功能在低光环境下启用
    const enableNight = 
      (config.nightVision ? config.nightVision.ENABLED : visionConfig.NIGHT_VISION_CONFIG.ENABLED) && 
      config.isLowLight ? 1 : 0;
    gl.uniform1i(programInfo.uniformLocations.enableNight, enableNight);
    
    // 强光适应功能在强光环境下启用
    const enableBrightLight = 
      (config.brightLightAdaptation ? config.brightLightAdaptation.ENABLED : visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.ENABLED) && 
      (features.isBrightLight || config.isBrightLight) ? 1 : 0;
    gl.uniform1i(programInfo.uniformLocations.enableBrightLight, enableBrightLight);
    
    // 运动视觉功能设置
    const enableMotion = features.motion ? 1 : 0;
    gl.uniform1i(programInfo.uniformLocations.enableMotion, enableMotion);
    
    // 设置运动视觉参数 - 大幅度增强参数对效果的影响
    let motionSensitivity, motionThreshold, motionSizeThreshold;
    
    if (animalType === 'cat') {
      // 猫科运动视觉参数 - 运动敏感度更高，阈值更低
      motionSensitivity = config.motionVision && config.motionVision.CAT && config.motionVision.CAT.MOTION_SENSITIVITY ? 
        config.motionVision.CAT.MOTION_SENSITIVITY : 6.0;
      
      motionThreshold = config.motionVision && config.motionVision.CAT && config.motionVision.CAT.MOTION_THRESHOLD ? 
        config.motionVision.CAT.MOTION_THRESHOLD : 20.0;
        
      motionSizeThreshold = config.motionVision && config.motionVision.CAT && config.motionVision.CAT.MOTION_SIZE_THRESHOLD ? 
        config.motionVision.CAT.MOTION_SIZE_THRESHOLD : 3.0; // 猫科默认大小阈值
    } else {
      // 犬科运动视觉参数
      motionSensitivity = config.motionVision && config.motionVision.DOG && config.motionVision.DOG.MOTION_SENSITIVITY ? 
        config.motionVision.DOG.MOTION_SENSITIVITY : 5.0;
      
      motionThreshold = config.motionVision && config.motionVision.DOG && config.motionVision.DOG.MOTION_THRESHOLD ? 
        config.motionVision.DOG.MOTION_THRESHOLD : 25.0;
        
      motionSizeThreshold = config.motionVision && config.motionVision.DOG && config.motionVision.DOG.MOTION_SIZE_THRESHOLD ? 
        config.motionVision.DOG.MOTION_SIZE_THRESHOLD : 4.0; // 犬科默认大小阈值
    }
    
    // 使用简化的参数映射，更直接反映用户设置的参数效果
    // 1. 运动敏感度: 线性映射，不再使用幂函数
    motionSensitivity = motionSensitivity / 2.0; // 简单的线性缩放，降低敏感度范围
    
    // 2. 运动阈值: 线性映射，不再使用反向计算
    motionThreshold = motionThreshold / 30.0; // 简单归一化到0-1范围
    if (motionThreshold < 0.1) motionThreshold = 0.1; // 限制最小值
    if (motionThreshold > 0.9) motionThreshold = 0.9; // 限制最大值
    
    // 3. 物体大小阈值: 线性映射
    motionSizeThreshold = motionSizeThreshold / 30.0; // 简单归一化到0-1范围
    if (motionSizeThreshold < 0.1) motionSizeThreshold = 0.1; // 限制最小值
    if (motionSizeThreshold > 0.9) motionSizeThreshold = 0.9; // 限制最大值
    
    // 只在调试模式下输出日志
    if (false) { // 设置为false关闭调试日志
      console.log('运动视觉参数处理后的值:', {
        motionSensitivity,
        motionThreshold,
        motionSizeThreshold
      });
    }
    
    gl.uniform1f(programInfo.uniformLocations.motionSensitivity, motionSensitivity);
    gl.uniform1f(programInfo.uniformLocations.motionThreshold, motionThreshold);
    gl.uniform1f(programInfo.uniformLocations.motionSizeThreshold, motionSizeThreshold);
    
    // 设置强光适应参数
    let brightnessReduction, contrastIncrease, vignetteStrength, highlightThreshold;
    
    if (animalType === 'cat') {
      // 猫科动物强光适应参数
      brightnessReduction = config.brightLightAdaptation && config.brightLightAdaptation.CAT ? 
        config.brightLightAdaptation.CAT.BRIGHTNESS_REDUCTION : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.BRIGHTNESS_REDUCTION;
      
      contrastIncrease = config.brightLightAdaptation && config.brightLightAdaptation.CAT ? 
        config.brightLightAdaptation.CAT.CONTRAST_INCREASE : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.CONTRAST_INCREASE;
      
      vignetteStrength = config.brightLightAdaptation && config.brightLightAdaptation.CAT ? 
        config.brightLightAdaptation.CAT.VIGNETTE_STRENGTH : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.VIGNETTE_STRENGTH;
      
      highlightThreshold = config.brightLightAdaptation && config.brightLightAdaptation.CAT ? 
        config.brightLightAdaptation.CAT.HIGHLIGHT_THRESHOLD : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.CAT.HIGHLIGHT_THRESHOLD;
    } else {
      // 犬科动物强光适应参数
      brightnessReduction = config.brightLightAdaptation && config.brightLightAdaptation.DOG ? 
        config.brightLightAdaptation.DOG.BRIGHTNESS_REDUCTION : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.BRIGHTNESS_REDUCTION;
      
      contrastIncrease = config.brightLightAdaptation && config.brightLightAdaptation.DOG ? 
        config.brightLightAdaptation.DOG.CONTRAST_INCREASE : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.CONTRAST_INCREASE;
      
      vignetteStrength = config.brightLightAdaptation && config.brightLightAdaptation.DOG ? 
        config.brightLightAdaptation.DOG.VIGNETTE_STRENGTH : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.VIGNETTE_STRENGTH;
      
      highlightThreshold = config.brightLightAdaptation && config.brightLightAdaptation.DOG ? 
        config.brightLightAdaptation.DOG.HIGHLIGHT_THRESHOLD : 
        visionConfig.BRIGHT_LIGHT_ADAPTATION_CONFIG.DOG.HIGHLIGHT_THRESHOLD;
    }
    
    // 设置白天暗化效果参数
    let enableDayDarkening = 0;
    let dayDarkeningFactor = 0.85; // 默认暗化因子
    
    if (config.dayVision && config.dayVision.ENABLED) {
      enableDayDarkening = 1;
      dayDarkeningFactor = config.dayVision.DARKENING_FACTOR || 0.85;
    }
    
    gl.uniform1f(programInfo.uniformLocations.brightnessReduction, brightnessReduction);
    gl.uniform1f(programInfo.uniformLocations.contrastIncrease, contrastIncrease);
    gl.uniform1f(programInfo.uniformLocations.vignetteStrength, vignetteStrength);
    gl.uniform1f(programInfo.uniformLocations.highlightThreshold, highlightThreshold);
    
    // 设置白天暗化效果的uniform变量
    gl.uniform1i(programInfo.uniformLocations.enableDayDarkening, enableDayDarkening);
    gl.uniform1f(programInfo.uniformLocations.dayDarkeningFactor, dayDarkeningFactor);
    
    // 设置夜视增强参数
    const enableEnhancedNight = config.enhancementParams ? 1 : 0;
    gl.uniform1i(programInfo.uniformLocations.enableEnhancedNight, enableEnhancedNight);
    
    if (config.enhancementParams) {
      // 使用增强参数
      gl.uniform1f(programInfo.uniformLocations.adaptiveContrast, config.enhancementParams.adaptiveContrast || 1.0);
      gl.uniform1f(programInfo.uniformLocations.noiseReduction, config.enhancementParams.noiseReduction || 0.0);
      gl.uniform1f(programInfo.uniformLocations.edgeSharpening, config.enhancementParams.edgeSharpening || 0.0);
    } else {
      // 使用默认值
      gl.uniform1f(programInfo.uniformLocations.adaptiveContrast, 1.0);
      gl.uniform1f(programInfo.uniformLocations.noiseReduction, 0.0);
      gl.uniform1f(programInfo.uniformLocations.edgeSharpening, 0.0);
    }
    
    // 绘制
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
    
    // 更新前一帧纹理
    if (!webglContext.firstFrame) {
      gl.bindTexture(gl.TEXTURE_2D, prevFrameTexture);
      gl.texImage2D(
        gl.TEXTURE_2D, 0, gl.RGBA, 
        frame.width, frame.height, 0,
        gl.RGBA, gl.UNSIGNED_BYTE, 
        new Uint8Array(frame.data)
      );
    } else {
      webglContext.firstFrame = false;
    }
  } catch (error) {
    console.error('渲染帧错误:', error);
  }
}

// 释放WebGL资源
function releaseWebGLResources(webglContext) {
  if (!webglContext || !webglContext.gl) return;
  
  const { gl, programInfo, buffers, texture, prevFrameTexture } = webglContext;
  
  // 删除缓冲区
  if (buffers) {
    if (buffers.position) gl.deleteBuffer(buffers.position);
    if (buffers.textureCoord) gl.deleteBuffer(buffers.textureCoord);
  }
  
  // 删除纹理
  if (texture) gl.deleteTexture(texture);
  if (prevFrameTexture) gl.deleteTexture(prevFrameTexture);
  
  // 删除着色器程序
  if (programInfo && programInfo.program) {
    gl.deleteProgram(programInfo.program);
  }
}



module.exports = {
  initWebGL,
  renderFrame,
  releaseWebGLResources
};
