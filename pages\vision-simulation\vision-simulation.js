// 获取应用实例
const app = getApp();

// 导入核心模块（仍需要的）
const webglRenderer = require('./utils/webgl-renderer');
const visionManager = require('./utils/vision-manager');
const visionConfig = require('./utils/vision-config');
const tipsManager = require('./utils/tips-manager');
const camManager = require('./utils/cam-manager');

// 导入新的管理器模块
const LifecycleManager = require('./utils/lifecycle-manager');
const CameraController = require('./utils/camera-controller');
const VisionModeManager = require('./utils/vision-mode-manager');
const InteractionHandler = require('./utils/interaction-handler');
const StateManager = require('./utils/state-manager');
const BreedConfigurator = require('./utils/breed-configurator');

// 全局变量，用于存储页面实例和WebGL上下文
let page = null;
let webglContext = null;
let frameCount = 0;
let visionContext = null; // 视觉管理器上下文

Page({
  data: {}, // 数据将由StateManager管理

  // 初始化管理器实例
  lifecycleManager: null,
  cameraController: null,
  visionModeManager: null,
  interactionHandler: null,
  stateManager: null,
  breedConfigurator: null,

  onLoad: function (options) {
    console.log('页面加载，开始初始化管理器');
    
    // 保存页面实例到全局变量
    page = this;
    
    // 初始化所有管理器
    this.initializeManagers();
    
    // 设置初始数据
    this.setData(this.stateManager.getInitialData());
    
    // 调用生命周期管理器的onLoad
    this.lifecycleManager.onLoad(options);
  },

  onReady: function() {
    console.log('页面就绪');
    this.lifecycleManager.onReady();
  },

  onShow: function() {
    console.log('页面显示');
    this.lifecycleManager.onShow();
  },

  onHide: function() {
    console.log('页面隐藏');
    this.lifecycleManager.onHide();
  },

  onUnload: function() {
    console.log('页面卸载');
    this.lifecycleManager.onUnload();
    
    // 清理所有管理器
    this.cleanupManagers();
    
    // 清除页面实例引用
    page = null;
  },

  /**
   * 初始化所有管理器
   */
  initializeManagers: function() {
    // 创建管理器实例
    this.lifecycleManager = new LifecycleManager();
    this.cameraController = new CameraController();
    this.visionModeManager = new VisionModeManager();
    this.interactionHandler = new InteractionHandler();
    this.stateManager = new StateManager();
    this.breedConfigurator = new BreedConfigurator();
    
    // 初始化所有管理器
    this.lifecycleManager.init(this);
    this.cameraController.init(this);
    this.visionModeManager.init(this);
    this.interactionHandler.init(this);
    this.stateManager.init(this);
    this.breedConfigurator.init(this);
    
    console.log('所有管理器初始化完成');
  },

  /**
   * 清理所有管理器
   */
  cleanupManagers: function() {
    if (this.cameraController) {
      this.cameraController.cleanup();
    }
    
    // 重置管理器引用
    this.lifecycleManager = null;
    this.cameraController = null;
    this.visionModeManager = null;
    this.interactionHandler = null;
    this.stateManager = null;
    this.breedConfigurator = null;
    
    console.log('所有管理器已清理');
  },

  // ==================== 品种配置相关方法 ====================
  
  /**
   * 根据动物种类和品种设置视力清晰度因子
   */
  setBreedVisualAcuity: function(breedName, animalType) {
    return this.breedConfigurator.setBreedVisualAcuity(breedName, animalType);
  },

  /**
   * 设置暹罗猫的特殊夜视能力参数
   */
  setSiameseNightVision: function() {
    this.breedConfigurator.setSiameseNightVision();
  },

  /**
   * 设置哈士奇的夜视能力参数
   */
  setHuskyNightVision: function() {
    this.breedConfigurator.setHuskyNightVision();
  },

  // ==================== 相机控制相关方法 ====================
  
  /**
   * 初始化相机类型状态
   */
  initializeCameraType: function() {
    this.cameraController.initializeCameraType();
  },

  /**
   * 初始化相机
   */
  initCamera: function() {
    this.cameraController.initCamera();
  },

  /**
   * 相机错误处理
   */
  handleCameraError: function(error) {
    this.cameraController.handleCameraError(error);
  },

  /**
   * 打开相机设置
   */
  openCameraSetting: function() {
    this.cameraController.openCameraSetting();
  },

  /**
   * 重试初始化相机
   */
  retryCameraInit: function() {
    this.cameraController.retryCameraInit();
  },

  /**
   * 重置相机状态
   */
  resetCameraState: function() {
    this.cameraController.resetCameraState();
  },

  /**
   * 切换到指定相机
   */
  switchToCamera: function(cameraType) {
    this.cameraController.switchToCamera(cameraType);
  },

  /**
   * 切换到手机相机
   */
  switchToPhoneCamera: function(position) {
    this.cameraController.switchToPhoneCamera(position);
  },

  /**
   * 切换到硬件相机
   */
  switchToHardwareCamera: function() {
    this.cameraController.switchToHardwareCamera();
  },

  /**
   * 启用CAM模式
   */
  enableCamMode: function() {
    this.cameraController.enableCamMode();
  },

  /**
   * 禁用CAM模式
   */
  disableCamMode: function() {
    this.cameraController.disableCamMode();
  },

  /**
   * 重试CAM连接
   */
  retryCamConnection: function() {
    this.cameraController.retryCamConnection();
  },

  /**
   * 处理CAM图片加载错误
   */
  handleCamImageError: function(e) {
    this.cameraController.handleCamImageError(e);
  },

  /**
   * 处理CAM图片加载完成
   */
  handleCamImageLoad: function(e) {
    this.cameraController.handleCamImageLoad(e);
  },

  // ==================== 视觉模式相关方法 ====================
  
  /**
   * 显示/隐藏视觉模式选择器
   */
  toggleVisionModeSelector: function() {
    const newState = !this.data.showVisionModeSelector;

    this.setData({
      showVisionModeSelector: newState,
      showVisionModeDetail: newState, // 同步显示模式详情
      // 隐藏视觉选择引导提示
      showVisionSelectGuide: false
    });

    // 添加触觉反馈
    if (newState && wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  /**
   * 选择视觉模式
   */
  selectVisionMode: function(e) {
    const mode = e.currentTarget.dataset.mode;

    // 如果点击当前模式，仅关闭选择器
    if (mode === this.data.currentVisionMode) {
      this.toggleVisionModeSelector();
      return;
    }

    // 添加触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({ type: 'medium' });
    }

    // 根据选择的模式设置相应的特性
    let features = { ...this.data.features };

    // 根据视觉模式的层级关系设置特性
    switch(mode) {
      case 'dichromatic':
        features.color = true;
        features.night = false;
        features.motion = false;
        break;
      case 'acuity':
        features.color = true;
        features.night = false;
        features.motion = false;
        break;
      case 'nightVision':
        features.color = true;
        features.night = true;
        features.motion = false;
        break;
      case 'motionVision':
        features.color = true;
        features.night = true;
        features.motion = true;
        break;
    }

    // 更新当前模式和特性
    this.setData({
      currentVisionMode: mode,
      features: features,
      showVisionModeSelector: false
    });

    // 如果有最后一帧，重新渲染
    if (this._lastFrame && this.webglContext) {
      this.processFrameWebGL(this._lastFrame);
    }

    // 显示模式切换提示
    const modeNames = {
      'dichromatic': '二色视觉',
      'acuity': '视力辨析',
      'nightVision': '光感模式',
      'motionVision': '运动视觉'
    };

    wx.showToast({
      title: `已切换到${modeNames[mode]}`,
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 切换运动视觉功能
   */
  toggleMotionVision: function() {
    this.visionModeManager.toggleMotionVision();
  },

  /**
   * 显示/隐藏视觉参数调整面板
   */
  toggleVisionParams: function() {
    // 在显示参数面板前，确保同步最新的品种特定参数
    if (!this.data.showVisionParams) {
      // 即将显示参数面板，同步品种特定参数到UI
      const breedName = this.data.breedName;
      if (breedName) {
        let resolutionFactor = this.data.originalDogVisionParams.resolutionFactor;

        // 根据品种设置视力清晰度
        if (this.data.features.isCat) {
          // 猫科动物处理逻辑
          if (breedName === '暹罗') {
            resolutionFactor = 0.15; // 暹罗猫特殊视力清晰度因子
          } else {
            resolutionFactor = 0.2; // 猫科视力清晰度因子
          }
        } else {
          // 犬科动物处理逻辑
          if (breedName.includes('拉布拉多')) {
            resolutionFactor = 1.0;
          } else if (breedName.includes('金毛')) {
            resolutionFactor = 0.33;
          } else if (breedName.includes('边境') || breedName.includes('牧羊')) {
            resolutionFactor = 0.4;
          } else if (breedName.includes('贵宾') || breedName.includes('柯基') ||
                    breedName.includes('比熊') || breedName.includes('哈士奇')) {
            resolutionFactor = 0.27;
          } else if (breedName.includes('吉娃娃') || breedName.includes('法斗') ||
                    breedName.includes('法国')) {
            resolutionFactor = 0.25;
          }
        }

        // 确保UI显示正确的参数值
        this.setData({
          'dogVisionParams.resolutionFactor': resolutionFactor
        });

        console.log('参数面板显示前同步品种[' + breedName + ']的视力清晰度参数:', resolutionFactor);
      }
    }

    // 切换参数面板的显示状态
    this.setData({
      showVisionParams: !this.data.showVisionParams
    });
  },

  /**
   * 显示/隐藏分辨率选择器
   */
  toggleResolutionSelector: function() {
    this.visionModeManager.toggleResolutionSelector();
  },

  /**
   * 切换分辨率
   */
  changeResolution: function(e) {
    const index = e.currentTarget.dataset.index;
    this.visionModeManager.changeResolution(index);
  },

  /**
   * 获取模式名称
   */
  getModeName: function(mode) {
    return this.visionModeManager.getModeName(mode);
  },

  // ==================== 用户交互相关方法 ====================
  
  /**
   * 处理触摸开始事件
   */
  handleTouchStart: function(e) {
    this.interactionHandler.handleTouchStart(e);
  },

  /**
   * 处理触摸结束事件
   */
  handleTouchEnd: function(e) {
    this.interactionHandler.handleTouchEnd(e);
  },

  /**
   * 处理相机区域点击事件
   */
  handleViewAreaTap: function() {
    this.interactionHandler.handleViewAreaTap();
  },

  /**
   * 切换视角
   */
  toggleView: function() {
    const newView = this.data.currentView === 'dog' ? 'human' : 'dog';
    this.setData({
      currentView: newView
    });

    // 添加触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }

    console.log('切换视角到:', newView);
  },

  /**
   * 切换控制面板显示/隐藏状态
   */
  toggleControlPanel: function() {
    this.interactionHandler.toggleControlPanel();
  },

  /**
   * 切换分析区域展开/收起状态
   */
  toggleAnalysisExpand: function() {
    const newState = !this.data.isAnalysisExpanded;
    this.setData({
      isAnalysisExpanded: newState
    });

    console.log('切换分析区域状态:', newState ? '展开' : '收起');
  },

  /**
   * 显示/隐藏相机选择器
   */
  toggleCameraSelector: function() {
    this.setData({
      showCameraSelector: !this.data.showCameraSelector
    });
  },

  /**
   * 选择相机类型
   */
  selectCameraType: function(e) {
    const cameraType = e.currentTarget.dataset.type;

    // 如果选择的是当前相机类型，仅关闭选择器
    if (cameraType === this.data.currentCameraType) {
      this.toggleCameraSelector();
      return;
    }

    console.log('切换相机类型:', this.data.currentCameraType, '->', cameraType);

    // 更新当前相机类型
    this.setData({
      currentCameraType: cameraType,
      showCameraSelector: false
    });

    // 根据选择的相机类型执行相应操作
    this.cameraController.switchToCamera(cameraType);

    // 添加触觉反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  /**
   * 防止触摸事件穿透
   */
  preventTouchMove: function(e) {
    return this.interactionHandler.preventTouchMove(e);
  },

  /**
   * 防止冒泡
   */
  preventBubble: function(e) {
    return this.interactionHandler.preventBubble(e);
  },

  /**
   * 标签页切换 - 点击切换
   */
  changeTab: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      currentTab: index
    });
  },

  /**
   * 标签页切换 - 滑动切换
   */
  swiperChange: function(e) {
    this.setData({
      currentTab: e.detail.current
    });
  },

  /**
   * 清晰度因子滑块变化处理
   */
  onResolutionChange: function(e) {
    const resolutionFactor = e.detail.value;

    // 更新UI显示的参数值
    this.setData({
      'dogVisionParams.resolutionFactor': resolutionFactor
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: resolutionFactor
      });

      // 如果有最后一帧，重新渲染
      if (this._lastFrame && this.webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
  },

  /**
   * 视野因子滑块变化处理
   */
  onAntiAliasChange: function(e) {
    const viewFieldFactor = e.detail.value;

    // 更新UI显示的参数值
    this.setData({
      'dogVisionParams.antiAliasFactor': viewFieldFactor
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateVisualAcuityConfig({
        VIEW_FIELD_FACTOR: viewFieldFactor
      });

      // 如果有最后一帧，重新渲染
      if (this._lastFrame && this.webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
  },

  /**
   * 亮度增强滑块变化处理
   */
  onBrightnessChange: function(e) {
    const value = e.detail.value;
    const brightness = 1 + value / 50;

    // 更新UI显示的参数值
    this.setData({
      'dogVisionParams.brightness': brightness
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        BRIGHTNESS: brightness
      });

      // 如果有最后一帧，重新渲染
      if (this._lastFrame && this.webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
  },

  /**
   * 对比度滑块变化处理
   */
  onContrastChange: function(e) {
    const value = e.detail.value;
    const contrast = 1 + value / 50;

    // 更新UI显示的参数值
    this.setData({
      'dogVisionParams.contrast': contrast
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateNightVisionConfig({
        CONTRAST: contrast
      });

      // 如果有最后一帧，重新渲染
      if (this._lastFrame && this.webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }
  },

  /**
   * 运动敏感度滑块变化处理
   */
  onMotionSensitivityChange: function(e) {
    this.interactionHandler.onMotionSensitivityChange(e);
  },

  /**
   * 运动阈值滑块变化处理
   */
  onMotionThresholdChange: function(e) {
    this.interactionHandler.onMotionThresholdChange(e);
  },

  /**
   * 运动物体大小阈值滑块变化处理
   */
  onMotionSizeThresholdChange: function(e) {
    this.interactionHandler.onMotionSizeThresholdChange(e);
  },

  /**
   * 保存爱宠视角图片
   */
  savePetVisionImage: function() {
    this.interactionHandler.savePetVisionImage();
  },

  /**
   * 运动预设循环切换函数
   */
  nextMotionPreset: function() {
    this.interactionHandler.nextMotionPreset();
  },

  /**
   * 运动视觉风格切换函数
   */
  toggleMotionStyle: function() {
    this.interactionHandler.toggleMotionStyle();
  },

  /**
   * 返回上一页
   */
  navigateBack: function() {
    this.interactionHandler.navigateBack();
  },

  /**
   * 检查是否需要显示视觉功能层级引导
   */
  checkShowVisionGuide: function() {
    this.interactionHandler.checkShowVisionGuide();
  },

  /**
   * 关闭视觉功能层级引导
   */
  closeVisionGuide: function() {
    this.interactionHandler.closeVisionGuide();
  },

  /**
   * 切换"不再显示"选项
   */
  toggleRememberGuide: function() {
    this.interactionHandler.toggleRememberGuide();
  },

  /**
   * 显示运动视觉引导提示
   */
  showMotionVisionGuideTip: function() {
    this.interactionHandler.showMotionVisionGuideTip();
  },

  // ==================== 核心业务逻辑方法 ====================

  /**
   * 初始化WebGL和视觉管理器
   */
  initWebGL: async function() {
    try {
      // 获取动物类型
      const animalType = this.data.features.isCat ? 'cat' : 'dog';

      // 根据动物类型和品种设置视力清晰度因子
      if (this.data.breedName) {
        // 调用设置视力清晰度因子函数，传入品种和动物类型
        this.setBreedVisualAcuity(this.data.breedName, animalType);

        // 如果是犬科动物且是哈士奇，单独设置夜视能力参数
        if (animalType === 'dog' && this.data.breedName.includes('哈士奇')) {
          this.setHuskyNightVision();
        }
      } else {
        // 没有品种信息时，仍然调用设置视力清晰度函数，传入动物类型
        this.setBreedVisualAcuity('', animalType);
      }

      // 根据动物类型设置夜视参数
      let nightVisionConfig;

      if (animalType === 'cat') {
        // 猫科动物夜视参数
        nightVisionConfig = {
          CAT: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST
          },
          DOG: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST
          },
          LOW_LIGHT_THRESHOLD: this.data.LOW_LIGHT_THRESHOLD,
          ENABLED: this.data.features.night
        };
      } else {
        // 犬科动物夜视参数
        nightVisionConfig = {
          CAT: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.CAT.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.CAT.CONTRAST
          },
          DOG: {
            BRIGHTNESS: visionConfig.NIGHT_VISION_CONFIG.DOG.BRIGHTNESS,
            CONTRAST: visionConfig.NIGHT_VISION_CONFIG.DOG.CONTRAST
          },
          LOW_LIGHT_THRESHOLD: this.data.LOW_LIGHT_THRESHOLD,
          ENABLED: this.data.features.night
        };
      }

      // 在初始化前确保动物类型特定的清晰度因子已设置
      let breedSpecificResolutionFactor = this.data.dogVisionParams.resolutionFactor;

      // 初始化视觉管理器 - 使用await等待异步初始化完成
      visionContext = await visionManager.initVisionManager({
        // 初始化配置
        animalType: animalType,
        isLowLight: false,
        // 使用当前的视觉参数，并强制使用犬种特定的清晰度因子
        visualAcuity: {
          RESOLUTION_FACTOR: breedSpecificResolutionFactor, // 使用犬种特定的清晰度因子
          VIEW_FIELD_FACTOR: this.data.dogVisionParams.antiAliasFactor, // 使用新的参数名称
          ENABLED: true
        },
        nightVision: nightVisionConfig,
        dichromaticVision: {
          ENABLED: this.data.features.color
        }
      });

      // 获取WebGL上下文
      webglContext = visionContext.webglContext;

      if (webglContext) {
        console.log('初始化WebGL和视觉管理器成功');
        this.lifecycleManager.webglInitialized = true;

        // 如果页面没有隐藏，初始化相机
        if (!this.lifecycleManager.pageHidden) {
          // 添加延迟，给WebGL初始化完成留出时间
          setTimeout(() => {
            if (!this.lifecycleManager.pageHidden && !this.lifecycleManager.cameraInitialized) {
              console.log('在WebGL初始化成功后延迟初始化相机');
              this.initCamera();
            }
          }, 500);
        } else {
          console.log('页面当前处于隐藏状态，暂不初始化相机');
        }
      } else {
        throw new Error('无法获取WebGL上下文');
      }
    } catch (error) {
      console.error('初始化WebGL失败:', error);
      this.setData({ cameraError: true, cameraErrorMsg: '初始化图像处理失败' });
      wx.showToast({
        title: '初始化图像处理失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 使用WebGL处理帧
   */
  processFrameWebGL: function(frame) {
    try {
      // 如果WebGL上下文未初始化或页面已卸载，则不处理
      if (!webglContext || !page || !visionContext) {
        console.warn('处理帧跳过: WebGL上下文未初始化或页面已卸载');
        return;
      }

      // CAM模式下，图片流处理方式不同，直接返回
      if (this.data.camMode) {
        console.log('CAM模式下不进行WebGL视觉处理');
        return;
      }

      // 更新最后帧处理时间戳，用于检测相机工作状态
      this._lastFrameCheckTime = Date.now();

      // 如果正在切换全屏模式，确保相机不重新加载
      if (this.data.isFullscreen && this.data.cameraLoading) {
        this.setData({
          cameraLoading: false
        });
        console.log('全屏模式下关闭相机加载提示');
        return;
      }

      // 检查WebGL上下文是否有效
      if (!webglContext.gl) {
        console.warn('处理帧跳过: WebGL上下文的gl对象无效');
        return;
      }

      // 保存最后一帧，用于参数变化时重新渲染和保存图片
      if (frame && frame.data) {
        // 深拷贝帧数据以避免引用问题
        this._lastFrame = {
          data: new Uint8Array(frame.data),
          width: frame.width,
          height: frame.height
        };
        // 同时在WebGL上下文中也保存一份，确保保存图片时可用
        if (webglContext) {
          webglContext.rawFrame = this._lastFrame;
        }
      } else {
        console.warn('处理帧跳过: 无效的帧数据');
        return;
      }

      // 更新帧计数器
      frameCount++;

      // 获取当前选择的分辨率设置
      const currentResolution = this.data.resolutionOptions[this.data.currentResolutionIndex];
      const targetWidth = currentResolution.width;
      const targetHeight = currentResolution.height;

      // 更新帧尺寸信息，使用当前选择的分辨率
      if (this.data.frame.width !== targetWidth || this.data.frame.height !== targetHeight) {
        console.log('设置相机分辨率显示为', targetWidth, 'x', targetHeight, '，实际帧尺寸:', frame.width, 'x', frame.height);
        this.setData({
          frame: {
            width: targetWidth,
            height: targetHeight
          }
        });
      }

      // 使用视觉管理器处理帧
      const animalType = this.data.features.isCat ? 'cat' : 'dog';

      // 获取当前运动视觉参数
      const motionParams = {
        enabled: this.data.features.motion, // 传递运动视觉开关状态
        sensitivity: this.data.dogVisionParams.motionSensitivity,
        threshold: this.data.dogVisionParams.motionThreshold,
        sizeThreshold: this.data.dogVisionParams.motionSizeThreshold // 添加物体大小阈值参数
      };

      // 只在运动视觉开启或参数变化时输出调试日志
      if (this._lastMotionParams && (
          this._lastMotionParams.enabled !== motionParams.enabled ||
          this._lastMotionParams.sensitivity !== motionParams.sensitivity ||
          this._lastMotionParams.threshold !== motionParams.threshold ||
          this._lastMotionParams.sizeThreshold !== motionParams.sizeThreshold
        )) {
        console.log('运动视觉参数变化:', {
          '之前': this._lastMotionParams,
          '现在': motionParams
        });
      }

      // 保存当前参数供下次比较
      this._lastMotionParams = { ...motionParams };

      // 根据当前视觉模式强制覆盖夜视和强光效果
      let forceDisableNightVision = false;
      let forceDisableBrightLight = false;

      // 如果当前模式是二色视觉或视力辨析度，强制禁用夜视和强光效果
      if (this.data.currentVisionMode === 'dichromatic' || this.data.currentVisionMode === 'acuity') {
        forceDisableNightVision = true;
        forceDisableBrightLight = true;
      }

      const result = visionManager.processFrame(frame, webglContext, animalType, motionParams, forceDisableNightVision, forceDisableBrightLight);

      // 调试日志，帮助确认强光检测状态
      if (result.brightPixelRatio > 0.3) {
        console.log('强光检测状态:', {
          brightPixelRatio: result.brightPixelRatio,
          isBrightLight: result.isBrightLight,
          forceDisableBrightLight: forceDisableBrightLight
        });
      }

      // 更新亮度、低光状态和强光状态
      if (result.brightness !== this.data.averageBrightness ||
          result.isLowLight !== this.data.isLowLight ||
          result.isBrightLight !== this.data.isBrightLight ||
          result.brightPixelRatio !== this.data.brightPixelRatio) {

        // 先检测光照状态变化，然后再更新数据
        // 检测强光模式变化
        const brightLightChanged = result.isBrightLight !== this.data.isBrightLight;
        const enteringBrightLight = brightLightChanged && result.isBrightLight;

        // 检测夜视模式变化
        const lowLightChanged = result.isLowLight !== this.data.isLowLight;
        const enteringLowLight = lowLightChanged && result.isLowLight;

        // 计算格式化后的高亮像素比例
        const brightPixelRatio = result.brightPixelRatio || 0;
        const brightPixelRatioFormatted = (brightPixelRatio * 100).toFixed(1);

        // 更新数据
        this.setData({
          averageBrightness: result.brightness,
          isLowLight: result.isLowLight,
          isBrightLight: result.isBrightLight,
          brightPixelRatio: brightPixelRatio,
          brightPixelRatioFormatted: brightPixelRatioFormatted
        });

        // 显示相应提示(仅在进入模式时显示一次)
        if (enteringBrightLight) {
          // 进入强光模式，显示强光适应提示
          console.log('检测到进入强光模式，尝试显示强光适应提示');
          // 使用提示管理器确保提示不会重复显示
          tipsManager.showBrightLightTip(this);
        }

        if (enteringLowLight) {
          // 进入夜视模式，显示夜视能力提示
          console.log('检测到进入夜视模式，尝试显示夜视能力提示');
          // 使用提示管理器确保提示不会重复显示
          tipsManager.showNightVisionTip(this);
        }
      }

      // 如果相机加载中，标记为加载完成
      if (this.data.cameraLoading) {
        this.setData({ cameraLoading: false });
      }
    } catch (error) {
      console.error('处理帧错误:', error);
    }
  },

  // ==================== 提示管理相关方法 ====================

  /**
   * 显示强光适应提示
   */
  showBrightLightTip: function() {
    tipsManager.showBrightLightTip(this);
  },

  /**
   * 关闭强光适应提示
   */
  closeBrightLightTip: function() {
    tipsManager.closeBrightLightTip(this);
  },

  /**
   * 显示夜视模式提示
   */
  showNightVisionTip: function() {
    tipsManager.showNightVisionTip(this);
  },

  /**
   * 关闭夜视模式提示
   */
  closeNightVisionTip: function() {
    tipsManager.closeNightVisionTip(this);
  },

  /**
   * 显示运动视觉提示
   */
  showMotionTip: function() {
    tipsManager.showMotionVisionTip(this);
  },

  /**
   * 关闭运动视觉提示
   */
  closeMotionTip: function() {
    tipsManager.closeMotionVisionTip(this);
  },

  // ==================== 主题管理相关方法 ====================

  /**
   * 更新主题
   */
  updateTheme: function() {
    // 从全局应用实例中获取当前主题
    const currentTheme = app.globalData.currentTheme || 'theme1';
    const themeConfig = app.globalData.themeConfig || {};

    // 使用状态管理器更新主题状态
    this.stateManager.updateThemeState(currentTheme, themeConfig);

    console.log('视觉模拟页面更新主题:', currentTheme);
  },

  /**
   * 注册主题变化回调
   */
  registerThemeCallback: function() {
    // 先移除可能存在的旧回调，避免重复
    this.unregisterThemeCallback();

    // 注册新的回调
    if (!app.themeChangeCallbacks) {
      app.themeChangeCallbacks = [];
    }

    app.themeChangeCallbacks.push(this.updateTheme.bind(this));
  },

  /**
   * 移除主题变化回调
   */
  unregisterThemeCallback: function() {
    if (app.themeChangeCallbacks) {
      const index = app.themeChangeCallbacks.findIndex(callback =>
        callback.toString().includes('updateTheme')
      );
      if (index > -1) {
        app.themeChangeCallbacks.splice(index, 1);
      }
    }
  },

  // ==================== CAM管理相关方法 ====================

  /**
   * 初始化CAM管理器
   */
  initCamManager: function() {
    console.log('初始化CAM管理器');

    try {
      // 初始化CAM管理器
      camManager.init(this);
      console.log('CAM管理器初始化成功');
    } catch (error) {
      console.error('CAM管理器初始化失败:', error);
    }
  },

  /**
   * toggleCamMode函数已被新的相机选择器系统取代
   * 保留方法名称以兼容可能的外部调用
   */
  toggleCamMode: function() {
    console.log('toggleCamMode已被新的相机选择器取代，请使用相机选择器进行切换');
    // 打开相机选择器
    this.toggleCameraSelector();
  },

  // ==================== 其他重要方法 ====================

  /**
   * 重新初始化相机
   */
  reinitializeCamera: function() {
    this.cameraController.reinitializeCamera();
  },

  /**
   * 重置视觉参数
   */
  resetVisionParams: function() {
    // 获取当前品种名称
    const breedName = this.data.breedName;

    console.log('重置视觉参数，品种:', breedName);

    // 根据品种重新设置默认参数
    let defaultParams = {
      resolutionFactor: 0.5,
      antiAliasFactor: 0.3,
      brightness: 1.5,
      contrast: 1.3,
      motionSensitivity: 3.0,
      motionThreshold: 10.0,
      motionSizeThreshold: 10.0
    };

    // 根据品种调整默认参数
    if (this.data.features.isCat) {
      // 猫科动物参数
      if (breedName === '暹罗') {
        defaultParams.resolutionFactor = 0.15;
        defaultParams.brightness = 1.5;
        defaultParams.contrast = 1.3;
      } else {
        defaultParams.resolutionFactor = 0.2;
        defaultParams.brightness = 2.0;
        defaultParams.contrast = 1.5;
      }
    } else {
      // 犬科动物参数
      if (breedName && breedName.includes('拉布拉多')) {
        defaultParams.resolutionFactor = 1.0;
      } else if (breedName && breedName.includes('金毛')) {
        defaultParams.resolutionFactor = 0.33;
      } else if (breedName && (breedName.includes('边境') || breedName.includes('牧羊'))) {
        defaultParams.resolutionFactor = 0.4;
      } else if (breedName && (breedName.includes('贵宾') || breedName.includes('柯基') ||
                breedName.includes('比熊') || breedName.includes('哈士奇'))) {
        defaultParams.resolutionFactor = 0.27;
        if (breedName.includes('哈士奇')) {
          defaultParams.brightness = 2.0;
          defaultParams.contrast = 1.5;
        }
      } else if (breedName && (breedName.includes('吉娃娃') || breedName.includes('法斗') ||
                breedName.includes('法国'))) {
        defaultParams.resolutionFactor = 0.25;
      }
    }

    // 更新UI显示的参数和原始参数
    this.setData({
      dogVisionParams: { ...defaultParams },
      originalDogVisionParams: { ...defaultParams }
    });

    // 更新视觉管理器配置
    if (visionManager) {
      visionManager.updateVisualAcuityConfig({
        RESOLUTION_FACTOR: defaultParams.resolutionFactor,
        VIEW_FIELD_FACTOR: defaultParams.antiAliasFactor
      });

      visionManager.updateNightVisionConfig({
        BRIGHTNESS: defaultParams.brightness,
        CONTRAST: defaultParams.contrast
      });

      // 如果有最后一帧，重新渲染
      if (this._lastFrame && this.webglContext) {
        this.processFrameWebGL(this._lastFrame);
      }
    }

    console.log('视觉参数已重置为默认值:', defaultParams);

    wx.showToast({
      title: '参数已重置',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 内部方法：将 Canvas 保存到相册
   */
  _saveCanvasToAlbum: function() {
    // 检查是否有WebGL上下文和帧数据
    if (!webglContext || !webglContext.rawFrame) {
      wx.hideLoading();
      wx.showToast({
        title: '暂无图像数据',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      // 使用WebGL渲染器保存图片
      webglRenderer.saveFrameToAlbum(webglContext, webglContext.rawFrame)
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '图片已保存到相册',
            icon: 'success',
            duration: 2000
          });
        })
        .catch(error => {
          console.error('保存图片失败:', error);
          wx.hideLoading();
          wx.showToast({
            title: '保存失败，请重试',
            icon: 'none',
            duration: 2000
          });
        });
    } catch (error) {
      console.error('保存图片异常:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 分享小程序
   */
  onShareAppMessage: function() {
    return {
      title: '爱宠视觉',
      path: '/pages/index/index',
      imageUrl: '/images/share-image.png',
      success: function() {
        console.log('分享成功');
      },
      fail: function() {
        console.log('分享失败');
      }
    };
  }
});
