<view class="container" style="background: {{themeStyle.background || 'var(--dark-bg)'}};">
  <!-- 导航栏 -->
  <view class="navbar" style="background: {{themeStyle.cardBg || 'var(--card-bg)'}};">
    <view class="navbar-title">
      <!-- 导航栏内容已移除，保留空结构 -->
    </view>
    <!-- 导航栏内容已移除，保留空结构 -->
  </view>
  
  <view class="main-content {{isLightTheme ? 'light-theme-content' : ''}}">
    <!-- 相机视图区域 -->
    <view class="camera-container">
      <!-- 当前视角标题 -->
      <view class="vision-title {{currentView === 'dog' ? 'dog-title' : 'human-title'}} {{isLightTheme ? 'light-theme-title' : ''}}">
        <view class="back-btn" hover-class="back-btn-hover" bindtap="navigateBack">
          <text class="back-arrow">←</text>
        </view>
        <!-- 区分猫科和犬科动物的视角标题 -->
        <text wx:if="{{currentView === 'dog' && features.isCat}}" class="{{isLightTheme ? 'light-theme-text' : ''}}">😺 {{breedName}}视角</text>
        <text wx:if="{{currentView === 'dog' && !features.isCat}}" class="{{isLightTheme ? 'light-theme-text' : ''}}">🐶 {{breedName}}视角</text>
        <text wx:if="{{currentView === 'human'}}" class="{{isLightTheme ? 'light-theme-text' : ''}}">👁️ 人类视角</text>
      </view>
      
      <!-- 视觉显示区域 - 添加手势交互事件 -->
      <view class="view-area" bindtap="handleViewAreaTap" bindtouchstart="handleTouchStart" bindtouchend="handleTouchEnd">
        <!-- 全屏切换按钮已移除 -->
        <!-- 人类视角 -->
        <view class="{{currentView === 'human' ? (features.isCat ? 'cat-human-camera-clip-container' : 'camera-clip-container') : (features.isCat ? 'cat-camera-clip-container' : 'camera-clip-container')}}">
          <!-- 手机摄像头组件 - 仅在非CAM模式下显示 -->
          <camera
            wx:if="{{!camMode}}"
            id="camera"
            class="vision-view {{currentView === 'human' ? 'active' : ''}} {{isFullscreen ? 'fullscreen-camera' : ''}}"
            device-position="{{cameraPosition}}"
            resolution="{{resolutionOptions[currentResolutionIndex].value}}"
            frame-size="{{resolutionOptions[currentResolutionIndex].frameSize}}"
            binderror="handleCameraError"
            bindtouchstart="handleTouchStart"
            bindtouchend="handleTouchEnd"
            flash="off"
            mode="normal"
          ></camera>
          
          <!-- CAM硬件相机图片显示 - 仅在CAM模式下显示 -->
          <view wx:if="{{camMode}}" class="cam-image-container">
            <!-- 双缓冲图片显示 -->
            <image 
              src="{{camImageUrl}}" 
              class="cam-image {{camCurrentBuffer === 'buffer1' ? 'cam-active' : 'cam-hidden'}}"
              mode="aspectFill"
              data-buffer="buffer1"
              bindtouchstart="handleTouchStart"
              bindtouchend="handleTouchEnd"
              binderror="handleCamImageError"
              bindload="handleCamImageLoad"
            ></image>
            <image 
              src="{{camNextImageUrl}}" 
              class="cam-image {{camCurrentBuffer === 'buffer2' ? 'cam-active' : 'cam-hidden'}}"
              mode="aspectFill"
              data-buffer="buffer2"
              bindtouchstart="handleTouchStart"
              bindtouchend="handleTouchEnd"
              binderror="handleCamImageError"
              bindload="handleCamImageLoad"
            ></image>
            
            <!-- CAM状态指示器 -->
            <view class="cam-status-indicator {{camStatus}}">
              <text wx:if="{{camStatus === 'connecting'}}">📡 连接中...</text>
              <text wx:elif="{{camStatus === 'connected'}}">📡 CAM已连接</text>
              <text wx:elif="{{camStatus === 'error'}}">❌ 连接失败</text>
            </view>
          </view>
        </view>
        
        <!-- 犬科视角 -->
        <canvas
          type="webgl"
          id="processCanvas"
          class="vision-view {{currentView === 'dog' ? 'active' : ''}}"
          bindtouchstart="handleTouchStart"
          bindtouchend="handleTouchEnd"
        ></canvas>
        
        <!-- 运动视觉提示已移除 -->
        
        <!-- 设备性能级别和跳帧信息显示 (已隐藏) -->
        <view class="performance-info" hidden="true">
          <view class="performance-column">
            <text class="performance-level">{{devicePerformance === 'high' ? '高性能' : (devicePerformance === 'medium' ? '中性能' : '节能模式')}}</text>
            <text class="fps-info">{{currentFPS}}fps</text>
          </view>
          <text class="frame-skip-info">跳帧:{{devicePerformance === 'low' ? '2' : (devicePerformance === 'medium' ? '1' : '0')}}</text>
          <text class="resolution-info">{{frame.width}}x{{frame.height}}</text>
          <text class="night-vision-text" wx:if="{{isLowLight}}">夜视增强</text>
        </view>
        
        <!-- 相机错误提示 -->
        <view class="camera-error-container" wx:if="{{cameraError}}">
          <view class="camera-error-box">
            <text class="camera-error-icon">📷❌</text>
            <text class="camera-error-text">{{cameraErrorMsg || '相机启动失败'}}</text>
            <text class="camera-error-tip">请检查相机权限或重启应用</text>
            
            <!-- 错误操作按钮区 -->
            <view class="camera-error-actions">
              <!-- 重试按钮 -->
              <view class="camera-action-btn retry-btn" bindtap="retryCameraInit">
                <text class="action-btn-icon">🔄</text>
                <text class="action-btn-text">重试</text>
              </view>
              
              <!-- 设置按钮，仅在权限错误时显示 -->
              <view class="camera-action-btn setting-btn" bindtap="openCameraSetting" wx:if="{{showCameraSettingBtn}}">
                <text class="action-btn-icon">⚙️</text>
                <text class="action-btn-text">前往设置</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 相机加载提示 -->
        <view class="camera-loading-container {{cameraLoading && !cameraError && !camMode ? 'show' : ''}}">
          <view class="camera-loading-box">
            <view class="loading-ring"></view>
            <view class="loading-dot"></view>
            <view class="camera-loading-text">相机加载中</view>
            <view class="loading-dots">
              <text class="dot">.</text>
              <text class="dot">.</text>
              <text class="dot">.</text>
            </view>
          </view>
        </view>
        
        <!-- CAM硬件相机加载提示 -->
        <view class="cam-loading-container {{camIsLoading && camMode ? 'show' : ''}}">
          <view class="cam-loading-box">
            <view class="loading-ring cam-loading-ring"></view>
            <view class="loading-dot cam-loading-dot"></view>
            <view class="cam-loading-text">CAM连接中</view>
            <view class="loading-dots">
              <text class="dot">.</text>
              <text class="dot">.</text>
              <text class="dot">.</text>
            </view>
            <view class="cam-loading-desc">正在连接硬件相机...</view>
          </view>
        </view>
        
        <!-- CAM硬件相机错误提示 -->
        <view class="cam-error-container" wx:if="{{camStatus === 'error' && camMode}}">
          <view class="cam-error-box">
            <text class="cam-error-icon">📡❌</text>
            <text class="cam-error-text">{{camErrorMsg || 'CAM连接失败'}}</text>
            <text class="cam-error-tip">请检查网络连接或硬件状态</text>
            
            <!-- CAM错误操作按钮区 -->
            <view class="cam-error-actions">
              <!-- 重试按钮 -->
              <view class="cam-action-btn retry-btn" bindtap="retryCamConnection">
                <text class="action-btn-icon">🔄</text>
                <text class="action-btn-text">重试连接</text>
              </view>
              
              <!-- 退回手机相机按钮 -->
              <view class="cam-action-btn back-btn" bindtap="disableCamMode">
                <text class="action-btn-icon">📱</text>
                <text class="action-btn-text">返回手机相机</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 人类视角提示 -->
        <view wx:if="{{currentView === 'human'}}" class="visual-angle-tip">
          <view class="visual-angle-info">
            <text wx:if="{{features.isCat}}">🐾 猫科水平视角约200°，比人类的180°更广</text>
            <text wx:else>🐶 犬科水平视角约240°，比人类的180°更广</text>
          </view>
        </view>
        
        <!-- 硬件相机模式提示 -->
        <view wx:if="{{camMode && currentView === 'dog'}}" class="cam-mode-tip">
          <view class="cam-mode-info">
            <text class="cam-mode-icon">📡</text>
            <text class="cam-mode-text">硬件相机模式</text>
            <text class="cam-mode-desc">暂无视觉处理功能，显示原始图像</text>
          </view>
        </view>
        
        <!-- 爱宠视角状态指示条组 -->
        <view wx:if="{{currentView === 'dog'}}" class="pet-vision-status-group">
          <!-- 1. 二色视觉状态指示条 - 任何模式都高亮 -->
          <view class="vision-status-item color-vision active">
            <view class="vision-status-icon">🎨</view>
            <view class="vision-status-text">二色视觉</view>
          </view>
          
          <!-- 2. 视力辨析状态指示条 - acuity、nightVision、motionVision模式下高亮 -->
          <view class="vision-status-item acuity-vision {{currentVisionMode === 'acuity' || currentVisionMode === 'nightVision' || currentVisionMode === 'motionVision' ? 'active' : ''}}">
            <view class="vision-status-icon">👓</view>
            <view class="vision-status-text">视力辨析</view>
          </view>
          
          <!-- 3. 夜视/强光/正常模式状态指示条 - nightVision、motionVision模式下高亮 -->
          <view class="vision-status-item light-vision {{isLowLight ? 'low-light' : (isBrightLight ? 'bright-light' : 'normal-light')}} {{currentVisionMode === 'nightVision' || currentVisionMode === 'motionVision' ? 'active' : ''}}">
            <view class="vision-status-icon">
              <text wx:if="{{isLowLight}}">🌙</text>
              <text wx:elif="{{isBrightLight}}">😎</text>
              <text wx:else>☀️</text>
            </view>
            <view class="vision-status-text">
              <text wx:if="{{isLowLight}}">夜视模式</text>
              <text wx:elif="{{isBrightLight}}">强光模式</text>
              <text wx:else>光感模式</text>
            </view>
          </view>
          
          <!-- 4. 运动视觉状态指示条 - 只有motionVision模式下高亮 -->
          <view class="vision-status-item motion-vision {{currentVisionMode === 'motionVision' ? 'active' : ''}}">
            <view class="vision-status-icon">⚡</view>
            <view class="vision-status-text">运动视觉</view>
          </view>
        </view>
        
        <!-- iOS风格半透明浮动控制栏 - 现在是垂直排列 -->
        <view class="ios-control-bar {{showControls ? 'visible' : 'hidden'}}">
          <!-- 视角切换按钮 - 区分猫科和犬科图标 -->
          <view class="ios-btn-container">
            <view class="ios-btn" hover-class="ios-btn-hover" bindtap="toggleView">
              <view class="ios-icon" wx:if="{{features.isCat}}">{{currentView === 'dog' ? '👁️' : '😺'}}</view>
              <view class="ios-icon" wx:else>{{currentView === 'dog' ? '👁️' : '🐶'}}</view>
            </view>
            <text class="ios-btn-label">切换视角</text>
          </view>
          
          <!-- 相机选择器按钮 -->
          <view class="ios-btn-container">
            <view class="ios-btn {{currentCameraType === 'cam' ? 'cam-active' : ''}}" hover-class="ios-btn-hover" bindtap="toggleCameraSelector">
              <view class="ios-icon">
                <text wx:if="{{currentCameraType === 'back'}}">📷</text>
                <text wx:elif="{{currentCameraType === 'front'}}">🤳</text>
                <text wx:else>📡</text>
              </view>
            </view>
            <text class="ios-btn-label">切换相机</text>
          </view>
          
          <!-- 保存图片按钮 -->
          <view class="ios-btn-container" wx:if="{{currentView === 'dog'}}">
            <view class="ios-btn" hover-class="ios-btn-hover" bindtap="savePetVisionImage">
              <view class="ios-icon">📷</view>
            </view>
            <text class="ios-btn-label">保存图片</text>
          </view>
          
          <!-- 视觉模式按钮 -->
          <view class="ios-btn-container" wx:if="{{currentView === 'dog'}}">
            <view class="ios-btn" hover-class="ios-btn-hover" bindtap="toggleVisionModeSelector">
              <view class="ios-icon">🎭</view>
            </view>
            <text class="ios-btn-label">爱宠视角</text>
          </view>
          
          <!-- 参数设置按钮 -->
          <view class="ios-btn-container" wx:if="{{currentView === 'dog'}}">
            <view class="ios-btn" hover-class="ios-btn-hover" bindtap="toggleVisionParams">
              <view class="ios-icon">⚙️</view>
            </view>
            <text class="ios-btn-label">参数设置</text>
          </view>
        </view>
        
        <!-- 分辨率选择器 -->
        <view class="resolution-selector {{showResolutionSelector ? 'show' : ''}}">
          <view class="resolution-header">
            <text class="resolution-title">分辨率设置</text>
            <view class="close-btn" bindtap="toggleResolutionSelector">×</view>
          </view>
          <view class="resolution-options">
            <view wx:for="{{resolutionOptions}}" wx:key="index" 
                  class="resolution-option {{currentResolutionIndex === index ? 'selected' : ''}}"
                  data-index="{{index}}" bindtap="changeResolution">
              <text>{{item.name}}</text>
              <text class="resolution-desc">{{item.width}}x{{item.height}}</text>
            </view>
          </view>
          <view class="resolution-note">
            <text>请根据设备性能选择合适的分辨率，高分辨率可能影响性能</text>
          </view>
        </view>
        
        <!-- 视觉模式选择器 - 重新布局 -->
        <view class="circular-vision-selector {{showVisionModeSelector ? 'show' : ''}} {{selectorFadeOut ? 'fade-out' : ''}}" catch:touchmove="preventTouchMove">
          <view class="circular-selector-backdrop" bindtap="toggleVisionModeSelector"></view>
          
          <view class="circular-menu-container">
            <!-- 重新布局的功能模块网格 -->
            <view class="circular-menu-items improved-layout">
              <!-- 二色视觉 - 基础层 -->
              <view class="circular-menu-item {{currentVisionMode === 'dichromatic' ? 'selected' : ''}} level-1" 
                    data-mode="dichromatic" bindtap="selectVisionMode">
                <view class="circular-item-icon">
                  <text>🎨</text>
                </view>
                <view class="circular-item-label">
                  <text>二色视觉</text>
                </view>
                <view class="level-badge">1</view>
              </view>
              
              <!-- 视力辨析度 - 第二层 -->
              <view class="circular-menu-item {{currentVisionMode === 'acuity' ? 'selected' : ''}} level-2" 
                    data-mode="acuity" bindtap="selectVisionMode">
                <view class="circular-item-icon">
                  <text>👓</text>
                </view>
                <view class="circular-item-label">
                  <text>视力辨析</text>
                </view>
                <view class="level-badge">2</view>
              </view>
              
              <!-- 夜视能力 - 第三层 -->
              <view class="circular-menu-item {{currentVisionMode === 'nightVision' ? 'selected' : ''}} level-3" 
                    data-mode="nightVision" bindtap="selectVisionMode">
                <view class="circular-item-icon">
                  <text>🌙</text>
                </view>
                <view class="circular-item-label">
                  <text>光感模式</text>
                </view>
                <view class="level-badge">3</view>
              </view>
              
              <!-- 运动视觉 - 最高层 -->
              <view class="circular-menu-item {{currentVisionMode === 'motionVision' || features.motion ? 'selected' : ''}} level-4" 
                    data-mode="motionVision" bindtap="selectVisionMode">
                <view class="circular-item-icon">
                  <text>⚡</text>
                </view>
                <view class="circular-item-label">
                  <text>运动视觉</text>
                </view>
                <view class="level-badge">4</view>
              </view>
            </view>
            
            <!-- 当前选择的模式提示 -->
            <view class="mode-selected-tip mode-tip-1" wx:if="{{currentVisionMode === 'dichromatic'}}">
              <text>已选择二色视觉（第1层）</text>
            </view>
            <view class="mode-selected-tip mode-tip-2" wx:if="{{currentVisionMode === 'acuity'}}">
              <text>已选择视力辨析（第2层）</text>
            </view>
            <view class="mode-selected-tip mode-tip-3" wx:if="{{currentVisionMode === 'nightVision'}}">
              <text>已选择光感模式（第3层）</text>
            </view>
            <view class="mode-selected-tip mode-tip-4" wx:if="{{currentVisionMode === 'motionVision'}}">
              <text>已选择运动视觉（第4层）</text>
            </view>
          </view>
        </view>

        <!-- 旧版弧形菜单可以保留但设为隐藏 -->
        <view class="arc-vision-selector" style="display: none;">
          <!-- 原有的弧形菜单内容 -->
        </view>
      </view>
    </view>
    
    <!-- 视觉分析区域 - 可展开版 -->
    <view class="analysis-container {{isAnalysisExpanded ? 'expanded' : ''}}" style="background: {{themeStyle.backgroundDark || 'var(--dark-bg)'}};">      
      <!-- 拖动条 - 中心对称设计 -->
      <view class="analysis-drag-handle" bindtap="toggleAnalysisExpand">
        <view class="analysis-drag-indicator"></view>
        <text class="drag-text">{{isAnalysisExpanded ? '收起' : '展开'}}知识库</text>
        <view class="analysis-drag-indicator"></view>
      </view>
      <!-- 标签页切换 -->
      <view class="tabs-container" style="border-color: {{themeStyle.cardBg || 'var(--card-bg)'}};">
        <view 
          class="tab {{currentTab === 0 ? 'active-tab' : ''}} {{isLightTheme ? 'light-theme-text' : ''}}" 
          data-index="0" 
          bindtap="changeTab"
        >眼部构造</view>
        <view 
          class="tab {{currentTab === 1 ? 'active-tab' : ''}} {{isLightTheme ? 'light-theme-text' : ''}}" 
          data-index="1" 
          bindtap="changeTab"
        >爱宠视角</view>
        <view 
          class="tab {{currentTab === 2 ? 'active-tab' : ''}} {{isLightTheme ? 'light-theme-text' : ''}}" 
          data-index="2" 
          bindtap="changeTab"
        >眼部健康</view>
      </view>
      
      <!-- 内容区域 -->
      <view class="tab-content" style="background: {{themeStyle.cardBg || 'var(--card-bg)'}};">
        <!-- 视觉特征 -->
        <view class="content-section" hidden="{{currentTab !== 0}}">
          <scroll-view scroll-y class="tab-scroll-view">
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">👁️</text>
                <text>眼睛特征</text>
              </view>
              <view class="sense-desc">
                <view class="feature-row">
                  <text class="feature-label">颜色:</text>
                  <text class="feature-value">{{visionTabs.basicFeatures.eyeColor}}</text>
                </view>
                <view class="feature-row">
                  <text class="feature-label">形状:</text>
                  <text class="feature-value">{{visionTabs.basicFeatures.eyeShape}}</text>
                </view>
                <view class="feature-extension" wx:if="{{visionTabs.basicFeatures.eyeExtension}}">
                  <text class="extension-text">{{visionTabs.basicFeatures.eyeExtension}}</text>
                </view>
              </view>
            </view>
            
            <!-- 第三眼睑卡片 -->
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">🛡️</text>
                <text>第三眼睑</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.basicFeatures.thirdEyelid}}</text>
              </view>
            </view>
          </scroll-view>
        </view>
        
        <!-- 感知能力 -->
        <view class="content-section" hidden="{{currentTab != 1}}">
          <scroll-view scroll-y class="tab-scroll-view">
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">🎨</text>
                <text>二色视觉</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.coreFeatures.colorSystem}}</text>
              </view>
            </view>
            
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">📐</text>
                <text>视场角度</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.coreFeatures.visualAngle}}</text>
              </view>
            </view>

            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">👓</text>
                <text>视力辨析</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.coreFeatures.visualAcuity}}</text>
              </view>
            </view>
            
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">🌙</text>
                <text>光感模式</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.coreFeatures.nightVision}}</text>
              </view>
            </view>
            
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">⚡</text>
                <text>运动视觉</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.coreFeatures.motionPerception}}</text>
              </view>
            </view>
          </scroll-view>
        </view>
        
        <!-- 爱宠护理 -->
        <view class="content-section" hidden="{{currentTab !== 2}}">
          <scroll-view scroll-y class="tab-scroll-view">
            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">💧</text>
                <text>眼睛泪痕</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.otherFeatures.tearStains}}</text>
              </view>
            </view>

            <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">🩺</text>
                <text>常见眼疾</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.otherFeatures.commonEyeDiseases}}</text>
              </view>
            </view>
            
            <!-- 模拟精度标签页已隐藏 -->
            <!-- <view class="sense-card" hover-class="card-hover">
              <view class="sense-title">
                <text class="sense-icon">ℹ️</text>
                <text>模拟精度</text>
              </view>
              <view class="sense-desc">
                <text>{{visionTabs.otherFeatures.simulationAccuracy}}</text>
                <view class="warning-text">
                  温馨提醒：本程序只关注爱宠的眼睛，至于听力、嘴觉等感官不做模拟。未来版本我们期待能为您带来更全面的感官模拟体验。
                </view>
              </view>
            </view> -->
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 视觉参数调整面板 -->
    <view class="vision-params-panel {{showVisionParams ? 'show' : ''}}" wx:if="{{currentView === 'dog'}}">
      <view class="params-header">
        <text class="params-title">视觉参数调整</text>
        <view class="params-close" bindtap="toggleVisionParams">×</view>
      </view>
      
      <!-- 视力辨析度参数 - 新增部分 -->
      <view class="param-section">
        <text class="section-title">👓 视力辨析度</text>
        <view class="param-item">
          <text class="param-label">视力清晰度 ({{dogVisionParams.resolutionFactor}})</text>
          <slider min="0.2" max="1" step="0.05" value="{{dogVisionParams.resolutionFactor}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onResolutionChange" bindchange="onResolutionChange"/>
          <text class="param-desc">调整犬猫视力的清晰度，值越大图像越清晰。犬科视力约为人类的1/4-1/5，模拟其对细节的辨识能力较弱。</text>
        </view>
        
        <view class="param-item">
          <text class="param-label">视野因子 ({{dogVisionParams.antiAliasFactor}})</text>
          <slider min="0.2" max="3.0" step="0.1" value="{{dogVisionParams.antiAliasFactor}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onAntiAliasChange" bindchange="onAntiAliasChange"/>
          <text class="param-desc">调整视野范围内外的清晰度差异，值越大边缘越模糊。小值(0.2)时整个视野清晰，大值(3.0)时只有中心清晰。建议在不同场景下测试极端值。</text>
        </view>
      </view>
      
      <!-- 夜视能力参数 -->
      <view class="param-section">
        <text class="section-title">🌙 夜视能力</text>
        <view class="param-item">
          <text class="param-label">亮度增强 ({{dogVisionParams.brightness}})</text>
          <slider min="1" max="3" step="0.1" value="{{dogVisionParams.brightness}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onBrightnessChange" bindchange="onBrightnessChange"/>
          <text class="param-desc">模拟犬科动物在低光环境中的视觉增强能力。值越大，夜视增强效果越明显，哈士奇等犬种默认值较高。</text>
        </view>
        
        <view class="param-item">
          <text class="param-label">对比度 ({{dogVisionParams.contrast}})</text>
          <slider min="1" max="2" step="0.1" value="{{dogVisionParams.contrast}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onContrastChange" bindchange="onContrastChange"/>
          <text class="param-desc">调整低光环境下的对比度增强。值越大，物体轮廓越清晰，但可能会导致细节丢失。</text>
        </view>
      </view>
      
      <!-- 运动视觉参数 -->
      <view class="param-section">
        <text class="section-title">👁️‍🗨️ 运动视觉</text>
        <view class="param-item">
          <text class="param-label">运动敏感度 ({{dogVisionParams.motionSensitivity}})</text>
          <slider min="0" max="5" step="0.1" value="{{dogVisionParams.motionSensitivity}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onMotionSensitivityChange" bindchange="onMotionSensitivityChange"/>
          <text class="param-desc">调整运动检测的敏感度。值越大，越容易检测到轻微的运动。猫科动物默认值较高。建议在1-3之间进行测试。</text>
        </view>
        
        <view class="param-item">
          <text class="param-label">运动阈值 ({{dogVisionParams.motionThreshold}})</text>
          <slider min="5" max="50" step="1" value="{{dogVisionParams.motionThreshold}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onMotionThresholdChange" bindchange="onMotionThresholdChange"/>
          <text class="param-desc">调整运动检测的阈值。值越小，越容易检测到轻微的运动。猫科动物默认值较低。</text>
        </view>
        
        <view class="param-item">
          <text class="param-label">物体大小阈值 ({{dogVisionParams.motionSizeThreshold}})</text>
          <slider min="1" max="50" step="1" value="{{dogVisionParams.motionSizeThreshold}}" 
                  activeColor="var(--primary-color)" backgroundColor="#444" block-size="24" 
                  bindchanging="onMotionSizeThresholdChange" bindchange="onMotionSizeThresholdChange"/>
          <text class="param-desc">调整运动物体的大小阈值。值越小，越容易检测到小物体的运动。猫科动物默认值较低。建议在10-50之间进行测试。</text>
        </view>
      </view>
      
      <!-- 重置按钮 -->
      <view class="param-item">
        <button class="reset-btn" bindtap="resetVisionParams">重置为默认值</button>
        <text class="reset-desc">将所有参数重置为当前犬种的默认设置</text>
      </view>
    </view>

    <!-- 相机选择器面板 -->
    <view class="camera-selector-overlay {{showCameraSelector ? 'show' : ''}}" bindtap="toggleCameraSelector">
      <view class="camera-selector-panel" catchtap="preventBubble">
        <view class="selector-header">
          <text class="selector-title">选择相机</text>
          <text class="selector-close" bindtap="toggleCameraSelector">×</text>
        </view>
        
        <view class="camera-options">
          <view 
            wx:for="{{cameraOptions}}" 
            wx:key="type"
            class="camera-option {{currentCameraType === item.type ? 'active' : ''}} {{!item.available ? 'disabled' : ''}}"
            data-type="{{item.type}}"
            bindtap="selectCameraType"
          >
            <view class="option-icon">{{item.icon}}</view>
            <view class="option-content">
              <text class="option-name">{{item.name}}</text>
              <text class="option-desc">{{item.desc}}</text>
              <!-- 硬件相机特殊说明 -->
              <text wx:if="{{item.type === 'cam'}}" class="option-note">暂无图像处理功能</text>
            </view>
            <view class="option-status">
              <text wx:if="{{currentCameraType === item.type}}" class="status-selected">✓</text>
              <text wx:elif="{{item.type === 'cam' && camStatus === 'connected'}}" class="status-connected">●</text>
            </view>
          </view>
        </view>
        
        <view class="selector-footer">
          <text class="selector-tip">点击选择要使用的相机设备</text>
        </view>
      </view>
    </view>

    <!-- 视觉功能层级引导 -->
    <view class="vision-hierarchy-guide {{showVisionGuide ? 'show' : ''}}" bindtap="closeVisionGuide">
      <view class="guide-content" catchtap="preventBubble">
        <view class="guide-header">
          <text class="guide-title">视觉功能层级</text>
          <text class="guide-close" bindtap="closeVisionGuide">×</text>
        </view>
        
        <view class="guide-body">
          <view class="hierarchy-diagram">
            <view class="hierarchy-level level-1">
              <view class="level-icon">🎨</view>
              <view class="level-info">
                <text class="level-name">二色视觉</text>
                <text class="level-desc">基础层</text>
              </view>
            </view>
            
            <view class="hierarchy-arrow">↓</view>
            
            <view class="hierarchy-level level-2">
              <view class="level-icon">👓</view>
              <view class="level-info">
                <text class="level-name">视力辨析</text>
                <text class="level-desc">包含二色视觉</text>
              </view>
            </view>
            
            <view class="hierarchy-arrow">↓</view>
            
            <view class="hierarchy-level level-3">
              <view class="level-icon">🌙</view>
              <view class="level-info">
                <text class="level-name">夜视能力</text>
                <text class="level-desc">包含二色视觉和视力辨析</text>
              </view>
            </view>
            
            <view class="hierarchy-arrow">↓</view>
            
            <view class="hierarchy-level level-4">
              <view class="level-icon">⚡</view>
              <view class="level-info">
                <text class="level-name">运动视觉</text>
                <text class="level-desc">包含所有基础功能</text>
              </view>
            </view>
          </view>
          
          <view class="guide-tip">
            <text>提示: 选择高级功能会自动包含前面的基础功能</text>
          </view>
        </view>
        
        <view class="guide-footer">
          <button class="guide-btn" bindtap="closeVisionGuide">我明白了</button>
          <view class="guide-remember">
            <checkbox value="{{rememberVisionGuide}}" bindtap="toggleRememberGuide"/>
            <text>不再显示</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 运动视觉按钮引导提示 -->
    <view class="motion-guide-tip {{showMotionGuideTip ? 'guide-tip-show' : ''}}">
      <view class="guide-tip-content">
        <view class="guide-tip-arrow"></view>
        <text class="guide-tip-text">点击这里体验完整的运动视觉功能</text>
      </view>
    </view>
  </view>
</view>