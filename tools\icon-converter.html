<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标转换工具</title>
    <style>
        body {
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f7f7f7;
            color: #333;
        }
        h1 {
            color: #FF8A5B;
            text-align: center;
        }
        .instruction {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .icon-area {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: calc(50% - 40px);
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 120px;
            margin-bottom: 20px;
            background: #f0f0f0;
            border-radius: 8px;
        }
        button {
            background-color: #FF8A5B;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 10px;
        }
        button:hover {
            background-color: #ff7a43;
        }
        code {
            display: block;
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tab-button {
            background-color: #eee;
            color: #333;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .tab-button.active {
            background-color: #FF8A5B;
            color: white;
        }
    </style>
</head>
<body>
    <h1>爱宠看世界 - 图标转换工具</h1>
    
    <div class="instruction">
        <h2>使用说明</h2>
        <p>此工具用于将SVG图标转换为微信小程序的tabBar使用的PNG图标。</p>
        <p>步骤：</p>
        <ol>
            <li>查看下方预设的SVG图标</li>
            <li>点击"转换为PNG"按钮生成PNG版本</li>
            <li>右键点击生成的PNG图像并保存</li>
            <li>将保存的图像文件放入小程序项目的<code>images/icons</code>目录中</li>
        </ol>
    </div>

    <div class="tab-buttons">
        <button class="tab-button active" onclick="showTab('home')">首页图标</button>
        <button class="tab-button" onclick="showTab('profile')">个人中心图标</button>
    </div>

    <div id="home-tab">
        <div class="icon-area">
            <div class="icon-container">
                <h3>首页图标 (默认状态)</h3>
                <div class="icon-preview" id="home-default-preview">
                    <svg width="48px" height="48px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <title>首页图标</title>
                        <g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <path d="M12,2.5 L3,9.5 L3,21 C3,21.5522847 3.44771525,22 4,22 L20,22 C20.5522847,22 21,21.5522847 21,21 L21,9.5 L12,2.5 Z" id="房子轮廓" stroke="#999999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9,22 L9,16 C9,14.8954305 9.8954305,14 11,14 L13,14 C14.1045695,14 15,14.8954305 15,16 L15,22" id="门" stroke="#999999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9,11 L9,11 C9.55228475,11 10,11.4477153 10,12 L10,12 C10,12.5522847 9.55228475,13 9,13 L9,13 C8.44771525,13 8,12.5522847 8,12 L8,12 C8,11.4477153 8.44771525,11 9,11 Z" id="窗户1" fill="#999999"></path>
                            <path d="M15,11 L15,11 C15.5522847,11 16,11.4477153 16,12 L16,12 C16,12.5522847 15.5522847,13 15,13 L15,13 C14.4477153,13 14,12.5522847 14,12 L14,12 C14,11.4477153 14.4477153,11 15,11 Z" id="窗户2" fill="#999999"></path>
                        </g>
                    </svg>
                </div>
                <div id="home-default-png"></div>
                <button onclick="convertToPng('home-default-preview', 'home-default-png', 'home.png')">转换为PNG</button>
            </div>
            
            <div class="icon-container">
                <h3>首页图标 (激活状态)</h3>
                <div class="icon-preview" id="home-active-preview">
                    <svg width="48px" height="48px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <title>首页图标-激活</title>
                        <g id="首页-激活" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <path d="M12,2.5 L3,9.5 L3,21 C3,21.5522847 3.44771525,22 4,22 L20,22 C20.5522847,22 21,21.5522847 21,21 L21,9.5 L12,2.5 Z" id="房子轮廓" stroke="#FF8A5B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9,22 L9,16 C9,14.8954305 9.8954305,14 11,14 L13,14 C14.1045695,14 15,14.8954305 15,16 L15,22" id="门" stroke="#FF8A5B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M9,11 L9,11 C9.55228475,11 10,11.4477153 10,12 L10,12 C10,12.5522847 9.55228475,13 9,13 L9,13 C8.44771525,13 8,12.5522847 8,12 L8,12 C8,11.4477153 8.44771525,11 9,11 Z" id="窗户1" fill="#FF8A5B"></path>
                            <path d="M15,11 L15,11 C15.5522847,11 16,11.4477153 16,12 L16,12 C16,12.5522847 15.5522847,13 15,13 L15,13 C14.4477153,13 14,12.5522847 14,12 L14,12 C14,11.4477153 14.4477153,11 15,11 Z" id="窗户2" fill="#FF8A5B"></path>
                        </g>
                    </svg>
                </div>
                <div id="home-active-png"></div>
                <button onclick="convertToPng('home-active-preview', 'home-active-png', 'home-active.png')">转换为PNG</button>
            </div>
        </div>
    </div>

    <div id="profile-tab" style="display: none;">
        <div class="icon-area">
            <div class="icon-container">
                <h3>个人中心图标 (默认状态)</h3>
                <div class="icon-preview" id="profile-default-preview">
                    <svg width="48px" height="48px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <title>个人中心图标</title>
                        <g id="个人中心" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <!-- 头像圆圈 -->
                            <circle id="头像背景" stroke="#999999" stroke-width="1.5" cx="12" cy="8" r="4.5"></circle>
                            <!-- 身体部分 -->
                            <path d="M4,20 C4,16.6862915 7.58172178,14 12,14 C16.4182782,14 20,16.6862915 20,20 L20,22 L4,22 L4,20 Z" id="身体" stroke="#999999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <!-- 头像内部 - 简化的人头轮廓 -->
                            <circle id="头部" fill="#999999" cx="12" cy="7" r="1.5"></circle>
                            <path d="M10.5,9 C10.5,9 11,10.5 12,10.5 C13,10.5 13.5,9 13.5,9" id="笑脸" stroke="#999999" stroke-width="0.75" stroke-linecap="round"></path>
                        </g>
                    </svg>
                </div>
                <div id="profile-default-png"></div>
                <button onclick="convertToPng('profile-default-preview', 'profile-default-png', 'profile.png')">转换为PNG</button>
            </div>
            
            <div class="icon-container">
                <h3>个人中心图标 (激活状态)</h3>
                <div class="icon-preview" id="profile-active-preview">
                    <svg width="48px" height="48px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <title>个人中心图标-激活</title>
                        <g id="个人中心-激活" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <!-- 头像圆圈 -->
                            <circle id="头像背景" stroke="#FF8A5B" stroke-width="1.5" cx="12" cy="8" r="4.5"></circle>
                            <!-- 身体部分 -->
                            <path d="M4,20 C4,16.6862915 7.58172178,14 12,14 C16.4182782,14 20,16.6862915 20,20 L20,22 L4,22 L4,20 Z" id="身体" stroke="#FF8A5B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <!-- 头像内部 - 简化的人头轮廓 -->
                            <circle id="头部" fill="#FF8A5B" cx="12" cy="7" r="1.5"></circle>
                            <path d="M10.5,9 C10.5,9 11,10.5 12,10.5 C13,10.5 13.5,9 13.5,9" id="笑脸" stroke="#FF8A5B" stroke-width="0.75" stroke-linecap="round"></path>
                        </g>
                    </svg>
                </div>
                <div id="profile-active-png"></div>
                <button onclick="convertToPng('profile-active-preview', 'profile-active-png', 'profile-active.png')">转换为PNG</button>
            </div>
        </div>
    </div>

    <div class="instruction">
        <h2>替换步骤</h2>
        <p>1. 在微信开发者工具中，将生成的PNG图片放入项目的<code>images/icons</code>目录</p>
        <p>2. 确保图片名称为：<code>home.png</code>, <code>home-active.png</code>, <code>profile.png</code>, <code>profile-active.png</code></p>
        <p>3. 确认app.json配置文件中使用了正确的图标路径：</p>
        <code>"tabBar": {
  "color": "#999999",
  "selectedColor": "#FF8A5B",
  "backgroundColor": "#ffffff",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/index/index",
      "text": "首页",
      "iconPath": "images/icons/home.png",
      "selectedIconPath": "images/icons/home-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "个人中心",
      "iconPath": "images/icons/profile.png",
      "selectedIconPath": "images/icons/profile-active.png"
    }
  ]
}</code>
    </div>

    <script>
        function showTab(tab) {
            // 隐藏所有标签页
            document.getElementById('home-tab').style.display = 'none';
            document.getElementById('profile-tab').style.display = 'none';

            // 显示选中的标签页
            document.getElementById(tab + '-tab').style.display = 'block';

            // 更新按钮样式
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(button => {
                button.classList.remove('active');
            });

            // 设置当前按钮为激活状态
            event.target.classList.add('active');
        }

        function convertToPng(previewId, targetId, filename) {
            const svgElement = document.getElementById(previewId).querySelector('svg');
            const canvas = document.createElement('canvas');
            canvas.width = 64;  // 设置适合小程序的尺寸
            canvas.height = 64;

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 创建Image对象
            const img = new Image();
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);

            img.onload = function() {
                // 在canvas上绘制图像
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                URL.revokeObjectURL(url);

                // 创建下载链接
                const pngUrl = canvas.toDataURL('image/png');
                const downloadLink = document.createElement('a');
                downloadLink.href = pngUrl;
                downloadLink.download = filename;
                downloadLink.innerHTML = `<img src="${pngUrl}" alt="${filename}" style="width:48px;height:48px;margin:10px;"> <br>右键点击图片并选择"另存为"`;
                
                const targetElement = document.getElementById(targetId);
                targetElement.innerHTML = '';
                targetElement.appendChild(downloadLink);
            };

            img.src = url;
        }
    </script>
</body>
</html> 